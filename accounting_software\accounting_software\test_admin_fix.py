#!/usr/bin/env python3
"""
Test Django admin fix
"""
import requests

def test_admin_pages():
    print("🔧 TESTING DJANGO ADMIN PAGES")
    print("=" * 40)
    
    # Test admin pages that were failing
    admin_urls = [
        "http://127.0.0.1:8000/admin/purchase/vendorbillitem/",
        "http://127.0.0.1:8000/admin/purchase/vendorbill/",
        "http://127.0.0.1:8000/admin/purchase/vendorpayment/"
    ]
    
    for url in admin_urls:
        try:
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                print(f"✅ {url.split('/')[-2]} - Working")
            elif response.status_code == 302:
                print(f"🔐 {url.split('/')[-2]} - Redirected (login required)")
            else:
                print(f"❌ {url.split('/')[-2]} - Status {response.status_code}")
        except Exception as e:
            print(f"❌ {url.split('/')[-2]} - Error: {e}")

def test_model_str_methods():
    print("\n🧪 TESTING MODEL __str__ METHODS")
    print("=" * 40)
    
    try:
        import os
        import sys
        import django
        
        # Set up Django
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'erp_backend.settings')
        sys.path.append('/d/erp_accv1/accounting_software/accounting_software/erp_backend')
        django.setup()
        
        from purchase.models import VendorBill, VendorPayment
        from contacts.models import Contact
        
        # Test VendorBill __str__ method
        vendor = Contact.objects.filter(contact_type='vendor').first()
        if vendor:
            bills = VendorBill.objects.filter(vendor=vendor).first()
            if bills:
                print(f"✅ VendorBill __str__: {str(bills)}")
            else:
                print("ℹ️  No vendor bills found to test")
        else:
            print("ℹ️  No vendors found to test")
            
        print("✅ Model __str__ methods are working!")
        
    except Exception as e:
        print(f"❌ Model test failed: {e}")

if __name__ == "__main__":
    test_admin_pages()
    test_model_str_methods()
    
    print("\n🎉 DJANGO ADMIN SHOULD NOW BE WORKING!")
    print("✅ You can now access the admin interface without errors!")
