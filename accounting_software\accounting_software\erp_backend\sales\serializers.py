from rest_framework import serializers
from .models import ProductCategory, Product


class ProductCategorySerializer(serializers.ModelSerializer):
    """Serializer for ProductCategory model"""
    
    subcategories_count = serializers.SerializerMethodField()
    products_count = serializers.SerializerMethodField()
    full_path = serializers.SerializerMethodField()
    
    class Meta:
        model = ProductCategory
        fields = '__all__'
        read_only_fields = ('category_id', 'created_at', 'updated_at', 'created_by')
    
    def get_subcategories_count(self, obj):
        """Get count of subcategories"""
        return obj.subcategories.filter(is_active=True).count()
    
    def get_products_count(self, obj):
        """Get count of products in this category"""
        return obj.products.filter(status='active').count()
    
    def get_full_path(self, obj):
        """Get full category path"""
        return obj.get_full_path()


class ProductSerializer(serializers.ModelSerializer):
    """Serializer for Product model"""
    
    category_name = serializers.CharField(source='category.name', read_only=True)
    margin_amount = serializers.DecimalField(max_digits=15, decimal_places=2, read_only=True)
    margin_percentage = serializers.DecimalField(max_digits=5, decimal_places=2, read_only=True)
    markup_percentage = serializers.DecimalField(max_digits=5, decimal_places=2, read_only=True)
    total_quantity_on_hand = serializers.SerializerMethodField()
    
    class Meta:
        model = Product
        fields = '__all__'
        read_only_fields = (
            'product_id', 'margin_amount', 'margin_percentage', 'markup_percentage',
            'price_last_updated_at', 'price_last_updated_by', 'cost_last_updated_at', 
            'cost_last_updated_by', 'created_at', 'updated_at', 'created_by'
        )
    
    def get_total_quantity_on_hand(self, obj):
        """Get total quantity from all warehouses"""
        try:
            from inventory.models import Inventory
            from django.db import models
            total_qty = Inventory.objects.filter(product=obj).aggregate(
                total=models.Sum('quantity_on_hand')
            )['total'] or 0
            return float(total_qty)
        except:
            return float(obj.quantity_on_hand)
    
    def create(self, validated_data):
        """Create product with user tracking"""
        request = self.context.get('request')
        if request and hasattr(request, 'user'):
            validated_data['created_by'] = request.user
        return super().create(validated_data)
    
    def update(self, instance, validated_data):
        """Update product with price change tracking"""
        request = self.context.get('request')
        
        # Track price changes
        if 'unit_price' in validated_data and validated_data['unit_price'] != instance.unit_price:
            if request and hasattr(request, 'user'):
                validated_data['price_last_updated_by'] = request.user
        
        if 'cost_price' in validated_data and validated_data['cost_price'] != instance.cost_price:
            if request and hasattr(request, 'user'):
                validated_data['cost_last_updated_by'] = request.user
        
        return super().update(instance, validated_data)
