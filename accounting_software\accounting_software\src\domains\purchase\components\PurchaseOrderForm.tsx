import React, { useState, useEffect } from 'react';
import {
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Grid,
  Typography,
  Box,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  IconButton,
  Autocomplete,
  Chip,
  Card,
  CardContent,
  FormHelperText,
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Receipt as ReceiptIcon,
  Business as BusinessIcon,
  Person as PersonIcon,
  LocalShipping as ShippingIcon,
  Description as DescriptionIcon,
  Close as CloseIcon,
} from '@mui/icons-material';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import dayjs from 'dayjs';
import { purchaseOrderService, PurchaseOrderFormData } from '../../../services/purchaseOrder.service';
import { vendorService, Vendor } from '../../../services/vendor.service';
import { useCurrencyInfo } from '../../gl/hooks/useCurrencyInfo';
// PurchaseProductContext removed - products will be loaded directly from API
import { salesTaxService, type SalesTaxOption } from '../../../services/sales-tax.service';
import {
  JournalLineTable,
  type JournalLineItem,
  type AccountOption,
  FormattedCurrencyInput,
  QuantityInput,
  StandardDatePicker,
} from '../../../shared/components';
import ConfirmationDialog from '../../../shared/components/ConfirmationDialog';
import { useConfirmation } from '../../../shared/hooks/useConfirmation';
import { usePaymentTerms } from '../../../contexts/PaymentTermsContext';

// Enhanced line item interface for PO
interface POLineItem extends JournalLineItem {
  product_id: string | number | null;
  product_name: string;
  quantity: number;
  unit_of_measure: string;
  unit_price: number;
  discount_percent: number;
  line_total: number;
}

// Units of Measure
interface UnitOfMeasure {
  id: string;
  name: string;
  abbreviation: string;
  type: 'weight' | 'volume' | 'count' | 'length' | 'area';
}

const COMMON_UNITS: UnitOfMeasure[] = [
  { id: 'pcs', name: 'Pieces', abbreviation: 'pcs', type: 'count' },
  { id: 'kg', name: 'Kilogram', abbreviation: 'kg', type: 'weight' },
  { id: 'g', name: 'Gram', abbreviation: 'g', type: 'weight' },
  { id: 'l', name: 'Liter', abbreviation: 'L', type: 'volume' },
  { id: 'ml', name: 'Milliliter', abbreviation: 'ml', type: 'volume' },
  { id: 'box', name: 'Box', abbreviation: 'box', type: 'count' },
  { id: 'pack', name: 'Pack', abbreviation: 'pack', type: 'count' },
  { id: 'dozen', name: 'Dozen', abbreviation: 'doz', type: 'count' },
  { id: 'm', name: 'Meter', abbreviation: 'm', type: 'length' },
  { id: 'cm', name: 'Centimeter', abbreviation: 'cm', type: 'length' },
];

// PO Status Options
const PO_STATUS_OPTIONS = [
  { id: 'draft', value: 'draft', label: 'Draft', color: 'default' },
  { id: 'pending', value: 'pending', label: 'Pending Approval', color: 'warning' },
  { id: 'approved', value: 'approved', label: 'Approved', color: 'info' },
  { id: 'sent', value: 'sent', label: 'Sent to Vendor', color: 'primary' },
  { id: 'acknowledged', value: 'acknowledged', label: 'Acknowledged', color: 'success' },
  { id: 'partial', value: 'partial', label: 'Partially Received', color: 'warning' },
  { id: 'received', value: 'received', label: 'Fully Received', color: 'success' },
  { id: 'closed', value: 'closed', label: 'Closed', color: 'default' },
  { id: 'cancelled', value: 'cancelled', label: 'Cancelled', color: 'error' },
];

interface PurchaseOrderFormProps {
  onClose: () => void;
  onSave: () => void;
  initialValues?: any;
  viewMode?: boolean;
}

const validationSchema = Yup.object({
  poDate: Yup.string().required('PO Date is required'),
  expectedDeliveryDate: Yup.string().required('Expected delivery date is required'),
  buyerName: Yup.string().required('Buyer name is required'),
});

const PurchaseOrderForm: React.FC<PurchaseOrderFormProps> = ({ onClose, onSave, initialValues, viewMode = false }) => {
  const { currencyInfo } = useCurrencyInfo();
  const { paymentTerms } = usePaymentTerms();
  // Products will be loaded directly from API when needed
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { confirmation, showSuccess, showError, hideConfirmation } = useConfirmation();
  const [expanded, setExpanded] = useState<string>('panel4');
  const [vendors, setVendors] = useState<Vendor[]>([]);
  const [loadingVendors, setLoadingVendors] = useState(false);
  const [salesTaxes, setSalesTaxes] = useState<SalesTaxOption[]>([]);
  const [loadingSalesTaxes, setLoadingSalesTaxes] = useState(false);
  const [lineItems, setLineItems] = useState<POLineItem[]>([]);
  const isEditMode = Boolean(initialValues?.id);

  // Auto-generate PO number for new orders
  const generatePONumber = () => {
    const randomNum = Math.floor(Math.random() * 999999) + 1;
    return `PO-${randomNum.toString().padStart(6, '0')}`;
  };

  // Create empty line item
  const createEmptyLineItem = (index: number): POLineItem => ({
    id: `line_${Date.now()}_${index}`,
    product_id: null,
    product_name: '',
    description: '',
    quantity: 1,
    unit_of_measure: 'pcs',
    unit_price: 0,
    discount_percent: 0,
    line_total: 0,
    amount: 0,
    account_id: null,
    account_name: '',
    sales_tax: null,
    sales_tax_description: '',
    sales_tax_rate: 0,
    sales_tax_amount: 0,
    taxable_amount: 0,
  });

  // Load vendors
  useEffect(() => {
    const loadVendorsData = async () => {
      try {
        setLoadingVendors(true);
        const vendorData = await vendorService.getAllVendors({ status: 'active' });
        console.log('✅ Loaded vendors:', vendorData);
        setVendors(Array.isArray(vendorData) ? vendorData : []);
      } catch (err) {
        console.error('Failed to load vendors:', err);
        setVendors([]);
      } finally {
        setLoadingVendors(false);
      }
    };
    loadVendorsData();
  }, []);

  // Load input taxes using salesTaxService
  useEffect(() => {
    const loadInputTaxes = async () => {
      try {
        setLoadingSalesTaxes(true);
        const inputTaxes = await salesTaxService.getInputTaxes();
        console.log('✅ Loaded input taxes:', inputTaxes);
        setSalesTaxes(inputTaxes);
      } catch (err) {
        console.error('Failed to load input taxes:', err);
        setSalesTaxes([]);
      } finally {
        setLoadingSalesTaxes(false);
      }
    };
    loadInputTaxes();
  }, []);

  // Initialize line items from initialValues
  useEffect(() => {
    console.log('Line items useEffect triggered. initialValues:', initialValues);
    if (initialValues?.lineItems && initialValues.lineItems.length > 0 && viewMode) {
      const transformedItems = initialValues.lineItems.map((item: any, index: number) => {
        const matchingSalesTax = salesTaxes.length > 0
          ? salesTaxes.find(tax =>
              Math.abs(tax.rate - parseFloat(item.sales_tax_rate || item.tax_rate || '0')) < 0.01
            )
          : null;

        const transformedItem = {
          ...createEmptyLineItem(index),
          id: item.id || `line_${Date.now()}_${index}`,
          product_id: item.product_id || item.account_id || null,
          product_name: item.product_name || item.account_name || item.description || '',
          description: item.description || '',
          quantity: parseFloat(item.quantity?.toString() || '0'),
          unit_of_measure: item.unit_of_measure || 'pcs',
          unit_price: parseFloat(item.unit_price?.toString() || '0'),
          discount_percent: parseFloat(item.discount_percent?.toString() || '0'),
          line_total: parseFloat(item.line_total?.toString() || '0'),
          taxable: Boolean(item.taxable),
          tax_rate: parseFloat(item.tax_rate || item.sales_tax_rate || '0'),
          sales_tax_amount: parseFloat(item.sales_tax_amount || item.tax_amount || '0'),
          sales_tax: matchingSalesTax ? matchingSalesTax.id : null,
          sales_tax_rate: parseFloat(item.tax_rate || item.sales_tax_rate || '0'),
          sales_tax_description: matchingSalesTax
            ? matchingSalesTax.description
            : item.sales_tax_description ||
              item.tax_description ||
              (parseFloat(item.tax_rate || item.sales_tax_rate || '0') > 0
                ? `Input Tax ${parseFloat(item.tax_rate || item.sales_tax_rate || '0')}%`
                : ''),
          notes: item.notes || '',
          account_id: item.product_id || item.account_id || 0,
          account_name: item.product_name || item.account_name || item.description || '',
          memo: '',
          amount: parseFloat(item.line_total?.toString() || '0'),
          debit_amount: 0,
          credit_amount: 0,
        };
        console.log(`Transformed line item ${index + 1}:`, transformedItem);
        return transformedItem;
      });
      setLineItems(transformedItems);
    } else {
      setLineItems([createEmptyLineItem(0)]);
    }
  }, [initialValues, viewMode]);

  // Products error handling removed since we're not using PurchaseProductContext

  const initialFormValues = {
    poNumber: initialValues?.poNumber || generatePONumber(),
    poDate: initialValues?.poDate || dayjs().format('YYYY-MM-DD'),
    expectedDeliveryDate: initialValues?.expectedDeliveryDate || dayjs().add(7, 'day').format('YYYY-MM-DD'),
    vendor: initialValues?.vendor || null,
    vendorId: initialValues?.vendorId || '',
    buyerName: initialValues?.buyerName || '',
    status: initialValues?.status || 'draft',
    paymentTermId: initialValues?.paymentTermId || '',
    shippingAddress: initialValues?.shippingAddress || '',
    notes: initialValues?.notes || '',
    lineItems: initialValues?.lineItems || [createEmptyLineItem(0)],
    subtotal: initialValues?.subtotal || 0,
    totalDiscount: initialValues?.totalDiscount || 0,
    totalTax: initialValues?.totalTax || 0,
    grandTotal: initialValues?.grandTotal || 0,
    referenceNumber: initialValues?.referenceNumber || '',
    buyerEmail: initialValues?.buyerEmail || '',
    buyerPhone: initialValues?.buyerPhone || '',
    memo: initialValues?.memo || '',
    paymentTerms: initialValues?.paymentTerms || 'net_30',
  };

  const formik = useFormik({
    initialValues: initialFormValues,
    validationSchema,
    onSubmit: async (values) => {
      try {
        setLoading(true);
        setError(null);
        const purchaseOrderData: PurchaseOrderFormData = {
          ...values,
          vendor: Number(values.vendor),
          vendorName: vendors.find(v => v.id === values.vendor)?.display_name || '',
          poDate: values.poDate,
          expectedDate: values.expectedDeliveryDate,
          currency: currencyInfo?.functional_currency || 'INR',
          lineItems: lineItems.map(item => ({
            description: item.description || item.product_name,
            quantity: Number(item.quantity) || 0,
            unitPrice: Number(item.unit_price) || 0,
            discountPercent: Number(item.discount_percent) || 0,
            taxable: Boolean(item.sales_tax),
            taxRate: Number(item.sales_tax_rate) || 0,
            productId: typeof item.product_id === 'string' ? Number(item.product_id) : item.product_id
          })),
        };
        if (isEditMode) {
          await purchaseOrderService.updatePurchaseOrder(initialValues.id, purchaseOrderData);
          showSuccess(
            'Purchase Order Updated!',
            `Purchase Order ${purchaseOrderData.poNumber} has been updated successfully.`
          );
        } else {
          await purchaseOrderService.createPurchaseOrder(purchaseOrderData);
          showSuccess(
            'Purchase Order Created!',
            `Purchase Order ${purchaseOrderData.poNumber} has been created successfully.`
          );
        }

        // Close after a short delay to show the success message
        setTimeout(() => {
          onSave();
        }, 1500);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to save purchase order';
        console.error('Failed to save purchase order:', err);
        setError(errorMessage);
        showError(
          'Save Failed',
          errorMessage
        );
      } finally {
        setLoading(false);
      }
    },
  });

  const handleAccordionChange = (panel: string) => (
    event: React.SyntheticEvent,
    isExpanded: boolean
  ) => {
    setExpanded(isExpanded ? panel : '');
  };

  const handleVendorChange = (event: any) => {
    const vendorId = event.target.value;
    const selectedVendor = vendors.find(v => v.id === vendorId);
    formik.setFieldValue('vendor', vendorId);
    formik.setFieldValue('vendorName', selectedVendor?.display_name || '');
    if (selectedVendor) {
      const vendorPaymentTerm = selectedVendor.payment_terms || 'net_30';
      const matchingTerm = paymentTerms.find(term => term.code === vendorPaymentTerm);
      formik.setFieldValue('paymentTerms', matchingTerm ? matchingTerm.code : 'net_30');
      formik.setFieldValue('currency', selectedVendor.currency || currencyInfo?.functional_currency || 'INR');
    }
  };

  const transformProductsToAccountOptions = (): AccountOption[] => {
    // Products will be loaded directly from API when needed
    // For now, return empty array - this will be implemented when product functionality is restored
    return [];
  };

  const handleLineChange = (lineId: string, field: string, value: any) => {
    setLineItems(prevItems =>
      prevItems.map(item => {
        if (item.id === lineId) {
          const updatedItem = { ...item };
          
          if (field === 'account_selection') {
            // Product selection functionality will be implemented when products are restored
            // For now, just handle basic account selection
            if (value) {
              updatedItem.account_id = Number(value.id);
              updatedItem.account_name = value.account_name;
              updatedItem.description = value.account_name;
            }
          } else if (field === 'description') {
            updatedItem.description = value;
          } else if (field === 'unit_of_measure') {
            updatedItem.unit_of_measure = value;
          } else if (field === 'quantity') {
            updatedItem.quantity = value;
            updatedItem.line_total = calculateLineTotal(updatedItem.quantity, updatedItem.unit_price, updatedItem.discount_percent);
            if (updatedItem.sales_tax) {
              const selectedTax = salesTaxes.find(tax => tax.id === updatedItem.sales_tax);
              if (selectedTax) {
                updatedItem.taxable_amount = updatedItem.line_total;
                updatedItem.sales_tax_amount = (updatedItem.line_total * selectedTax.rate) / 100;
              }
            }
          } else if (field === 'unit_price') {
            updatedItem.unit_price = value;
            updatedItem.line_total = calculateLineTotal(updatedItem.quantity, updatedItem.unit_price, updatedItem.discount_percent);
            if (updatedItem.sales_tax) {
              const selectedTax = salesTaxes.find(tax => tax.id === updatedItem.sales_tax);
              if (selectedTax) {
                updatedItem.taxable_amount = updatedItem.line_total;
                updatedItem.sales_tax_amount = (updatedItem.line_total * selectedTax.rate) / 100;
              }
            }
          } else if (field === 'discount_percent') {
            updatedItem.discount_percent = value;
            updatedItem.line_total = calculateLineTotal(updatedItem.quantity, updatedItem.unit_price, updatedItem.discount_percent);
            if (updatedItem.sales_tax) {
              const selectedTax = salesTaxes.find(tax => tax.id === updatedItem.sales_tax);
              if (selectedTax) {
                updatedItem.taxable_amount = updatedItem.line_total;
                updatedItem.sales_tax_amount = (updatedItem.line_total * selectedTax.rate) / 100;
              }
            }
          } else if (field === 'sales_tax') {
            updatedItem.sales_tax = value;
            const selectedTax = salesTaxes.find(tax => tax.id === value);
            if (selectedTax) {
              updatedItem.sales_tax_description = selectedTax.description;
              updatedItem.sales_tax_rate = selectedTax.rate;
              const taxableAmount = updatedItem.line_total;
              updatedItem.taxable_amount = taxableAmount;
              updatedItem.sales_tax_amount = (taxableAmount * selectedTax.rate) / 100;
            } else {
              updatedItem.sales_tax_description = '';
              updatedItem.sales_tax_rate = 0;
              updatedItem.sales_tax_amount = 0;
              updatedItem.taxable_amount = 0;
            }
          }
          
          // Always update final amount
          updatedItem.amount = updatedItem.line_total + (updatedItem.sales_tax_amount || 0);
          return updatedItem;
        }
        return item;
      })
    );
  };

  const handleAddLine = () => {
    const newIndex = lineItems.length;
    setLineItems(prevItems => [...prevItems, createEmptyLineItem(newIndex)]);
  };

  const handleRemoveLine = (lineId: string) => {
    setLineItems(prevItems => prevItems.filter(item => item.id !== lineId));
  };

  const calculateLineTotal = (quantity: number, unitPrice: number, discountPercent: number) => {
    const subtotal = quantity * unitPrice;
    const discount = subtotal * (discountPercent / 100);
    return subtotal - discount;
  };

  const calculateSubtotal = () => {
    return lineItems.reduce((total, item) => total + item.line_total, 0);
  };

  const calculateTotalDiscount = () => {
    return lineItems.reduce((total, item) => {
      const subtotal = item.quantity * item.unit_price;
      return total + (subtotal * (item.discount_percent / 100));
    }, 0);
  };

  const calculateTotalTax = () => {
    return lineItems.reduce((total, item) => total + (item.sales_tax_amount || 0), 0);
  };

  const calculateGrandTotal = () => {
    return calculateSubtotal() + calculateTotalTax();
  };

  const getCurrencySymbol = () => {
    return currencyInfo?.functional_currency_symbol || '$';
  };

  const getStatusColor = (status: string) => {
    const statusOption = PO_STATUS_OPTIONS.find(opt => opt.value === status);
    return statusOption?.color || 'default';
  };

  const additionalColumns = [
    {
      key: 'quantity',
      label: 'Qty',
      width: 80,
      render: (line: JournalLineItem, onChange: (value: any) => void) => {
        const poLine = line as POLineItem;
        return (
          <QuantityInput
            size="small"
            value={poLine.quantity}
            onChange={(e) => onChange(Number(e.target.value))}
            min={0.01}
            sx={{ width: '100%' }}
          />
        );
      },
    },
    {
      key: 'unit_of_measure',
      label: 'Unit',
      width: 80,
      render: (line: JournalLineItem, onChange: (value: any) => void) => {
        const poLine = line as POLineItem;
        return (
          <FormControl fullWidth size="small">
            <Select
              value={poLine.unit_of_measure}
              onChange={(e) => onChange(e.target.value)}
            >
              {COMMON_UNITS.map((unit, index) => (
                <MenuItem key={`unit-${unit.id || index}`} value={unit.id}>
                  {unit.abbreviation}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        );
      },
    },
    {
      key: 'unit_price',
      label: 'Unit Price',
      width: 120,
      render: (line: JournalLineItem, onChange: (value: any) => void) => {
        const poLine = line as POLineItem;
        const unitPrice = parseFloat(poLine.unit_price?.toString() || '0');
        return (
          <FormattedCurrencyInput
            size="small"
            name={`unit_price_${poLine.id}`}
            value={unitPrice}
            onChange={(e) => {
              const newValue = parseFloat(e.target.value) || 0;
              onChange(newValue);
            }}
            currencySymbol={getCurrencySymbol()}
            disabled={viewMode}
            sx={{ width: '100%' }}
          />
        );
      },
    },
    {
      key: 'discount_percent',
      label: 'Discount %',
      width: 100,
      render: (line: JournalLineItem, onChange: (value: any) => void) => {
        const poLine = line as POLineItem;
        return (
          <TextField
            type="number"
            size="small"
            value={poLine.discount_percent}
            onChange={(e) => onChange(Number(e.target.value))}
            inputProps={{ min: 0, max: 100, step: 0.1, style: { textAlign: 'right' } }}
            sx={{ width: '100%' }}
          />
        );
      },
    },
    {
      key: 'line_total',
      label: 'Total',
      width: 120,
      render: (line: JournalLineItem) => {
        const poLine = line as POLineItem;
        const lineTotal = parseFloat(poLine.line_total?.toString() || '0');
        const taxAmount = parseFloat(poLine.sales_tax_amount?.toString() || '0');
        const totalWithTax = lineTotal + taxAmount;
        return (
          <Typography variant="body2" fontWeight="medium" sx={{ textAlign: 'right' }}>
            {getCurrencySymbol()}
            {totalWithTax.toLocaleString('en-US', {
              minimumFractionDigits: 2,
              maximumFractionDigits: 2,
            })}
          </Typography>
        );
      },
    },
  ];

  // Product loading checks removed - products will be loaded directly from API when needed

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <DialogTitle
        sx={{
          borderBottom: '1px solid rgba(0, 0, 0, 0.12)',
          pb: 2,
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}
      >
        <Box>
          <Typography variant="h5" fontWeight="bold">
            {viewMode
              ? 'View Purchase Order'
              : isEditMode
              ? 'Edit Purchase Order'
              : 'Create New Purchase Order'}
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
            {viewMode
              ? `Viewing PO: ${formik.values.poNumber}`
              : isEditMode
              ? `Editing PO: ${formik.values.poNumber}`
              : 'Create a comprehensive purchase order with all required details'}
          </Typography>
        </Box>
        <IconButton onClick={onClose} sx={{ color: 'text.secondary' }}>
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      <form onSubmit={viewMode ? e => e.preventDefault() : formik.handleSubmit}>
        <DialogContent
          dividers
          sx={{
            p: 0,
            flex: 1,
            overflow: 'auto',
            height: 'calc(100vh - 200px)',
          }}
        >
          {error && (
            <Alert severity="error" sx={{ m: 3, mb: 0 }}>
              {error}
            </Alert>
          )}
          {productsError && (
            <Alert severity="error" sx={{ m: 3, mb: 0 }}>
              Failed to load products: {productsError}
            </Alert>
          )}
          {loadingProducts && (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          )}
          <Box sx={{ p: 3 }}>
            {/* PO Header Information */}
            <Accordion expanded={expanded === 'panel1'} onChange={handleAccordionChange('panel1')} sx={{ mb: 2 }}>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Box display="flex" alignItems="center" gap={1}>
                  <ReceiptIcon />
                  <Typography variant="subtitle1" fontWeight="bold">
                    Purchase Order Details
                  </Typography>
                </Box>
              </AccordionSummary>
              <AccordionDetails>
                <Grid container spacing={3}>
                  {/* PO Number and Status */}
                  <Grid item xs={12} sm={6} md={4}>
                    <TextField
                      fullWidth
                      label="PO Number"
                      name="poNumber"
                      value={formik.values.poNumber}
                      onChange={formik.handleChange}
                      disabled
                      helperText="Auto-generated when creating PO"
                    />
                  </Grid>
                  <Grid item xs={12} sm={6} md={4}>
                    <StandardDatePicker
                      label="PO Date *"
                      value={dayjs(formik.values.poDate)}
                      onChange={newValue => {
                        if (newValue && !viewMode) {
                          formik.setFieldValue('poDate', newValue.format('YYYY-MM-DD'));
                        }
                      }}
                      disabled={viewMode}
                      required
                      error={formik.touched.poDate && Boolean(formik.errors.poDate)}
                      helperText={formik.touched.poDate && (formik.errors.poDate as string)}
                      businessContext="general"
                      showQuickActions
                      dateFormat="DD/MM/YYYY"
                    />
                  </Grid>
                  <Grid item xs={12} sm={6} md={4}>
                    <FormControl fullWidth>
                      <InputLabel>Status</InputLabel>
                      <Select
                        name="status"
                        value={formik.values.status}
                        onChange={formik.handleChange}
                        label="Status"
                        disabled={viewMode}
                        renderValue={value => (
                          <Box display="flex" alignItems="center" gap={1}>
                            <Chip
                              label={PO_STATUS_OPTIONS.find(opt => opt.value === value)?.label || value}
                              color={getStatusColor(value) as any}
                              size="small"
                            />
                          </Box>
                        )}
                      >
                        {PO_STATUS_OPTIONS.map((option, index) => (
                          <MenuItem key={`status-${option.value || index}`} value={option.value}>
                            {option.label}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  {/* Vendor Information */}
                  <Grid item xs={12} sm={6}>
                    <FormControl fullWidth>
                      <InputLabel>Vendor *</InputLabel>
                      <Select
                        name="vendor"
                        value={formik.values.vendor || ''}
                        onChange={handleVendorChange}
                        label="Vendor *"
                        error={formik.touched.vendor && Boolean(formik.errors.vendor)}
                        disabled={loadingVendors || viewMode}
                      >
                        <MenuItem key="vendor-empty" value="">
                          <em>Select a vendor</em>
                        </MenuItem>
                        {loadingVendors ? (
                          <MenuItem key="vendor-loading" disabled>
                            <CircularProgress size={20} sx={{ mr: 1 }} />
                            Loading vendors...
                          </MenuItem>
                        ) : vendors.length === 0 ? (
                          <MenuItem key="vendor-no-vendors" disabled>
                            No vendors available
                          </MenuItem>
                        ) : (
                          vendors.map((vendor, index) => (
                            <MenuItem key={`vendor-${vendor.id || index}`} value={vendor.id}>
                              <Box>
                                <Typography variant="body2" fontWeight="medium">
                                  {vendor.display_name}
                                </Typography>
                                <Typography variant="caption" color="text.secondary">
                                  {vendor.vendor_id} | {vendor.email}
                                </Typography>
                              </Box>
                            </MenuItem>
                          ))
                        )}
                      </Select>
                      {formik.touched.vendor && formik.errors.vendor && (
                        <FormHelperText error>{formik.errors.vendor as string}</FormHelperText>
                      )}
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <StandardDatePicker
                      label="Expected Delivery Date *"
                      value={formik.values.expectedDeliveryDate ? dayjs(formik.values.expectedDeliveryDate) : null}
                      onChange={newValue => {
                        if (newValue) {
                          formik.setFieldValue('expectedDeliveryDate', newValue.format('YYYY-MM-DD'));
                        }
                      }}
                      required
                      error={formik.touched.expectedDeliveryDate && Boolean(formik.errors.expectedDeliveryDate)}
                      helperText={formik.touched.expectedDeliveryDate && (formik.errors.expectedDeliveryDate as string)}
                      businessContext="general"
                      showQuickActions
                      dateFormat="DD/MM/YYYY"
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Reference Number"
                      name="referenceNumber"
                      value={formik.values.referenceNumber}
                      onChange={formik.handleChange}
                      helperText="Internal reference or requisition number"
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <Autocomplete
                      options={paymentTerms}
                      getOptionLabel={option => option.name}
                      value={paymentTerms.find(term => term.code === formik.values.paymentTerms) || null}
                      onChange={(_, newValue) => {
                        formik.setFieldValue('paymentTerms', newValue ? newValue.code : 'net_30');
                      }}
                      renderOption={(props, option, { index }) => (
                        <li {...props} key={`payment-term-${option.code || index}`}>
                          {option.name}
                        </li>
                      )}
                      renderInput={params => (
                        <TextField {...params} label="Payment Terms" fullWidth />
                      )}
                    />
                  </Grid>
                </Grid>
              </AccordionDetails>
            </Accordion>
            {/* Buyer Information */}
            <Accordion expanded={expanded === 'panel2'} onChange={handleAccordionChange('panel2')} sx={{ mb: 2 }}>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Box display="flex" alignItems="center" gap={1}>
                  <PersonIcon />
                  <Typography variant="subtitle1" fontWeight="bold">
                    Buyer Information
                  </Typography>
                </Box>
              </AccordionSummary>
              <AccordionDetails>
                <Grid container spacing={3}>
                  <Grid item xs={12} sm={6} md={4}>
                    <TextField
                      fullWidth
                      label="Buyer Name *"
                      name="buyerName"
                      value={formik.values.buyerName}
                      onChange={formik.handleChange}
                      error={formik.touched.buyerName && Boolean(formik.errors.buyerName)}
                      helperText={formik.touched.buyerName && (formik.errors.buyerName as string)}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6} md={4}>
                    <TextField
                      fullWidth
                      label="Buyer Email"
                      name="buyerEmail"
                      type="email"
                      value={formik.values.buyerEmail}
                      onChange={formik.handleChange}
                    />
                  </Grid>
                  <Grid item xs={12} sm={6} md={4}>
                    <TextField
                      fullWidth
                      label="Buyer Phone"
                      name="buyerPhone"
                      value={formik.values.buyerPhone}
                      onChange={formik.handleChange}
                    />
                  </Grid>
                </Grid>
              </AccordionDetails>
            </Accordion>
            {/* Shipping Information */}
            <Accordion expanded={expanded === 'panel3'} onChange={handleAccordionChange('panel3')} sx={{ mb: 2 }}>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Box display="flex" alignItems="center" gap={1}>
                  <ShippingIcon />
                  <Typography variant="subtitle1" fontWeight="bold">
                    Shipping & Delivery
                  </Typography>
                </Box>
              </AccordionSummary>
              <AccordionDetails>
                <Grid container spacing={3}>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Ship To Address"
                      name="shippingAddress"
                      multiline
                      rows={3}
                      value={formik.values.shippingAddress}
                      onChange={formik.handleChange}
                      placeholder="Enter complete shipping address including city, state, and postal code"
                    />
                  </Grid>
                </Grid>
              </AccordionDetails>
            </Accordion>
            {/* Line Items using JournalLineTable */}
            <Accordion expanded={expanded === 'panel4'} onChange={handleAccordionChange('panel4')} sx={{ mb: 2 }}>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Box display="flex" alignItems="center" gap={1}>
                  <BusinessIcon />
                  <Typography variant="subtitle1" fontWeight="bold">
                    Line Items ({lineItems.length})
                  </Typography>
                  {loadingProducts && (
                    <CircularProgress size={20} sx={{ ml: 2 }} />
                  )}
                </Box>
              </AccordionSummary>
              <AccordionDetails>
                <Box mb={2}>
                  <Typography variant="body2" color="text.secondary">
                    Add products and services to this purchase order
                  </Typography>
                  {purchaseProducts.length === 0 && !loadingProducts && (
                    <Alert severity="warning" sx={{ mt: 2 }}>
                      No products available. Please add products in the inventory management section first.
                    </Alert>
                  )}
                </Box>
                <JournalLineTable
                  tableMode="custom"
                  showAccountColumn
                  showDescriptionColumn
                  showMemoColumn={false}
                  showAmountColumn={false}
                  showDebitCreditColumns={false}
                  showSalesTaxColumn
                  showActionsColumn={!viewMode}
                  lines={lineItems}
                  accounts={transformProductsToAccountOptions()}
                  salesTaxes={salesTaxes}
                  onLineChange={handleLineChange}
                  onAddLine={handleAddLine}
                  onRemoveLine={handleRemoveLine}
                  currencySymbol={getCurrencySymbol()}
                  accountColumnLabel="Product"
                  accountPlaceholder="Select Product"
                  descriptionPlaceholder="Enter description"
                  salesTaxColumnLabel="Input Tax"
                  salesTaxPlaceholder="Select Input Tax"
                  additionalColumns={additionalColumns}
                  minLines={1}
                  tableHeight="300px"
                  readOnly={viewMode}
                  loading={false}
                />
                {/* Validation Message */}
                {lineItems.length === 0 || !lineItems.some(item => item.description.trim() !== '' || item.quantity > 0 || item.unit_price > 0) ? (
                  <FormHelperText error sx={{ mt: 1 }}>
                    Please add at least one line item with description, quantity, or price
                  </FormHelperText>
                ) : null}
                {/* Totals Summary */}
                <Card sx={{ mt: 3, bgcolor: 'grey.50' }}>
                  <CardContent>
                    <Grid container spacing={2}>
                      <Grid item xs={12} sm={6} md={2.4}>
                        <Typography variant="body2" color="text.secondary">Subtotal</Typography>
                        <Typography variant="h6" fontWeight="medium">
                          {getCurrencySymbol()}
                          {calculateSubtotal().toLocaleString('en-US', {
                            minimumFractionDigits: 2,
                            maximumFractionDigits: 2,
                          })}
                        </Typography>
                      </Grid>
                      <Grid item xs={12} sm={6} md={2.4}>
                        <Typography variant="body2" color="text.secondary">Total Discount</Typography>
                        <Typography variant="h6" fontWeight="medium" color="success.main">
                          -{getCurrencySymbol()}
                          {calculateTotalDiscount().toLocaleString('en-US', {
                            minimumFractionDigits: 2,
                            maximumFractionDigits: 2,
                          })}
                        </Typography>
                      </Grid>
                      <Grid item xs={12} sm={6} md={2.4}>
                        <Typography variant="body2" color="text.secondary">Total Tax</Typography>
                        <Typography variant="h6" fontWeight="medium">
                          {getCurrencySymbol()}
                          {calculateTotalTax().toLocaleString('en-US', {
                            minimumFractionDigits: 2,
                            maximumFractionDigits: 2,
                          })}
                        </Typography>
                      </Grid>
                      <Grid item xs={12} sm={6} md={2.4}>
                        <Typography variant="body2" color="text.secondary">Net Amount</Typography>
                        <Typography variant="h6" fontWeight="medium">
                          {getCurrencySymbol()}
                          {(calculateSubtotal() - calculateTotalDiscount()).toLocaleString('en-US', {
                            minimumFractionDigits: 2,
                            maximumFractionDigits: 2,
                          })}
                        </Typography>
                      </Grid>
                      <Grid item xs={12} sm={6} md={2.4}>
                        <Typography variant="body2" color="text.secondary">Total Payment</Typography>
                        <Typography variant="h5" fontWeight="bold" color="primary.main">
                          {getCurrencySymbol()}
                          {calculateGrandTotal().toLocaleString('en-US', {
                            minimumFractionDigits: 2,
                            maximumFractionDigits: 2,
                          })}
                        </Typography>
                      </Grid>
                    </Grid>
                  </CardContent>
                </Card>
              </AccordionDetails>
            </Accordion>
            {/* Notes and Comments */}
            <Accordion expanded={expanded === 'panel5'} onChange={handleAccordionChange('panel5')}>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Box display="flex" alignItems="center" gap={1}>
                  <DescriptionIcon />
                  <Typography variant="subtitle1" fontWeight="bold">
                    Notes & Comments
                  </Typography>
                </Box>
              </AccordionSummary>
              <AccordionDetails>
                <Grid container spacing={3}>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Internal Memo"
                      name="memo"
                      multiline
                      rows={3}
                      value={formik.values.memo}
                      onChange={formik.handleChange}
                      placeholder="Internal notes for your team..."
                    />
                  </Grid>
                  <Grid item xs={12} sm={6}>
                    <TextField
                      fullWidth
                      label="Notes to Vendor"
                      name="notes"
                      multiline
                      rows={3}
                      value={formik.values.notes}
                      onChange={formik.handleChange}
                      placeholder="Special instructions or notes for the vendor..."
                    />
                  </Grid>
                </Grid>
              </AccordionDetails>
            </Accordion>
          </Box>
        </DialogContent>
        <DialogActions sx={{ px: 3, py: 2, bgcolor: 'grey.50' }}>
          <Button onClick={onClose} disabled={loading} size="large">
            {viewMode ? 'Close' : 'Cancel'}
          </Button>
          {!viewMode && (
            <Button
              type="submit"
              variant="contained"
              disabled={loading}
              startIcon={loading ? <CircularProgress size={20} /> : null}
              size="large"
              sx={{
                minWidth: 160,
                background: 'linear-gradient(45deg, #1976d2 30%, #42a5f5 90%)',
                '&:hover': {
                  background: 'linear-gradient(45deg, #1565c0 30%, #1976d2 90%)',
                },
              }}
            >
              {loading ? 'Saving...' : isEditMode ? 'Update Purchase Order' : 'Create Purchase Order'}
            </Button>
          )}
        </DialogActions>
      </form>

      {/* Confirmation Dialog */}
      <ConfirmationDialog
        open={confirmation.open}
        onClose={hideConfirmation}
        onConfirm={confirmation.onConfirm}
        title={confirmation.title}
        message={confirmation.message}
        type={confirmation.type}
        confirmText={confirmation.confirmText}
        cancelText={confirmation.cancelText}
        showCancel={confirmation.showCancel}
        confirmColor={confirmation.confirmColor}
        autoClose={confirmation.autoClose}
        autoCloseDelay={confirmation.autoCloseDelay}
      />
    </LocalizationProvider>
  );
};

export default PurchaseOrderForm;