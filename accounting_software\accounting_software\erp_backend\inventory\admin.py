from django.contrib import admin
from .models import (
    UnitsOfMeasure, Warehouse, WarehouseLocation, Inventory, InventoryCostLayer,
    StockTransaction, StockAdjustment, StockAdjustmentItem, LowStockAlert,
    StockValuation, StockValuationItem, GoodsReceiptNote, GoodsReceiptNoteItem,
    VendorInvoice, GoodsReturnNote, GoodsReturnNoteItem, InventoryTransfer,
    InventoryTransferItem
)


@admin.register(UnitsOfMeasure)
class UnitsOfMeasureAdmin(admin.ModelAdmin):
    list_display = ['unit_name', 'abbreviation', 'unit_type', 'is_active']
    list_filter = ['unit_type', 'is_active']
    search_fields = ['unit_name', 'abbreviation']


@admin.register(Warehouse)
class WarehouseAdmin(admin.ModelAdmin):
    list_display = ['name', 'warehouse_code', 'warehouse_type', 'location', 'is_active']
    list_filter = ['warehouse_type', 'is_active']
    search_fields = ['name', 'warehouse_code', 'location']


@admin.register(WarehouseLocation)
class WarehouseLocationAdmin(admin.ModelAdmin):
    list_display = ['location_code', 'warehouse', 'zone', 'aisle', 'rack', 'bin', 'location_type', 'is_active']
    list_filter = ['warehouse', 'location_type', 'is_active']
    search_fields = ['location_code', 'zone', 'aisle', 'rack', 'bin']
    ordering = ['warehouse__name', 'location_code']


@admin.register(Inventory)
class InventoryAdmin(admin.ModelAdmin):
    list_display = ['product', 'warehouse', 'location', 'quantity_on_hand', 'quantity_reserved', 'reorder_point', 'valuation_method', 'abc_classification']
    list_filter = ['warehouse', 'location', 'valuation_method', 'abc_classification', 'product__category']
    search_fields = ['product__name', 'product__sku', 'warehouse__name', 'location__location_code']
    readonly_fields = ['available_quantity', 'is_below_reorder_point', 'is_out_of_stock', 'total_value_average']


class InventoryCostLayerInline(admin.TabularInline):
    model = InventoryCostLayer
    extra = 0
    readonly_fields = ['is_depleted', 'total_value']


@admin.register(InventoryCostLayer)
class InventoryCostLayerAdmin(admin.ModelAdmin):
    list_display = ['inventory', 'layer_date', 'reference_type', 'original_quantity', 'remaining_quantity', 'unit_cost']
    list_filter = ['reference_type', 'layer_date']
    search_fields = ['inventory__product__name', 'batch_number']
    readonly_fields = ['is_depleted', 'total_value']


@admin.register(StockTransaction)
class StockTransactionAdmin(admin.ModelAdmin):
    list_display = ['product', 'warehouse', 'transaction_type', 'quantity', 'unit_cost', 'total_cost', 'txn_date']
    list_filter = ['transaction_type', 'warehouse', 'txn_date', 'reference_type']
    search_fields = ['product__name', 'product__sku', 'batch_number', 'description']
    readonly_fields = ['total_cost']
    date_hierarchy = 'txn_date'


@admin.register(LowStockAlert)
class LowStockAlertAdmin(admin.ModelAdmin):
    list_display = ['inventory', 'alert_type', 'priority', 'status', 'current_quantity', 'threshold_quantity', 'created_at']
    list_filter = ['alert_type', 'priority', 'status', 'created_at']
    search_fields = ['inventory__product__name', 'inventory__warehouse__name']
    readonly_fields = ['created_at', 'acknowledged_at', 'resolved_at']
    actions = ['acknowledge_alerts', 'resolve_alerts']

    def acknowledge_alerts(self, request, queryset):
        for alert in queryset.filter(status='ACTIVE'):
            alert.acknowledge(request.user)
        self.message_user(request, f"Acknowledged {queryset.count()} alerts.")
    acknowledge_alerts.short_description = "Acknowledge selected alerts"

    def resolve_alerts(self, request, queryset):
        for alert in queryset.filter(status__in=['ACTIVE', 'ACKNOWLEDGED']):
            alert.resolve(request.user)
        self.message_user(request, f"Resolved {queryset.count()} alerts.")
    resolve_alerts.short_description = "Resolve selected alerts"


class StockValuationItemInline(admin.TabularInline):
    model = StockValuationItem
    extra = 0
    readonly_fields = ['fifo_total_value', 'lifo_total_value', 'average_total_value', 'standard_total_value']


@admin.register(StockValuation)
class StockValuationAdmin(admin.ModelAdmin):
    list_display = ['valuation_date', 'valuation_type', 'warehouse', 'status', 'total_items', 'average_total_value', 'created_by']
    list_filter = ['valuation_type', 'status', 'warehouse', 'valuation_date']
    search_fields = ['warehouse__name', 'product_category']
    readonly_fields = ['total_items', 'total_quantity', 'fifo_total_value', 'lifo_total_value', 'average_total_value', 'standard_total_value', 'created_at', 'completed_at']
    inlines = [StockValuationItemInline]

    def save_model(self, request, obj, form, change):
        if not change:  # New object
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(StockValuationItem)
class StockValuationItemAdmin(admin.ModelAdmin):
    list_display = ['valuation', 'inventory', 'quantity_on_hand', 'average_unit_cost', 'average_total_value']
    list_filter = ['valuation__status', 'inventory__warehouse']
    search_fields = ['inventory__product__name', 'valuation__valuation_date']
    readonly_fields = ['fifo_total_value', 'lifo_total_value', 'average_total_value', 'standard_total_value']


class StockAdjustmentItemInline(admin.TabularInline):
    model = StockAdjustmentItem
    extra = 0
    readonly_fields = ['adjustment_quantity', 'adjustment_value']


@admin.register(StockAdjustment)
class StockAdjustmentAdmin(admin.ModelAdmin):
    list_display = ['adjustment_number', 'adjustment_type', 'adjustment_date', 'status', 'total_adjustment_value', 'created_by']
    list_filter = ['adjustment_type', 'status', 'reason_code', 'adjustment_date']
    search_fields = ['adjustment_number', 'description']
    readonly_fields = ['total_adjustment_value', 'created_at', 'updated_at']
    inlines = [StockAdjustmentItemInline]

    def save_model(self, request, obj, form, change):
        if not change:  # New object
            obj.created_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(StockAdjustmentItem)
class StockAdjustmentItemAdmin(admin.ModelAdmin):
    list_display = ['adjustment', 'product', 'warehouse', 'system_quantity', 'physical_quantity', 'adjustment_quantity', 'adjustment_value']
    list_filter = ['adjustment__status', 'warehouse']
    search_fields = ['product__name', 'adjustment__adjustment_number']
    readonly_fields = ['adjustment_quantity', 'adjustment_value']


@admin.register(GoodsReceiptNote)
class GoodsReceiptNoteAdmin(admin.ModelAdmin):
    list_display = ['grn_number', 'purchase_order', 'warehouse', 'receipt_date', 'status']
    list_filter = ['status', 'warehouse', 'receipt_date']
    search_fields = ['grn_number', 'purchase_order__po_number']


@admin.register(GoodsReceiptNoteItem)
class GoodsReceiptNoteItemAdmin(admin.ModelAdmin):
    list_display = ['grn', 'product', 'quantity_ordered', 'quantity_received', 'condition']
    list_filter = ['condition', 'grn__status']
    search_fields = ['product__name', 'grn__grn_number']


@admin.register(VendorInvoice)
class VendorInvoiceAdmin(admin.ModelAdmin):
    list_display = ['invoice_number', 'purchase_order', 'invoice_date', 'total_amount', 'status']
    list_filter = ['status', 'invoice_date']
    search_fields = ['invoice_number', 'purchase_order__po_number']


class GoodsReturnNoteItemInline(admin.TabularInline):
    model = GoodsReturnNoteItem
    extra = 0
    fields = ['product', 'original_grn_item', 'quantity_received', 'quantity_returned', 
              'return_reason', 'condition_notes', 'photos_attached']
    readonly_fields = ['return_value']


@admin.register(GoodsReturnNote)
class GoodsReturnNoteAdmin(admin.ModelAdmin):
    list_display = [
        'grn_return_number', 'vendor', 'original_grn', 'warehouse', 
        'return_date', 'return_reason', 'status', 'total_value'
    ]
    list_filter = ['status', 'return_reason', 'warehouse', 'return_date', 'vendor']
    search_fields = [
        'grn_return_number', 'vendor__name', 'vendor__display_name',
        'original_grn__grn_number', 'notes'
    ]
    readonly_fields = [
        'grn_return_number', 'created_at', 'updated_at', 'approved_at', 
        'posted_at', 'total_quantity', 'total_value'
    ]
    fieldsets = (
        ('Basic Information', {
            'fields': ('grn_return_number', 'original_grn', 'vendor', 'warehouse')
        }),
        ('Return Details', {
            'fields': ('return_date', 'return_reason', 'status', 'notes')
        }),
        ('Totals', {
            'fields': ('total_quantity', 'total_value', 'expected_credit_amount', 
                      'actual_credit_amount', 'credit_received_date')
        }),
        ('Workflow', {
            'fields': ('returned_by', 'approved_by', 'approved_at', 'posted_by', 'posted_at')
        }),
        ('Audit Trail', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    inlines = [GoodsReturnNoteItemInline]
    
    def save_model(self, request, obj, form, change):
        if not change:  # Creating new record
            obj.returned_by = request.user
        super().save_model(request, obj, form, change)


@admin.register(GoodsReturnNoteItem)
class GoodsReturnNoteItemAdmin(admin.ModelAdmin):
    list_display = [
        'grn_return', 'product', 'quantity_received', 'quantity_returned', 
        'return_reason', 'return_value', 'photos_attached'
    ]
    list_filter = ['return_reason', 'photos_attached', 'grn_return__status']
    search_fields = [
        'product__name', 'product__sku', 'grn_return__grn_return_number',
        'original_grn_item__grn__grn_number', 'condition_notes'
    ]
    readonly_fields = ['return_value']
    
    fieldsets = (
        ('Item Information', {
            'fields': ('grn_return', 'product', 'original_grn_item')
        }),
        ('Quantities', {
            'fields': ('quantity_received', 'quantity_returned', 'unit_cost', 'return_value')
        }),
        ('Return Details', {
            'fields': ('return_reason', 'batch_number', 'condition_notes', 'photos_attached')
        }),
        ('Display Order', {
            'fields': ('line_order',),
            'classes': ('collapse',)
        })
    )


@admin.register(InventoryTransfer)
class InventoryTransferAdmin(admin.ModelAdmin):
    list_display = ['transfer_number', 'from_warehouse', 'to_warehouse', 'transfer_date', 'status', 'total_items']
    list_filter = ['status', 'from_warehouse', 'to_warehouse', 'transfer_date']
    search_fields = ['transfer_number', 'notes']
    readonly_fields = ['transfer_number', 'total_quantity', 'total_items', 'created_at', 'updated_at']


@admin.register(InventoryTransferItem)
class InventoryTransferItemAdmin(admin.ModelAdmin):
    list_display = ['transfer', 'product', 'quantity', 'batch_number']
    list_filter = ['transfer__status', 'product']
    search_fields = ['transfer__transfer_number', 'product__name', 'product__sku'] 