#!/usr/bin/env python3

import requests
import json

# Configuration
BASE_URL = "http://localhost:8000/api"
USERNAME = "admin"
PASSWORD = "admin123"

def get_auth_token():
    """Get authentication token"""
    url = f"{BASE_URL}/../api-token-auth/"
    data = {
        "username": USERNAME,
        "password": PASSWORD
    }
    
    try:
        response = requests.post(url, json=data)
        if response.status_code == 200:
            token = response.json().get('token')
            return token
        else:
            print(f"❌ Authentication failed: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ Authentication error: {e}")
        return None

def check_product(token, product_id):
    """Check product details"""
    url = f"{BASE_URL}/sales/products/{product_id}/"
    headers = {
        "Authorization": f"Token {token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(url, headers=headers)
        
        if response.status_code == 200:
            product = response.json()
            print(f"✅ Product {product_id} Details:")
            print(f"  - Name: {product.get('name')}")
            print(f"  - SKU: {product.get('sku')}")
            print(f"  - Type: {product.get('product_type')}")
            print(f"  - Status: {product.get('status')}")
            print(f"  - Unit Price: {product.get('unit_price')}")
            return product
        else:
            print(f"❌ Failed to get product {product_id}: {response.status_code}")
            print(f"Response: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

def check_po_bills(token, po_number):
    """Check if PO has any vendor bills"""
    url = f"{BASE_URL}/purchase/vendor-bills/"
    headers = {
        "Authorization": f"Token {token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(url, headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            bills = data.get('results', data) if isinstance(data, dict) else data
            
            po_bills = []
            for bill in bills:
                # Check if bill is linked to this PO
                if bill.get('source_type') == 'po':
                    # You might need to check the actual PO reference
                    po_bills.append(bill)
            
            print(f"\n📋 Vendor Bills for PO {po_number}:")
            if po_bills:
                for bill in po_bills:
                    print(f"  - Bill: {bill.get('bill_number')}")
                    print(f"  - Status: {bill.get('status')}")
                    print(f"  - Source: {bill.get('source_type')}")
            else:
                print(f"  - No vendor bills found for PO {po_number}")
            
            return po_bills
        else:
            print(f"❌ Failed to get vendor bills: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

if __name__ == "__main__":
    print("🔍 Checking Product 48 and PO-000033")
    print("=" * 50)
    
    token = get_auth_token()
    if not token:
        exit(1)
    
    # Check Product 48
    product = check_product(token, 48)
    
    # Check if PO-000033 has bills
    bills = check_po_bills(token, "PO-000033")
    
    print("\n" + "=" * 50)
    print("📊 ANALYSIS")
    print("=" * 50)
    
    if product:
        print(f"Product 48 type: {product.get('product_type')}")
        if product.get('product_type') == 'service':
            print("✅ Product 48 is a service - should be included")
        else:
            print("❌ Product 48 is not a service - will be filtered out")
    
    if bills is not None:
        if len(bills) > 0:
            print(f"❌ PO-000033 has {len(bills)} vendor bills - will be filtered out")
        else:
            print("✅ PO-000033 has no vendor bills - should be included")
