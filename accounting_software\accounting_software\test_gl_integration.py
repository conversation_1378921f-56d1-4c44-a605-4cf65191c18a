#!/usr/bin/env python3
"""
Test GL integration for vendor bills
"""
import requests
import json

# Configuration
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api"
TOKEN = "e15bc01f831c5111f413f534ef82288744cb7d41"

HEADERS = {
    'Content-Type': 'application/json',
    'Authorization': f'Token {TOKEN}'
}

def test_gl_integration():
    print("🧾 TESTING GL INTEGRATION FOR VENDOR BILLS")
    print("=" * 55)
    
    try:
        # 1. Get vendor and product
        print("1. Getting vendor and product...")
        vendors_response = requests.get(f"{API_BASE}/contacts/vendors/", headers=HEADERS, timeout=5)
        vendor_id = vendors_response.json()['results'][0]['contact']
        
        products_response = requests.get(f"{API_BASE}/sales/products/", headers=HEADERS, timeout=5)
        product_id = products_response.json()['results'][0]['id']
        
        print(f"✅ Using vendor ID: {vendor_id}, product ID: {product_id}")
        
        # 2. Create vendor bill with GL accounts and tax
        print("\n2. Creating vendor bill with GL integration...")
        bill_data = {
            "vendor": vendor_id,
            "bill_date": "2025-07-06",
            "due_date": "2025-08-05",
            "status": "approved",  # This should trigger GL entry creation
            "payment_terms": "Net 30",
            "reference_number": "GL-TEST-001",
            "notes": "Testing GL integration with accounts and tax",
            "line_items": [
                {
                    "product": product_id,
                    "item_description": "Product with GL account",
                    "quantity": 2,
                    "unit_price": 100.00,
                    "tax_rate": 10.0,  # 10% input tax
                    "account_code": "5010-COGS",  # Cost of Goods Sold
                    "line_order": 1
                },
                {
                    "item_description": "Service with different account",
                    "quantity": 1,
                    "unit_price": 200.00,
                    "tax_rate": 10.0,  # 10% input tax
                    "account_code": "5020-Services",  # Services Expense
                    "line_order": 2
                }
            ]
        }
        
        print("Sending bill data:")
        print(json.dumps(bill_data, indent=2))
        
        response = requests.post(f"{API_BASE}/purchase/vendor-bills/", json=bill_data, headers=HEADERS, timeout=10)
        
        print(f"\nStatus Code: {response.status_code}")
        
        if response.status_code == 201:
            print("✅ SUCCESS! Vendor bill created!")
            data = response.json()
            bill_id = data['id']
            bill_number = data['bill_number']
            total_amount = data['total_amount']
            
            print(f"Bill ID: {bill_id}")
            print(f"Bill Number: {bill_number}")
            print(f"Total Amount: ${total_amount}")
            print(f"Line Items: {len(data['line_items'])}")
            
            # Show line items with tax details
            for i, item in enumerate(data['line_items']):
                print(f"  Item {i+1}: {item['item_description']}")
                print(f"    Line Total: ${item['line_total']}")
                print(f"    Tax Amount: ${item.get('tax_amount', 0)}")
                print(f"    Account Code: {item.get('account_code', 'N/A')}")
            
            # 3. Check if GL entries were created
            print(f"\n3. Checking GL entries for bill {bill_number}...")
            gl_response = requests.get(f"{API_BASE}/gl/journal-entries/?search=VB-{bill_number}", headers=HEADERS, timeout=5)
            
            if gl_response.status_code == 200:
                gl_data = gl_response.json()
                if gl_data.get('results'):
                    journal_entry = gl_data['results'][0]
                    print("✅ GL entries found!")
                    print(f"Journal Entry ID: {journal_entry['id']}")
                    print(f"Reference: {journal_entry['reference_number']}")
                    print(f"Total Amount: ${journal_entry['total_amount']}")
                    print(f"Status: {journal_entry['status']}")
                    
                    # Get journal entry lines
                    lines_response = requests.get(f"{API_BASE}/gl/journal-entries/{journal_entry['id']}/", headers=HEADERS, timeout=5)
                    if lines_response.status_code == 200:
                        entry_detail = lines_response.json()
                        if entry_detail.get('journal_lines'):
                            print(f"\nJournal Entry Lines ({len(entry_detail['journal_lines'])}):")
                            for line in entry_detail['journal_lines']:
                                account_name = line.get('account_name', 'Unknown Account')
                                debit = line.get('debit_amount', 0)
                                credit = line.get('credit_amount', 0)
                                description = line.get('description', '')
                                
                                if debit > 0:
                                    print(f"  DEBIT  ${debit:>8.2f} - {account_name} ({description})")
                                if credit > 0:
                                    print(f"  CREDIT ${credit:>8.2f} - {account_name} ({description})")
                        else:
                            print("❌ No journal entry lines found")
                    else:
                        print("❌ Failed to get journal entry details")
                else:
                    print("❌ No GL entries found for this vendor bill")
                    print("   GL entries should be created automatically when status is 'approved'")
            else:
                print(f"❌ Failed to check GL entries: {gl_response.status_code}")
            
            return True
        else:
            print(f"❌ FAILED! Status: {response.status_code}")
            try:
                error_data = response.json()
                print("Error details:")
                print(json.dumps(error_data, indent=2))
            except:
                print(f"Raw error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_gl_integration()
    
    print("\n" + "=" * 55)
    print("📋 GL INTEGRATION TEST SUMMARY")
    print("=" * 55)
    
    if success:
        print("🎉 GL INTEGRATION TEST COMPLETED!")
        print("✅ Check the results above to see if GL entries were created")
        print("✅ Expected GL entries:")
        print("   - DEBIT: Cost of Goods Sold ($200)")
        print("   - DEBIT: Services Expense ($200)")
        print("   - DEBIT: Sales Tax Payable ($40 - Input Tax)")
        print("   - CREDIT: Accounts Payable ($440)")
        print("\n💡 If GL entries were created, the integration is working!")
    else:
        print("❌ GL integration test failed")
        print("🔧 Check the error details above")
