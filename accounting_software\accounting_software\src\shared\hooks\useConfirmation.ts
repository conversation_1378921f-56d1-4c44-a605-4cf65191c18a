import { useState, useCallback } from 'react';
import { ConfirmationType } from '../components/ConfirmationDialog';

export interface ConfirmationOptions {
  title: string;
  message: string;
  type?: ConfirmationType;
  confirmText?: string;
  cancelText?: string;
  showCancel?: boolean;
  confirmColor?: 'primary' | 'secondary' | 'error' | 'warning' | 'info' | 'success';
  autoClose?: boolean;
  autoCloseDelay?: number;
}

export interface ConfirmationState extends ConfirmationOptions {
  open: boolean;
  onConfirm?: () => void;
}

export const useConfirmation = () => {
  const [confirmation, setConfirmation] = useState<ConfirmationState>({
    open: false,
    title: '',
    message: '',
    type: 'confirm',
  });

  const showConfirmation = useCallback((options: ConfirmationOptions): Promise<boolean> => {
    return new Promise((resolve) => {
      setConfirmation({
        ...options,
        open: true,
        onConfirm: () => {
          resolve(true);
          setConfirmation(prev => ({ ...prev, open: false }));
        },
      });
    });
  }, []);

  const showSuccess = useCallback((title: string, message: string, autoClose = true) => {
    setConfirmation({
      open: true,
      title,
      message,
      type: 'success',
      autoClose,
      autoCloseDelay: 3000,
      showCancel: false,
    });
  }, []);

  const showError = useCallback((title: string, message: string) => {
    setConfirmation({
      open: true,
      title,
      message,
      type: 'error',
      showCancel: false,
      confirmText: 'OK',
    });
  }, []);

  const showWarning = useCallback((title: string, message: string): Promise<boolean> => {
    return new Promise((resolve) => {
      setConfirmation({
        open: true,
        title,
        message,
        type: 'warning',
        confirmText: 'Continue',
        cancelText: 'Cancel',
        onConfirm: () => {
          resolve(true);
          setConfirmation(prev => ({ ...prev, open: false }));
        },
      });
    });
  }, []);

  const showInfo = useCallback((title: string, message: string, autoClose = true) => {
    setConfirmation({
      open: true,
      title,
      message,
      type: 'info',
      autoClose,
      autoCloseDelay: 3000,
      showCancel: false,
    });
  }, []);

  const hideConfirmation = useCallback(() => {
    setConfirmation(prev => ({ ...prev, open: false }));
  }, []);

  return {
    confirmation,
    showConfirmation,
    showSuccess,
    showError,
    showWarning,
    showInfo,
    hideConfirmation,
  };
};
