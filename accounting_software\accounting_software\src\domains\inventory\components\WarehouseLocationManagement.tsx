import React, { useState, useEffect } from 'react';
import {
  Box,
  Typo<PERSON>,
  Card,
  CardContent,
  Button,
  Alert,
  Grid,
  Chip,
  CircularProgress,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Tooltip,
  LinearProgress,
  FormControlLabel,
  Checkbox,
} from '@mui/material';
import {
  LocationOn as LocationIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Refresh as RefreshIcon,
  Warehouse as WarehouseIcon,
} from '@mui/icons-material';
import { inventoryService, WarehouseLocation, Warehouse } from '../services/inventory.service';

interface WarehouseLocationManagementProps {
  selectedWarehouse?: Warehouse;
  onLocationSelect?: (location: WarehouseLocation) => void;
}

const WarehouseLocationManagement: React.FC<WarehouseLocationManagementProps> = ({
  selectedWarehouse,
  onLocationSelect
}) => {
  const [locations, setLocations] = useState<WarehouseLocation[]>([]);
  const [warehouses, setWarehouses] = useState<Warehouse[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [warehouseFilter, setWarehouseFilter] = useState(selectedWarehouse?.warehouse_id || '');
  const [locationTypeFilter, setLocationTypeFilter] = useState('');
  const [activeFilter, setActiveFilter] = useState<boolean | ''>('');
  
  // Dialog states
  const [openDialog, setOpenDialog] = useState(false);
  const [dialogMode, setDialogMode] = useState<'create' | 'edit'>('create');
  const [selectedLocation, setSelectedLocation] = useState<WarehouseLocation | null>(null);
  
  // Form state
  const [formData, setFormData] = useState({
    warehouse: selectedWarehouse?.warehouse_id || '',
    zone: '',
    aisle: '',
    rack: '',
    bin: '',
    description: '',
    location_type: 'STANDARD' as const,
    max_capacity: '',
    is_active: true,
    allow_mixed_products: true,
  });

  useEffect(() => {
    loadWarehouses();
    loadLocations();
  }, []);

  useEffect(() => {
    loadLocations();
  }, [searchTerm, warehouseFilter, locationTypeFilter, activeFilter]);

  const loadWarehouses = async () => {
    try {
      const response = await inventoryService.getAllWarehouses();
      setWarehouses(response);
    } catch (err) {
      console.error('Failed to load warehouses:', err);
    }
  };

  const loadLocations = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await inventoryService.getAllWarehouseLocations({
        warehouse: warehouseFilter ? parseInt(warehouseFilter.toString()) : undefined,
        location_type: locationTypeFilter || undefined,
        is_active: activeFilter !== '' ? activeFilter : undefined,
        search: searchTerm || undefined,
      });
      
      setLocations(response.results || response);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load locations');
    } finally {
      setLoading(false);
    }
  };

  const handleOpenDialog = (mode: 'create' | 'edit', location?: WarehouseLocation) => {
    setDialogMode(mode);
    setSelectedLocation(location || null);
    
    if (location) {
      setFormData({
        warehouse: location.warehouse,
        zone: location.zone,
        aisle: location.aisle || '',
        rack: location.rack || '',
        bin: location.bin || '',
        description: location.description || '',
        location_type: location.location_type,
        max_capacity: location.max_capacity?.toString() || '',
        is_active: location.is_active,
        allow_mixed_products: location.allow_mixed_products,
      });
    } else {
      // Reset form for create mode
      setFormData({
        warehouse: selectedWarehouse?.warehouse_id || warehouseFilter || '',
        zone: '',
        aisle: '',
        rack: '',
        bin: '',
        description: '',
        location_type: 'STANDARD',
        max_capacity: '',
        is_active: true,
        allow_mixed_products: true,
      });
    }
    
    setOpenDialog(true);
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedLocation(null);
  };

  const handleSubmit = async () => {
    try {
      setLoading(true);
      
      const submitData = {
        ...formData,
        warehouse: parseInt(formData.warehouse.toString()),
        max_capacity: formData.max_capacity ? parseFloat(formData.max_capacity) : undefined,
      };
      
      if (dialogMode === 'create') {
        await inventoryService.createWarehouseLocation(submitData);
      } else if (dialogMode === 'edit' && selectedLocation) {
        await inventoryService.updateWarehouseLocation(selectedLocation.location_id, submitData);
      }
      
      handleCloseDialog();
      loadLocations();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save location');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (location: WarehouseLocation) => {
    if (window.confirm(`Are you sure you want to delete location "${location.location_code}"?`)) {
      try {
        setLoading(true);
        await inventoryService.deleteWarehouseLocation(location.location_id);
        loadLocations();
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to delete location');
      } finally {
        setLoading(false);
      }
    }
  };

  const getLocationTypeColor = (type: string) => {
    switch (type) {
      case 'STANDARD': return 'default';
      case 'BULK': return 'primary';
      case 'PICKING': return 'success';
      case 'RECEIVING': return 'info';
      case 'SHIPPING': return 'warning';
      case 'QUARANTINE': return 'error';
      case 'DAMAGED': return 'error';
      case 'RETURNS': return 'secondary';
      default: return 'default';
    }
  };

  return (
    <Box>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h5" component="h1">
          <LocationIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
          Warehouse Locations
          {selectedWarehouse && (
            <Typography variant="subtitle1" color="textSecondary" component="span" sx={{ ml: 2 }}>
              - {selectedWarehouse.name}
            </Typography>
          )}
        </Typography>
        <Box display="flex" gap={1}>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={loadLocations}
            disabled={loading}
          >
            Refresh
          </Button>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => handleOpenDialog('create')}
          >
            Add Location
          </Button>
        </Box>
      </Box>

      {/* Filters */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={3}>
              <TextField
                fullWidth
                label="Search locations"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                size="small"
              />
            </Grid>
            {!selectedWarehouse && (
              <Grid item xs={12} md={3}>
                <FormControl fullWidth size="small">
                  <InputLabel>Warehouse</InputLabel>
                  <Select
                    value={warehouseFilter}
                    onChange={(e) => setWarehouseFilter(e.target.value)}
                    label="Warehouse"
                  >
                    <MenuItem value="">All Warehouses</MenuItem>
                    {warehouses.map((warehouse) => (
                      <MenuItem key={warehouse.warehouse_id} value={warehouse.warehouse_id}>
                        {warehouse.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
            )}
            <Grid item xs={12} md={3}>
              <FormControl fullWidth size="small">
                <InputLabel>Location Type</InputLabel>
                <Select
                  value={locationTypeFilter}
                  onChange={(e) => setLocationTypeFilter(e.target.value)}
                  label="Location Type"
                >
                  <MenuItem value="">All Types</MenuItem>
                  <MenuItem value="STANDARD">Standard Storage</MenuItem>
                  <MenuItem value="BULK">Bulk Storage</MenuItem>
                  <MenuItem value="PICKING">Picking Location</MenuItem>
                  <MenuItem value="RECEIVING">Receiving Area</MenuItem>
                  <MenuItem value="SHIPPING">Shipping Area</MenuItem>
                  <MenuItem value="QUARANTINE">Quarantine Area</MenuItem>
                  <MenuItem value="DAMAGED">Damaged Goods</MenuItem>
                  <MenuItem value="RETURNS">Returns Area</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth size="small">
                <InputLabel>Status</InputLabel>
                <Select
                  value={activeFilter}
                  onChange={(e) => setActiveFilter(e.target.value as boolean | '')}
                  label="Status"
                >
                  <MenuItem value="">All Status</MenuItem>
                  <MenuItem value={true}>Active</MenuItem>
                  <MenuItem value={false}>Inactive</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Locations Table */}
      <Card>
        <CardContent>
          {loading ? (
            <Box display="flex" justifyContent="center" p={3}>
              <CircularProgress />
            </Box>
          ) : (
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Location Code</TableCell>
                    <TableCell>Warehouse</TableCell>
                    <TableCell>Zone</TableCell>
                    <TableCell>Details</TableCell>
                    <TableCell>Type</TableCell>
                    <TableCell>Utilization</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell align="right">Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {locations.map((location) => (
                    <TableRow 
                      key={location.location_id} 
                      hover
                      onClick={() => onLocationSelect?.(location)}
                      sx={{ cursor: onLocationSelect ? 'pointer' : 'default' }}
                    >
                      <TableCell>
                        <Typography variant="subtitle2" fontFamily="monospace">
                          {location.location_code}
                        </Typography>
                        {location.description && (
                          <Typography variant="caption" color="textSecondary">
                            {location.description}
                          </Typography>
                        )}
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {location.warehouse_name}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" fontWeight="bold">
                          {location.zone}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="caption" color="textSecondary">
                          {[location.aisle, location.rack, location.bin].filter(Boolean).join(' - ') || 'No details'}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={location.location_type}
                          color={getLocationTypeColor(location.location_type) as any}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Box sx={{ width: '100px' }}>
                          <LinearProgress
                            variant="determinate"
                            value={location.current_utilization_percent}
                            sx={{ mb: 0.5 }}
                          />
                          <Typography variant="caption">
                            {location.current_utilization_percent.toFixed(1)}%
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={location.is_active ? 'Active' : 'Inactive'}
                          color={location.is_active ? 'success' : 'default'}
                          size="small"
                        />
                      </TableCell>
                      <TableCell align="right">
                        <Tooltip title="Edit">
                          <IconButton
                            size="small"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleOpenDialog('edit', location);
                            }}
                          >
                            <EditIcon />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Delete">
                          <IconButton
                            size="small"
                            color="error"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDelete(location);
                            }}
                          >
                            <DeleteIcon />
                          </IconButton>
                        </Tooltip>
                      </TableCell>
                    </TableRow>
                  ))}
                  {locations.length === 0 && !loading && (
                    <TableRow>
                      <TableCell colSpan={8} align="center">
                        <Typography color="textSecondary">
                          No locations found
                        </Typography>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </CardContent>
      </Card>

      {/* Location Dialog */}
      <Dialog
        open={openDialog}
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          {dialogMode === 'create' ? 'Create New Location' : 'Edit Location'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <FormControl fullWidth required>
                  <InputLabel>Warehouse</InputLabel>
                  <Select
                    value={formData.warehouse}
                    onChange={(e) => setFormData({ ...formData, warehouse: e.target.value })}
                    label="Warehouse"
                    disabled={!!selectedWarehouse}
                  >
                    {warehouses.map((warehouse) => (
                      <MenuItem key={warehouse.warehouse_id} value={warehouse.warehouse_id}>
                        {warehouse.name} ({warehouse.warehouse_code})
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
              
              <Grid item xs={12} md={3}>
                <TextField
                  fullWidth
                  label="Zone"
                  value={formData.zone}
                  onChange={(e) => setFormData({ ...formData, zone: e.target.value })}
                  required
                  placeholder="A, B, C..."
                />
              </Grid>
              <Grid item xs={12} md={3}>
                <TextField
                  fullWidth
                  label="Aisle"
                  value={formData.aisle}
                  onChange={(e) => setFormData({ ...formData, aisle: e.target.value })}
                  placeholder="01, 02, 03..."
                />
              </Grid>
              <Grid item xs={12} md={3}>
                <TextField
                  fullWidth
                  label="Rack"
                  value={formData.rack}
                  onChange={(e) => setFormData({ ...formData, rack: e.target.value })}
                  placeholder="R1, R2, R3..."
                />
              </Grid>
              <Grid item xs={12} md={3}>
                <TextField
                  fullWidth
                  label="Bin"
                  value={formData.bin}
                  onChange={(e) => setFormData({ ...formData, bin: e.target.value })}
                  placeholder="B01, B02..."
                />
              </Grid>
              
              <Grid item xs={12} md={6}>
                <FormControl fullWidth required>
                  <InputLabel>Location Type</InputLabel>
                  <Select
                    value={formData.location_type}
                    onChange={(e) => setFormData({ ...formData, location_type: e.target.value as any })}
                    label="Location Type"
                  >
                    <MenuItem value="STANDARD">Standard Storage</MenuItem>
                    <MenuItem value="BULK">Bulk Storage</MenuItem>
                    <MenuItem value="PICKING">Picking Location</MenuItem>
                    <MenuItem value="RECEIVING">Receiving Area</MenuItem>
                    <MenuItem value="SHIPPING">Shipping Area</MenuItem>
                    <MenuItem value="QUARANTINE">Quarantine Area</MenuItem>
                    <MenuItem value="DAMAGED">Damaged Goods</MenuItem>
                    <MenuItem value="RETURNS">Returns Area</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Max Capacity"
                  type="number"
                  value={formData.max_capacity}
                  onChange={(e) => setFormData({ ...formData, max_capacity: e.target.value })}
                  placeholder="Optional"
                />
              </Grid>
              
              <Grid item xs={12}>
                <TextField
                  fullWidth
                  label="Description"
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  multiline
                  rows={2}
                  placeholder="Optional description"
                />
              </Grid>
              
              <Grid item xs={12}>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={formData.is_active}
                      onChange={(e) => setFormData({ ...formData, is_active: e.target.checked })}
                    />
                  }
                  label="Active location"
                />
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={formData.allow_mixed_products}
                      onChange={(e) => setFormData({ ...formData, allow_mixed_products: e.target.checked })}
                    />
                  }
                  label="Allow mixed products"
                />
              </Grid>
            </Grid>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button
            variant="contained"
            onClick={handleSubmit}
            disabled={loading || !formData.warehouse || !formData.zone}
          >
            {dialogMode === 'create' ? 'Create' : 'Update'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default WarehouseLocationManagement;
