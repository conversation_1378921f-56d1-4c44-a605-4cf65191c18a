import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Card,
  CardContent,
  CardHeader,
  Divider,
  Grid,
  TextField,
  Typography,
  Avatar,
  Snackbar,
  Alert,
  CircularProgress,
  Backdrop,
  Chip,
  Paper,
} from '@mui/material';
import { PhotoCamera, Save, Refresh, Edit, Lock } from '@mui/icons-material';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { PageContainer } from '../../../layouts/components/PageComponents';
import { useCompany } from '../../../contexts/CompanyContext';
import { useAuth } from '../../../contexts/AuthContext';
import { CURRENCIES } from '../../../shared/constants/currencies';
import SearchableSelect from '../../../shared/components/SearchableSelect';

// Validation schema for company information
const validationSchema = Yup.object({
  companyName: Yup.string()
    .required('Company name is required')
    .min(2, 'Company name must be at least 2 characters'),
  email: Yup.string()
    .email('Invalid email address')
    .required('Email is required'),
  phone: Yup.string()
    .required('Phone number is required')
    .min(10, 'Phone number must be at least 10 digits'),
  address: Yup.string()
    .required('Address is required')
    .min(5, 'Address must be at least 5 characters'),
  city: Yup.string()
    .required('City is required'),
  state: Yup.string()
    .required('State/Province is required'),
  postalCode: Yup.string()
    .required('Postal code is required'),
  country: Yup.string()
    .required('Country is required'),
  taxId: Yup.string()
    .required('Tax ID is required'),
  functionalCurrency: Yup.string()
    .required('Functional currency is required'),
  reportingCurrency: Yup.string()
    .required('Reporting currency is required'),
});

const CompanySettingsPage: React.FC = () => {
  console.log('CompanySettingsPage: Component loaded');
  
  // Get company context and auth context
  const { companyInfo, updateCompanyInfo, loading, error, fetchCompanyInfo, clearError } = useCompany();
  const { user } = useAuth();
  
  console.log('CompanySettingsPage: Company info:', companyInfo);
  console.log('CompanySettingsPage: Loading:', loading);
  console.log('CompanySettingsPage: Error:', error);
  
  // Local state for UI management
  const [logoPreview, setLogoPreview] = useState<string | null>(null);
  const [isEditing, setIsEditing] = useState(false);
  const [submitLoading, setSubmitLoading] = useState(false);

  // Check if user is superuser (can edit company information)
  const canEdit = user?.is_superuser || false;

  // Initialize formik with company data
  const formik = useFormik({
    initialValues: {
      companyName: '',
      address: '',
      city: '',
      state: '',
      postalCode: '',
      country: '',
      phone: '',
      email: '',
      website: '',
      taxId: '',
      functionalCurrency: 'USD',
      reportingCurrency: 'USD',
    },
    validationSchema,
    onSubmit: async (values) => {
      if (!canEdit) {
        return;
      }

      try {
        setSubmitLoading(true);
        await updateCompanyInfo(values);
        setIsEditing(false);
      } catch (err) {
        console.error('Error updating company:', err);
      } finally {
        setSubmitLoading(false);
      }
    },
  });

  // Update formik values when company info changes
  useEffect(() => {
    if (companyInfo) {
      formik.setValues({
        companyName: companyInfo.companyName || '',
        address: companyInfo.address || '',
        city: companyInfo.city || '',
        state: companyInfo.state || '',
        postalCode: companyInfo.postalCode || '',
        country: companyInfo.country || '',
        phone: companyInfo.phone || '',
        email: companyInfo.email || '',
        website: companyInfo.website || '',
        taxId: companyInfo.taxId || '',
        functionalCurrency: companyInfo.functionalCurrency || 'USD',
        reportingCurrency: companyInfo.reportingCurrency || 'USD',
      });
      setLogoPreview(companyInfo.logo || null);
    }
  }, [companyInfo]);

  // Handle logo upload
  const handleLogoChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (!canEdit || !isEditing) return;

    const file = event.target.files?.[0];
    if (file) {
      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        alert('File size must be less than 5MB');
        return;
      }

      // Validate file type
      if (!file.type.startsWith('image/')) {
        alert('Please select an image file');
        return;
      }

      const reader = new FileReader();
      reader.onload = () => {
        setLogoPreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  // Handle refresh company data
  const handleRefresh = async () => {
    try {
      await fetchCompanyInfo();
    } catch (err) {
      console.error('Error refreshing company data:', err);
    }
  };

  // Handle edit mode toggle
  const handleEditToggle = () => {
    if (!canEdit) return;
    
    if (isEditing) {
      // Cancel editing - reset form
      if (companyInfo) {
        formik.setValues({
          companyName: companyInfo.companyName || '',
          address: companyInfo.address || '',
          city: companyInfo.city || '',
          state: companyInfo.state || '',
          postalCode: companyInfo.postalCode || '',
          country: companyInfo.country || '',
          phone: companyInfo.phone || '',
          email: companyInfo.email || '',
          website: companyInfo.website || '',
          taxId: companyInfo.taxId || '',
          functionalCurrency: companyInfo.functionalCurrency || 'USD',
          reportingCurrency: companyInfo.reportingCurrency || 'USD',
        });
        setLogoPreview(companyInfo.logo || null);
      }
    }
    setIsEditing(!isEditing);
  };

  // Show loading state while fetching company data
      if (loading && !companyInfo) {
      console.log('CompanySettingsPage: Showing loading state');
      return (
    <PageContainer title="Company Settings">
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
          <CircularProgress size={60} />
          <Typography variant="h6" sx={{ ml: 2 }}>
            Loading company information...
          </Typography>
        </Box>
      </PageContainer>
    );
  }

  // Show error state if no company data and there's an error
  if (!companyInfo && error) {
    console.log('CompanySettingsPage: Showing error state:', error);
    return (
      <PageContainer title="Company Settings">
        <Paper sx={{ p: 4, textAlign: 'center' }}>
          <Typography variant="h6" color="error" gutterBottom>
            Error Loading Company Information
          </Typography>
          <Typography variant="body1" color="text.secondary" gutterBottom>
            {error}
          </Typography>
          <Button
            variant="contained"
            startIcon={<Refresh />}
            onClick={handleRefresh}
            sx={{ mt: 2 }}
          >
            Try Again
          </Button>
        </Paper>
      </PageContainer>
    );
  }

  // Show message if no company exists
  if (!companyInfo) {
    console.log('CompanySettingsPage: Showing no company message');
    return (
      <PageContainer title="Company Settings">
        <Paper sx={{ p: 4, textAlign: 'center' }}>
          <Typography variant="h6" gutterBottom>
            No Company Information Found
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Please create a company first before accessing settings.
          </Typography>
        </Paper>
      </PageContainer>
    );
  }

  console.log('CompanySettingsPage: Rendering main component with company info');
  
  return (
    <PageContainer title="Company Settings">
      {/* Loading overlay for form submission */}
      <Backdrop open={submitLoading} sx={{ zIndex: 1300 }}>
        <Box display="flex" flexDirection="column" alignItems="center">
          <CircularProgress color="primary" size={60} />
          <Typography variant="h6" sx={{ mt: 2, color: 'white' }}>
            Updating company information...
          </Typography>
        </Box>
      </Backdrop>

      <form onSubmit={formik.handleSubmit}>
        {/* Page Header with Actions */}
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
          <Box>
            <Typography variant="h4" gutterBottom>
              Company Settings
            </Typography>
            <Box display="flex" gap={1} alignItems="center">
              {canEdit ? (
                <Chip
                  icon={<Edit />}
                  label="Edit Access"
                  color="success"
                  variant="outlined"
                />
              ) : (
                <Chip
                  icon={<Lock />}
                  label="View Only"
                  color="default"
                  variant="outlined"
                />
              )}
              <Typography variant="body2" color="text.secondary">
                Last updated: {companyInfo.updatedAt ? new Date(companyInfo.updatedAt).toLocaleDateString() : 'Unknown'}
              </Typography>
            </Box>
          </Box>
          
          <Box display="flex" gap={2}>
            <Button
              variant="outlined"
              startIcon={<Refresh />}
              onClick={handleRefresh}
              disabled={loading}
            >
              Refresh
            </Button>
            
            {canEdit && (
              <>
                {!isEditing ? (
                  <Button
                    variant="contained"
                    startIcon={<Edit />}
                    onClick={handleEditToggle}
                  >
                    Edit Company
                  </Button>
                ) : (
                  <>
                    <Button
                      variant="outlined"
                      onClick={handleEditToggle}
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      variant="contained"
                      startIcon={<Save />}
                      disabled={submitLoading}
                    >
                      Save Changes
                    </Button>
                  </>
                )}
              </>
            )}
          </Box>
        </Box>

        <Grid container spacing={3}>
          {/* Company Logo */}
          <Grid item xs={12}>
            <Card>
              <CardHeader 
                title="Company Logo" 
                subheader={isEditing ? "Click to change logo" : "Company brand image"}
              />
              <CardContent>
                <Box display="flex" alignItems="center" gap={3}>
                  <Avatar
                    src={logoPreview || undefined}
                    alt={formik.values.companyName}
                    sx={{ width: 120, height: 120 }}
                  />
                  <Box>
                    {canEdit && isEditing && (
                      <>
                        <input
                          accept="image/*"
                          style={{ display: 'none' }}
                          id="logo-upload"
                          type="file"
                          onChange={handleLogoChange}
                        />
                        <label htmlFor="logo-upload">
                          <Button
                            variant="outlined"
                            component="span"
                            startIcon={<PhotoCamera />}
                          >
                            Upload New Logo
                          </Button>
                        </label>
                      </>
                    )}
                    <Typography variant="caption" display="block" sx={{ mt: 1 }}>
                      Recommended size: 200x200 pixels (max 5MB)
                    </Typography>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>

          {/* Company Information */}
          <Grid item xs={12}>
            <Card>
              <CardHeader title="Company Information" />
              <Divider />
              <CardContent>
                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Company Name"
                      name="companyName"
                      value={formik.values.companyName}
                      onChange={formik.handleChange}
                      error={formik.touched.companyName && Boolean(formik.errors.companyName)}
                      helperText={formik.touched.companyName && formik.errors.companyName}
                      disabled={!isEditing}
                      variant={isEditing ? "outlined" : "filled"}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Tax ID / VAT Number"
                      name="taxId"
                      value={formik.values.taxId}
                      onChange={formik.handleChange}
                      error={formik.touched.taxId && Boolean(formik.errors.taxId)}
                      helperText={formik.touched.taxId && formik.errors.taxId}
                      disabled={!isEditing}
                      variant={isEditing ? "outlined" : "filled"}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Email"
                      name="email"
                      value={formik.values.email}
                      onChange={formik.handleChange}
                      error={formik.touched.email && Boolean(formik.errors.email)}
                      helperText={formik.touched.email && formik.errors.email}
                      disabled={!isEditing}
                      variant={isEditing ? "outlined" : "filled"}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Phone"
                      name="phone"
                      value={formik.values.phone}
                      onChange={formik.handleChange}
                      error={formik.touched.phone && Boolean(formik.errors.phone)}
                      helperText={formik.touched.phone && formik.errors.phone}
                      disabled={!isEditing}
                      variant={isEditing ? "outlined" : "filled"}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Website"
                      name="website"
                      value={formik.values.website}
                      onChange={formik.handleChange}
                      disabled={!isEditing}
                      variant={isEditing ? "outlined" : "filled"}
                    />
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          {/* Address Information */}
          <Grid item xs={12}>
            <Card>
              <CardHeader title="Address Information" />
              <Divider />
              <CardContent>
                <Grid container spacing={3}>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Street Address"
                      name="address"
                      value={formik.values.address}
                      onChange={formik.handleChange}
                      error={formik.touched.address && Boolean(formik.errors.address)}
                      helperText={formik.touched.address && formik.errors.address}
                      disabled={!isEditing}
                      variant={isEditing ? "outlined" : "filled"}
                      multiline
                      rows={2}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="City"
                      name="city"
                      value={formik.values.city}
                      onChange={formik.handleChange}
                      error={formik.touched.city && Boolean(formik.errors.city)}
                      helperText={formik.touched.city && formik.errors.city}
                      disabled={!isEditing}
                      variant={isEditing ? "outlined" : "filled"}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="State / Province"
                      name="state"
                      value={formik.values.state}
                      onChange={formik.handleChange}
                      error={formik.touched.state && Boolean(formik.errors.state)}
                      helperText={formik.touched.state && formik.errors.state}
                      disabled={!isEditing}
                      variant={isEditing ? "outlined" : "filled"}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Postal Code"
                      name="postalCode"
                      value={formik.values.postalCode}
                      onChange={formik.handleChange}
                      error={formik.touched.postalCode && Boolean(formik.errors.postalCode)}
                      helperText={formik.touched.postalCode && formik.errors.postalCode}
                      disabled={!isEditing}
                      variant={isEditing ? "outlined" : "filled"}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Country"
                      name="country"
                      value={formik.values.country}
                      onChange={formik.handleChange}
                      error={formik.touched.country && Boolean(formik.errors.country)}
                      helperText={formik.touched.country && formik.errors.country}
                      disabled={!isEditing}
                      variant={isEditing ? "outlined" : "filled"}
                    />
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          {/* Currency Information - ENHANCED SECTION */}
          <Grid item xs={12}>
            <Card>
              <CardHeader 
                title="Currency Settings" 
                subheader="Configure your business and reporting currencies"
              />
              <Divider />
              <CardContent>
                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    {isEditing ? (
                      <SearchableSelect
                        options={CURRENCIES.map(curr => ({ 
                          label: `${curr.name} (${curr.code})`, 
                          value: curr.code 
                        }))}
                        value={CURRENCIES.map(curr => ({ 
                          label: `${curr.name} (${curr.code})`, 
                          value: curr.code 
                        })).find(option => option.value === formik.values.functionalCurrency) || null}
                        onChange={(newValue) => {
                          formik.setFieldValue('functionalCurrency', newValue ? newValue.value : 'USD');
                        }}
                        getOptionLabel={(option) => option.label}
                        label="Functional Currency"
                        error={formik.touched.functionalCurrency && Boolean(formik.errors.functionalCurrency)}
                        helperText={formik.touched.functionalCurrency ? 
                          formik.errors.functionalCurrency as string : 
                          'Primary currency for daily operations'
                        }
                      />
                    ) : (
                      <TextField
                        fullWidth
                        label="Functional Currency"
                        value={CURRENCIES.find(c => c.code === formik.values.functionalCurrency)?.name + 
                               ` (${formik.values.functionalCurrency})` || formik.values.functionalCurrency}
                        disabled
                        variant="filled"
                        helperText="Primary currency for daily operations"
                      />
                    )}
                  </Grid>
                  <Grid item xs={12} md={6}>
                    {isEditing ? (
                      <SearchableSelect
                        options={CURRENCIES.map(curr => ({ 
                          label: `${curr.name} (${curr.code})`, 
                          value: curr.code 
                        }))}
                        value={CURRENCIES.map(curr => ({ 
                          label: `${curr.name} (${curr.code})`, 
                          value: curr.code 
                        })).find(option => option.value === formik.values.reportingCurrency) || null}
                        onChange={(newValue) => {
                          formik.setFieldValue('reportingCurrency', newValue ? newValue.value : 'USD');
                        }}
                        getOptionLabel={(option) => option.label}
                        label="Reporting Currency"
                        error={formik.touched.reportingCurrency && Boolean(formik.errors.reportingCurrency)}
                        helperText={formik.touched.reportingCurrency ? 
                          formik.errors.reportingCurrency as string : 
                          'Currency for financial reporting'
                        }
                      />
                    ) : (
                      <TextField
                        fullWidth
                        label="Reporting Currency"
                        value={CURRENCIES.find(c => c.code === formik.values.reportingCurrency)?.name + 
                               ` (${formik.values.reportingCurrency})` || formik.values.reportingCurrency}
                        disabled
                        variant="filled"
                        helperText="Currency for financial reporting"
                      />
                    )}
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </form>

      {/* Error Snackbar */}
      <Snackbar
        open={!!error}
        autoHideDuration={6000}
        onClose={clearError}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert onClose={clearError} severity="error">
          {error}
        </Alert>
      </Snackbar>
    </PageContainer>
  );
};

export default CompanySettingsPage; 