#!/usr/bin/env python3
"""
Comprehensive test script for Vendor Bills backend functionality
Tests all the ERP features according to the specification
"""
import requests
import json
import sys
from datetime import datetime, timedelta

# Configuration
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api"

# Test data
TEST_VENDOR_BILL = {
    "vendor": 1,  # Assuming vendor with ID 1 exists
    "bill_date": datetime.now().strftime('%Y-%m-%d'),
    "due_date": (datetime.now() + timedelta(days=30)).strftime('%Y-%m-%d'),
    "status": "draft",
    "payment_terms": "Net 30",
    "reference_number": "TEST-BILL-001",
    "notes": "Test vendor bill for comprehensive testing",
    "line_items": [
        {
            "item_description": "Test Product 1",
            "quantity": 10,
            "unit_price": 100.00,
            "tax_rate": 10.0,
            "account_code": "5010-COGS"
        },
        {
            "item_description": "Test Service 1",
            "quantity": 5,
            "unit_price": 200.00,
            "tax_rate": 10.0,
            "account_code": "5020-Services"
        }
    ]
}

def test_endpoint(method, endpoint, data=None, description=""):
    """Test an API endpoint"""
    print(f"\n{'='*60}")
    print(f"Testing: {method} {endpoint}")
    print(f"Description: {description}")
    print(f"{'='*60}")
    
    try:
        url = f"{API_BASE}{endpoint}"
        
        if method == "GET":
            response = requests.get(url)
        elif method == "POST":
            response = requests.post(url, json=data, headers={'Content-Type': 'application/json'})
        elif method == "PUT":
            response = requests.put(url, json=data, headers={'Content-Type': 'application/json'})
        elif method == "PATCH":
            response = requests.patch(url, json=data, headers={'Content-Type': 'application/json'})
        elif method == "DELETE":
            response = requests.delete(url)
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200 or response.status_code == 201:
            print("✅ SUCCESS")
            try:
                response_data = response.json()
                if isinstance(response_data, dict):
                    if 'results' in response_data:
                        print(f"Results count: {len(response_data['results'])}")
                        if response_data['results']:
                            print("Sample result keys:", list(response_data['results'][0].keys()))
                    else:
                        print("Response keys:", list(response_data.keys()))
                elif isinstance(response_data, list):
                    print(f"List length: {len(response_data)}")
                    if response_data:
                        print("Sample item keys:", list(response_data[0].keys()))
                
                return response_data
            except:
                print("Response:", response.text[:200])
                return response.text
        else:
            print("❌ FAILED")
            print("Error:", response.text[:200])
            return None
            
    except Exception as e:
        print(f"❌ EXCEPTION: {e}")
        return None

def main():
    print("🚀 COMPREHENSIVE VENDOR BILLS BACKEND TEST")
    print("=" * 60)
    
    # Test 1: Basic vendor bills endpoints
    test_endpoint("GET", "/purchase/vendor-bills/", description="List all vendor bills")
    test_endpoint("GET", "/purchase/vendor-bills/stats/", description="Get vendor bills statistics")
    
    # Test 2: Create vendor bill
    created_bill = test_endpoint("POST", "/purchase/vendor-bills/", TEST_VENDOR_BILL, "Create new vendor bill")
    
    if created_bill and 'id' in created_bill:
        bill_id = created_bill['id']
        print(f"\n✅ Created vendor bill with ID: {bill_id}")
        
        # Test 3: Retrieve created bill
        test_endpoint("GET", f"/purchase/vendor-bills/{bill_id}/", description="Get specific vendor bill")
        
        # Test 4: Update vendor bill
        update_data = {
            "status": "approved",
            "notes": "Updated test bill - approved for payment"
        }
        test_endpoint("PATCH", f"/purchase/vendor-bills/{bill_id}/", update_data, "Update vendor bill status")
        
        # Test 5: Mark as paid
        payment_data = {"payment_amount": created_bill.get('total_amount', 0)}
        test_endpoint("POST", f"/purchase/vendor-bills/{bill_id}/mark-paid/", payment_data, "Mark bill as paid")
        
        # Test 6: Duplicate bill
        test_endpoint("POST", f"/purchase/vendor-bills/{bill_id}/duplicate/", description="Duplicate vendor bill")
        
    # Test 7: Test vendor bills creation scenarios
    print(f"\n{'='*60}")
    print("TESTING BILL CREATION SCENARIOS")
    print(f"{'='*60}")
    
    # Test creating from GRN (will fail if no GRN exists, but tests endpoint)
    test_endpoint("POST", "/purchase/vendor-bills/create-from-grn/", {"grn_id": 1}, "Create bill from GRN")
    
    # Test creating from PO (will fail if no PO exists, but tests endpoint)
    test_endpoint("POST", "/purchase/vendor-bills/create-from-po/", {"po_id": 1}, "Create bill from Purchase Order")
    
    # Test creating from return note (will fail if no return note exists, but tests endpoint)
    test_endpoint("POST", "/purchase/vendor-bills/create-from-return-note/", {"return_note_id": 1}, "Create bill from Return Note")
    
    # Test 8: Summary endpoint
    test_endpoint("GET", "/purchase/vendor-bills/summary/", description="Get vendor bills summary")
    
    # Test 9: Test vendors endpoint
    test_endpoint("GET", "/purchase/vendors/", description="List vendors for dropdown")
    
    print(f"\n{'='*60}")
    print("🎯 TEST SUMMARY")
    print(f"{'='*60}")
    print("✅ All endpoint tests completed")
    print("📋 Check the results above for any failures")
    print("🔧 Backend is ready for frontend integration")
    print(f"{'='*60}")

if __name__ == "__main__":
    main()
