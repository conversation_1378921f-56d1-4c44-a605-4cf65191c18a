import React, { useState, useEffect, useCallback } from 'react';
import {
  Box,
  Button,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TablePagination,
  TableRow,
  TextField,
  Typography,
  Chip,
  IconButton,
  Tooltip,
  Grid,
  CircularProgress,
  Dialog,
  Alert,
} from '@mui/material';
import {
  Add as AddIcon,
  Search as SearchIcon,
  Visibility as VisibilityIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  FilterList as FilterListIcon,
  GetApp as GetAppIcon,
  Receipt as ReceiptIcon,
  Send as SendIcon,
} from '@mui/icons-material';
import { PageContainer } from '../../../layouts/components/PageComponents';
import { purchaseOrderService, PurchaseOrder } from '../../../services/purchaseOrder.service';
import PurchaseOrderForm from '../components/PurchaseOrderForm';
import { useCurrencyInfo } from '../../gl/hooks/useCurrencyInfo';

const PurchaseOrdersPage: React.FC = () => {
  const { currencyInfo } = useCurrencyInfo();
  const [purchaseOrders, setPurchaseOrders] = useState<PurchaseOrder[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [successMessage, setSuccessMessage] = useState<string | null>(null);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');
  const [poFormOpen, setPoFormOpen] = useState(false);
  const [selectedPO, setSelectedPO] = useState<any>(null);

  // Format currency with proper IFRS compliance and comma separators
  const formatCurrency = (amount: number) => {
    if (!currencyInfo) return '0.00';
    
    const currency = currencyInfo.functional_currency;
    const symbol = currencyInfo.functional_currency_symbol;
    
    // Use proper international number formatting with commas
    const formatted = new Intl.NumberFormat('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount);
    
    return `${symbol}${formatted}`;
  };

  // Load purchase orders
  const loadPurchaseOrders = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      const data = await purchaseOrderService.getPurchaseOrders();
      setPurchaseOrders(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load purchase orders');
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    loadPurchaseOrders();
  }, [loadPurchaseOrders]);

  // Debug currency info when it loads
  useEffect(() => {
    if (currencyInfo) {
      console.log('💰 Currency Info Loaded:', {
        functional_currency: currencyInfo.functional_currency,
        functional_currency_symbol: currencyInfo.functional_currency_symbol,
        company_name: currencyInfo.company_name
      });
    }
  }, [currencyInfo]);

  // Filter purchase orders based on search term
  const filteredPurchaseOrders = purchaseOrders.filter(
    (po) =>
      po.po_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
      po.vendor_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      po.line_items?.some(item => 
        item.description.toLowerCase().includes(searchTerm.toLowerCase())
      )
  );

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
    setPage(0);
  };

  const handleDeletePurchaseOrder = async (poId: number) => {
    if (window.confirm('Are you sure you want to delete this purchase order?')) {
      try {
        await purchaseOrderService.deletePurchaseOrder(poId);
        await loadPurchaseOrders(); // Reload the list
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to delete purchase order');
      }
    }
  };

  const handleSendPurchaseOrder = async (poId: number) => {
    if (window.confirm('Are you sure you want to send this purchase order to the vendor?')) {
      try {
        await purchaseOrderService.sendPurchaseOrder(poId);
        await loadPurchaseOrders(); // Reload the list to show updated status
        setSuccessMessage('Purchase order sent successfully');
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to send purchase order');
      }
    }
  };

  const handleSavePurchaseOrder = async () => {
    await loadPurchaseOrders(); // Reload the list after save
    setPoFormOpen(false);
    setSelectedPO(null);
  };

  const handleViewPurchaseOrder = (po: PurchaseOrder) => {
    console.log('Raw PO data from API:', po);
    console.log('Line items raw data:', po.line_items);
    
    // Transform PO data to match form structure for viewing
    const poForForm = {
      id: po.id,
      poNumber: po.po_number,
      vendor: po.vendor,
      vendorName: po.vendor_name || '',
      poDate: po.po_date,
      expectedDeliveryDate: po.expected_date || '',
      referenceNumber: po.reference_number || '',
      memo: po.memo || '',
      shipToAddress: po.ship_to_address || '',
      paymentTerms: po.payment_terms || 'net_30',
      currency: po.currency || 'INR',
      status: po.status || 'draft',
      // Add buyer information if available
      buyerName: po.buyer_name || '',
      buyerEmail: po.buyer_email || '',
      buyerPhone: po.buyer_phone || '',
      // Add financial information - ensure proper number parsing
      subtotal: parseFloat(po.subtotal?.toString() || '0'),
      discountPercent: parseFloat(po.discount_percent?.toString() || '0'),
      discountAmount: parseFloat(po.discount_amount?.toString() || '0'),
      taxAmount: parseFloat(po.tax_amount?.toString() || '0'),
      totalAmount: parseFloat(po.total_amount?.toString() || '0'),
      // Transform line items with all required fields
      lineItems: po.line_items?.map((item, index) => {
        console.log(`Line item ${index + 1} raw data:`, item);
        return {
          id: `line-${index}`,
          product_id: item.product || null,
          product_name: item.description || '',
          description: item.description || '',
          quantity: parseFloat(item.quantity?.toString() || '0'),
          unit_of_measure: item.unit_of_measure || 'pcs',
          unit_price: parseFloat(item.unit_price?.toString() || '0'),
          discount_percent: parseFloat(item.discount_percent?.toString() || '0'),
          line_total: parseFloat(item.line_total?.toString() || '0'),
          taxable: Boolean(item.taxable),
          tax_rate: parseFloat(item.tax_rate?.toString() || '0'),
          sales_tax_amount: parseFloat(item.tax_amount?.toString() || '0'),
          sales_tax: item.taxable && item.tax_rate > 0 ? `tax-${item.tax_rate}` : null,
          sales_tax_rate: parseFloat(item.tax_rate?.toString() || '0'),
          notes: item.notes || '',
          // Required for JournalLineItem interface
          account_id: item.product || 0,
          account_name: item.description || '',
          memo: '',
          debit_amount: 0,
          credit_amount: 0,
        };
      }) || [],
      viewMode: true, // Add view mode flag
    };
    
    console.log('Transformed PO for form:', poForForm);
    console.log('Transformed line items:', poForForm.lineItems);
    setSelectedPO(poForForm);
    setPoFormOpen(true);
  };

  const handleEditPurchaseOrder = (po: PurchaseOrder) => {
    // Transform PO data to match form structure
    const poForForm = {
      id: po.id,
      vendor: po.vendor,
      vendorName: po.vendor_name || '',
      poDate: po.po_date,
      expectedDate: po.expected_date || '',
      referenceNumber: po.reference_number || '',
      memo: po.memo || '',
      shipToAddress: po.ship_to_address || '',
      paymentTerms: po.payment_terms || 'net_30',
      currency: po.currency || 'INR',
      lineItems: po.line_items?.map(item => ({
        description: item.description,
        quantity: item.quantity,
        unitPrice: item.unit_price,
        discountPercent: item.discount_percent,
        taxable: item.taxable,
        taxRate: item.tax_rate,
      })) || [],
    };
    
    setSelectedPO(poForForm);
    setPoFormOpen(true);
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'draft':
        return 'warning';
      case 'sent':
        return 'primary';
      case 'approved':
        return 'success';
      case 'pending':
        return 'warning';
      case 'received':
        return 'info';
      case 'cancelled':
        return 'error';
      default:
        return 'default';
    }
  };

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography color="error" variant="h6">
          Error: {error}
        </Typography>
      </Box>
    );
  }

  return (
    <PageContainer
      title="Purchase Orders"
      actions={
        <Button
          variant="contained"
          color="primary"
          startIcon={<AddIcon />}
          onClick={() => {
            setSelectedPO(null); // Clear any previous PO data
            setPoFormOpen(true);
          }}
        >
          Create Purchase Order
        </Button>
      }
    >
      {/* Success Message */}
      {successMessage && (
        <Alert 
          severity="success" 
          sx={{ mb: 3 }}
          onClose={() => setSuccessMessage(null)}
        >
          {successMessage}
        </Alert>
      )}

      {/* Error Message */}
      {error && (
        <Alert 
          severity="error" 
          sx={{ mb: 3 }}
          onClose={() => setError(null)}
        >
          {error}
        </Alert>
      )}

      {/* Search and Filter */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              placeholder="Search purchase orders by PO#, vendor, or product name..."
              value={searchTerm}
              onChange={handleSearchChange}
              InputProps={{
                startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />,
              }}
              variant="outlined"
              size="small"
            />
          </Grid>
          <Grid item xs={12} md={6} sx={{ display: 'flex', justifyContent: { xs: 'flex-start', md: 'flex-end' } }}>
            <Button startIcon={<FilterListIcon />} sx={{ mr: 1 }}>
              Filter
            </Button>
            <Button startIcon={<GetAppIcon />}>
              Export
            </Button>
          </Grid>
        </Grid>
      </Paper>

      {/* Purchase Orders Table */}
      <Paper>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>PO #</TableCell>
                <TableCell>Vendor</TableCell>
                <TableCell>Products</TableCell>
                <TableCell>Date</TableCell>
                <TableCell>Expected Date</TableCell>
                <TableCell align="right">Amount</TableCell>
                <TableCell>Status</TableCell>
                <TableCell align="center">Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {filteredPurchaseOrders
                .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                .map((po) => (
                  <TableRow key={po.id} hover>
                    <TableCell>{po.po_number}</TableCell>
                    <TableCell>{po.vendor_name}</TableCell>
                    <TableCell>
                      <Box sx={{ maxWidth: 200 }}>
                        {po.line_items && po.line_items.length > 0 ? (
                          <Typography variant="body2" sx={{ fontSize: '0.875rem' }}>
                            {po.line_items.slice(0, 3).map((item, index) => (
                              <span key={index}>
                                {item.description}
                                {index < Math.min(po.line_items!.length - 1, 2) && ', '}
                              </span>
                            ))}
                            {po.line_items.length > 3 && (
                              <Tooltip title={po.line_items.slice(3).map(item => item.description).join(', ')}>
                                <Typography component="span" sx={{ color: 'primary.main', cursor: 'pointer', fontSize: '0.875rem' }}>
                                  {` +${po.line_items.length - 3} more`}
                                </Typography>
                              </Tooltip>
                            )}
                          </Typography>
                        ) : (
                          <Typography variant="body2" color="text.secondary">
                            No items
                          </Typography>
                        )}
                      </Box>
                    </TableCell>
                    <TableCell>{new Date(po.po_date).toLocaleDateString()}</TableCell>
                    <TableCell>{po.expected_date ? new Date(po.expected_date).toLocaleDateString() : '-'}</TableCell>
                    <TableCell align="right">
                      <Typography variant="body2" fontWeight="medium">
                        {formatCurrency(po.total_amount)}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={po.status.charAt(0).toUpperCase() + po.status.slice(1)}
                        color={getStatusColor(po.status) as any}
                        size="small"
                      />
                    </TableCell>
                    <TableCell align="center">
                      <Tooltip title="View">
                        <IconButton size="small" onClick={() => handleViewPurchaseOrder(po)}>
                          <VisibilityIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                      
                      {/* Show Send button only for Draft POs */}
                      {po.status === 'draft' && (
                        <Tooltip title="Send to Vendor">
                          <IconButton 
                            size="small" 
                            onClick={() => po.id && handleSendPurchaseOrder(po.id)}
                            color="primary"
                          >
                            <SendIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      )}
                      
                      {/* Show Edit button only for Draft POs */}
                      {po.status === 'draft' && (
                        <Tooltip title="Edit">
                          <IconButton size="small" onClick={() => handleEditPurchaseOrder(po)}>
                            <EditIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      )}
                      
                      {/* Show Delete button only for Draft POs */}
                      {po.status === 'draft' && (
                        <Tooltip title="Delete">
                          <IconButton
                            size="small"
                            onClick={() => po.id && handleDeletePurchaseOrder(po.id)}
                            color="error"
                          >
                            <DeleteIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      )}

                      {/* Show Create Bill button for received/acknowledged POs */}
                      {(po.status === 'received' || po.status === 'acknowledged' || po.status === 'partial') && (
                        <Tooltip title="Create Vendor Bill">
                          <IconButton
                            size="small"
                            onClick={() => navigate(`/purchase/vendor-bills/create-from-po?po_id=${po.id}`)}
                            color="success"
                          >
                            <ReceiptIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              {filteredPurchaseOrders.length === 0 && (
                <TableRow>
                  <TableCell colSpan={8} align="center">
                    <Typography variant="body1" sx={{ py: 2 }}>
                      No purchase orders found
                    </Typography>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </TableContainer>
        <TablePagination
          rowsPerPageOptions={[5, 10, 25]}
          component="div"
          count={filteredPurchaseOrders.length}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
        />
      </Paper>

      {/* Purchase Order Form Dialog */}
      <Dialog 
        open={poFormOpen} 
        onClose={() => setPoFormOpen(false)} 
        maxWidth={false}
        fullWidth
        PaperProps={{
          sx: {
            width: 'calc(100vw - 72px)', // 0.5 inches on each side (36px each)
            height: 'calc(100vh - 72px)', // 0.5 inches on top and bottom
            maxWidth: 'none',
            maxHeight: 'none',
            margin: '36px', // 0.5 inches margin
          }
        }}
      >
        <PurchaseOrderForm
          onClose={() => setPoFormOpen(false)}
          onSave={handleSavePurchaseOrder}
          initialValues={selectedPO}
          viewMode={selectedPO?.viewMode}
        />
      </Dialog>
    </PageContainer>
  );
};

export default PurchaseOrdersPage; 