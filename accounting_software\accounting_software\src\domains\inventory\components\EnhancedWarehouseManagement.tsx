import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Card,
  CardContent,
  Button,
  Alert,
  Grid,
  Chip,
  CircularProgress,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Tooltip,
  Divider,
} from '@mui/material';
import {
  Warehouse as WarehouseIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  LocationOn as LocationIcon,
  Inventory as InventoryIcon,
  Refresh as RefreshIcon,
  Visibility as ViewIcon,
} from '@mui/icons-material';
import { inventoryService, Warehouse, WarehouseLocation } from '../services/inventory.service';
import { formatDate } from '../../../shared/utils/formatters';

interface EnhancedWarehouseManagementProps {
  onWarehouseSelect?: (warehouse: Warehouse) => void;
}

const EnhancedWarehouseManagement: React.FC<EnhancedWarehouseManagementProps> = ({
  onWarehouseSelect
}) => {
  const [warehouses, setWarehouses] = useState<Warehouse[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [warehouseTypeFilter, setWarehouseTypeFilter] = useState('');
  const [activeFilter, setActiveFilter] = useState<boolean | ''>('');
  
  // Dialog states
  const [openDialog, setOpenDialog] = useState(false);
  const [dialogMode, setDialogMode] = useState<'create' | 'edit' | 'view'>('create');
  const [selectedWarehouse, setSelectedWarehouse] = useState<Warehouse | null>(null);
  const [warehouseLocations, setWarehouseLocations] = useState<WarehouseLocation[]>([]);
  const [inventorySummary, setInventorySummary] = useState<any>(null);
  
  // Form state
  const [formData, setFormData] = useState({
    name: '',
    warehouse_code: '',
    warehouse_type: 'MAIN' as const,
    location: '',
    address: '',
    contact_person: '',
    phone: '',
    email: '',
    is_active: true,
  });

  useEffect(() => {
    loadWarehouses();
  }, [searchTerm, warehouseTypeFilter, activeFilter]);

  const loadWarehouses = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await inventoryService.getEnterpriseWarehouses({
        search: searchTerm || undefined,
        warehouse_type: warehouseTypeFilter || undefined,
        is_active: activeFilter !== '' ? activeFilter : undefined,
      });
      
      setWarehouses(response.results || response);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load warehouses');
    } finally {
      setLoading(false);
    }
  };

  const handleOpenDialog = (mode: 'create' | 'edit' | 'view', warehouse?: Warehouse) => {
    setDialogMode(mode);
    setSelectedWarehouse(warehouse || null);
    
    if (warehouse) {
      setFormData({
        name: warehouse.name,
        warehouse_code: warehouse.warehouse_code,
        warehouse_type: warehouse.warehouse_type,
        location: warehouse.location,
        address: warehouse.address || '',
        contact_person: warehouse.contact_person || '',
        phone: warehouse.phone || '',
        email: warehouse.email || '',
        is_active: warehouse.is_active,
      });
      
      // Load additional data for view/edit mode
      if (mode === 'view' || mode === 'edit') {
        loadWarehouseDetails(warehouse.warehouse_id);
      }
    } else {
      // Reset form for create mode
      setFormData({
        name: '',
        warehouse_code: '',
        warehouse_type: 'MAIN',
        location: '',
        address: '',
        contact_person: '',
        phone: '',
        email: '',
        is_active: true,
      });
    }
    
    setOpenDialog(true);
  };

  const loadWarehouseDetails = async (warehouseId: number) => {
    try {
      const [locationsResponse, summaryResponse] = await Promise.all([
        inventoryService.getWarehouseLocations(warehouseId),
        inventoryService.getWarehouseInventorySummary(warehouseId),
      ]);
      
      setWarehouseLocations(locationsResponse);
      setInventorySummary(summaryResponse);
    } catch (err) {
      console.error('Failed to load warehouse details:', err);
    }
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    setSelectedWarehouse(null);
    setWarehouseLocations([]);
    setInventorySummary(null);
  };

  const handleSubmit = async () => {
    try {
      setLoading(true);
      
      if (dialogMode === 'create') {
        await inventoryService.createWarehouse(formData);
      } else if (dialogMode === 'edit' && selectedWarehouse) {
        await inventoryService.updateWarehouse(selectedWarehouse.warehouse_id, formData);
      }
      
      handleCloseDialog();
      loadWarehouses();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save warehouse');
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (warehouse: Warehouse) => {
    if (window.confirm(`Are you sure you want to delete warehouse "${warehouse.name}"?`)) {
      try {
        setLoading(true);
        await inventoryService.deleteWarehouse(warehouse.warehouse_id);
        loadWarehouses();
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to delete warehouse');
      } finally {
        setLoading(false);
      }
    }
  };

  const getWarehouseTypeColor = (type: string) => {
    switch (type) {
      case 'MAIN': return 'primary';
      case 'BRANCH': return 'secondary';
      case 'TRANSIT': return 'warning';
      case 'VIRTUAL': return 'info';
      default: return 'default';
    }
  };

  return (
    <Box>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h5" component="h1">
          <WarehouseIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
          Warehouse Management
        </Typography>
        <Box display="flex" gap={1}>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={loadWarehouses}
            disabled={loading}
          >
            Refresh
          </Button>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => handleOpenDialog('create')}
          >
            Add Warehouse
          </Button>
        </Box>
      </Box>

      {/* Filters */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="Search warehouses"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                size="small"
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth size="small">
                <InputLabel>Warehouse Type</InputLabel>
                <Select
                  value={warehouseTypeFilter}
                  onChange={(e) => setWarehouseTypeFilter(e.target.value)}
                  label="Warehouse Type"
                >
                  <MenuItem value="">All Types</MenuItem>
                  <MenuItem value="MAIN">Main Warehouse</MenuItem>
                  <MenuItem value="BRANCH">Branch Warehouse</MenuItem>
                  <MenuItem value="TRANSIT">Transit Warehouse</MenuItem>
                  <MenuItem value="VIRTUAL">Virtual Warehouse</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth size="small">
                <InputLabel>Status</InputLabel>
                <Select
                  value={activeFilter}
                  onChange={(e) => setActiveFilter(e.target.value as boolean | '')}
                  label="Status"
                >
                  <MenuItem value="">All Status</MenuItem>
                  <MenuItem value={true}>Active</MenuItem>
                  <MenuItem value={false}>Inactive</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Warehouses Table */}
      <Card>
        <CardContent>
          {loading ? (
            <Box display="flex" justifyContent="center" p={3}>
              <CircularProgress />
            </Box>
          ) : (
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Name</TableCell>
                    <TableCell>Code</TableCell>
                    <TableCell>Type</TableCell>
                    <TableCell>Location</TableCell>
                    <TableCell>Locations</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Created</TableCell>
                    <TableCell align="right">Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {warehouses.map((warehouse) => (
                    <TableRow key={warehouse.warehouse_id} hover>
                      <TableCell>
                        <Typography variant="subtitle2">
                          {warehouse.name}
                        </Typography>
                        {warehouse.contact_person && (
                          <Typography variant="caption" color="textSecondary">
                            Contact: {warehouse.contact_person}
                          </Typography>
                        )}
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" fontFamily="monospace">
                          {warehouse.warehouse_code}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={warehouse.warehouse_type}
                          color={getWarehouseTypeColor(warehouse.warehouse_type) as any}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>{warehouse.location}</TableCell>
                      <TableCell>
                        {warehouse.total_locations ? (
                          <Chip
                            icon={<LocationIcon />}
                            label={`${warehouse.active_locations}/${warehouse.total_locations}`}
                            size="small"
                            variant="outlined"
                          />
                        ) : (
                          <Typography variant="caption" color="textSecondary">
                            No locations
                          </Typography>
                        )}
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={warehouse.is_active ? 'Active' : 'Inactive'}
                          color={warehouse.is_active ? 'success' : 'default'}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Typography variant="caption">
                          {formatDate(warehouse.created_at)}
                        </Typography>
                      </TableCell>
                      <TableCell align="right">
                        <Tooltip title="View Details">
                          <IconButton
                            size="small"
                            onClick={() => handleOpenDialog('view', warehouse)}
                          >
                            <ViewIcon />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Edit">
                          <IconButton
                            size="small"
                            onClick={() => handleOpenDialog('edit', warehouse)}
                          >
                            <EditIcon />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Delete">
                          <IconButton
                            size="small"
                            color="error"
                            onClick={() => handleDelete(warehouse)}
                          >
                            <DeleteIcon />
                          </IconButton>
                        </Tooltip>
                      </TableCell>
                    </TableRow>
                  ))}
                  {warehouses.length === 0 && !loading && (
                    <TableRow>
                      <TableCell colSpan={8} align="center">
                        <Typography color="textSecondary">
                          No warehouses found
                        </Typography>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </CardContent>
      </Card>

      {/* Warehouse Dialog */}
      <Dialog
        open={openDialog}
        onClose={handleCloseDialog}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          {dialogMode === 'create' && 'Create New Warehouse'}
          {dialogMode === 'edit' && 'Edit Warehouse'}
          {dialogMode === 'view' && 'Warehouse Details'}
        </DialogTitle>
        <DialogContent>
          {dialogMode === 'view' ? (
            // View Mode - Display warehouse details
            <Box>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Typography variant="h6" gutterBottom>
                    Basic Information
                  </Typography>
                  <Box mb={2}>
                    <Typography variant="subtitle2" color="textSecondary">
                      Warehouse Name
                    </Typography>
                    <Typography variant="body1">{selectedWarehouse?.name}</Typography>
                  </Box>
                  <Box mb={2}>
                    <Typography variant="subtitle2" color="textSecondary">
                      Warehouse Code
                    </Typography>
                    <Typography variant="body1" fontFamily="monospace">
                      {selectedWarehouse?.warehouse_code}
                    </Typography>
                  </Box>
                  <Box mb={2}>
                    <Typography variant="subtitle2" color="textSecondary">
                      Type
                    </Typography>
                    <Chip
                      label={selectedWarehouse?.warehouse_type}
                      color={getWarehouseTypeColor(selectedWarehouse?.warehouse_type || '') as any}
                      size="small"
                    />
                  </Box>
                  <Box mb={2}>
                    <Typography variant="subtitle2" color="textSecondary">
                      Location
                    </Typography>
                    <Typography variant="body1">{selectedWarehouse?.location}</Typography>
                  </Box>
                </Grid>

                <Grid item xs={12} md={6}>
                  <Typography variant="h6" gutterBottom>
                    Contact Information
                  </Typography>
                  <Box mb={2}>
                    <Typography variant="subtitle2" color="textSecondary">
                      Contact Person
                    </Typography>
                    <Typography variant="body1">
                      {selectedWarehouse?.contact_person || 'Not specified'}
                    </Typography>
                  </Box>
                  <Box mb={2}>
                    <Typography variant="subtitle2" color="textSecondary">
                      Phone
                    </Typography>
                    <Typography variant="body1">
                      {selectedWarehouse?.phone || 'Not specified'}
                    </Typography>
                  </Box>
                  <Box mb={2}>
                    <Typography variant="subtitle2" color="textSecondary">
                      Email
                    </Typography>
                    <Typography variant="body1">
                      {selectedWarehouse?.email || 'Not specified'}
                    </Typography>
                  </Box>
                </Grid>

                {/* Inventory Summary */}
                {inventorySummary && (
                  <Grid item xs={12}>
                    <Divider sx={{ my: 2 }} />
                    <Typography variant="h6" gutterBottom>
                      Inventory Summary
                    </Typography>
                    <Grid container spacing={2}>
                      <Grid item xs={3}>
                        <Card variant="outlined">
                          <CardContent>
                            <Typography variant="h4" color="primary">
                              {inventorySummary.total_products || 0}
                            </Typography>
                            <Typography variant="caption">
                              Total Products
                            </Typography>
                          </CardContent>
                        </Card>
                      </Grid>
                      <Grid item xs={3}>
                        <Card variant="outlined">
                          <CardContent>
                            <Typography variant="h4" color="info.main">
                              {inventorySummary.total_quantity || 0}
                            </Typography>
                            <Typography variant="caption">
                              Total Quantity
                            </Typography>
                          </CardContent>
                        </Card>
                      </Grid>
                      <Grid item xs={3}>
                        <Card variant="outlined">
                          <CardContent>
                            <Typography variant="h4" color="warning.main">
                              {inventorySummary.low_stock_count || 0}
                            </Typography>
                            <Typography variant="caption">
                              Low Stock Items
                            </Typography>
                          </CardContent>
                        </Card>
                      </Grid>
                      <Grid item xs={3}>
                        <Card variant="outlined">
                          <CardContent>
                            <Typography variant="h4" color="error.main">
                              {inventorySummary.out_of_stock_count || 0}
                            </Typography>
                            <Typography variant="caption">
                              Out of Stock
                            </Typography>
                          </CardContent>
                        </Card>
                      </Grid>
                    </Grid>
                  </Grid>
                )}

                {/* Warehouse Locations */}
                {warehouseLocations.length > 0 && (
                  <Grid item xs={12}>
                    <Divider sx={{ my: 2 }} />
                    <Typography variant="h6" gutterBottom>
                      Warehouse Locations ({warehouseLocations.length})
                    </Typography>
                    <TableContainer component={Paper} variant="outlined">
                      <Table size="small">
                        <TableHead>
                          <TableRow>
                            <TableCell>Location Code</TableCell>
                            <TableCell>Zone</TableCell>
                            <TableCell>Type</TableCell>
                            <TableCell>Utilization</TableCell>
                            <TableCell>Status</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {warehouseLocations.slice(0, 5).map((location) => (
                            <TableRow key={location.location_id}>
                              <TableCell fontFamily="monospace">
                                {location.location_code}
                              </TableCell>
                              <TableCell>{location.zone}</TableCell>
                              <TableCell>
                                <Chip label={location.location_type} size="small" />
                              </TableCell>
                              <TableCell>
                                {location.current_utilization_percent.toFixed(1)}%
                              </TableCell>
                              <TableCell>
                                <Chip
                                  label={location.is_active ? 'Active' : 'Inactive'}
                                  color={location.is_active ? 'success' : 'default'}
                                  size="small"
                                />
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                    {warehouseLocations.length > 5 && (
                      <Typography variant="caption" color="textSecondary" sx={{ mt: 1 }}>
                        Showing 5 of {warehouseLocations.length} locations
                      </Typography>
                    )}
                  </Grid>
                )}
              </Grid>
            </Box>
          ) : (
            // Create/Edit Mode - Form
            <Box>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Warehouse Name"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    required
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Warehouse Code"
                    value={formData.warehouse_code}
                    onChange={(e) => setFormData({ ...formData, warehouse_code: e.target.value })}
                    required
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <FormControl fullWidth required>
                    <InputLabel>Warehouse Type</InputLabel>
                    <Select
                      value={formData.warehouse_type}
                      onChange={(e) => setFormData({ ...formData, warehouse_type: e.target.value as any })}
                      label="Warehouse Type"
                    >
                      <MenuItem value="MAIN">Main Warehouse</MenuItem>
                      <MenuItem value="BRANCH">Branch Warehouse</MenuItem>
                      <MenuItem value="TRANSIT">Transit Warehouse</MenuItem>
                      <MenuItem value="VIRTUAL">Virtual Warehouse</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="Location"
                    value={formData.location}
                    onChange={(e) => setFormData({ ...formData, location: e.target.value })}
                    required
                  />
                </Grid>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Address"
                    value={formData.address}
                    onChange={(e) => setFormData({ ...formData, address: e.target.value })}
                    multiline
                    rows={2}
                  />
                </Grid>
                <Grid item xs={12} md={4}>
                  <TextField
                    fullWidth
                    label="Contact Person"
                    value={formData.contact_person}
                    onChange={(e) => setFormData({ ...formData, contact_person: e.target.value })}
                  />
                </Grid>
                <Grid item xs={12} md={4}>
                  <TextField
                    fullWidth
                    label="Phone"
                    value={formData.phone}
                    onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                  />
                </Grid>
                <Grid item xs={12} md={4}>
                  <TextField
                    fullWidth
                    label="Email"
                    type="email"
                    value={formData.email}
                    onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                  />
                </Grid>
                <Grid item xs={12}>
                  <FormControl>
                    <Box display="flex" alignItems="center">
                      <input
                        type="checkbox"
                        checked={formData.is_active}
                        onChange={(e) => setFormData({ ...formData, is_active: e.target.checked })}
                      />
                      <Typography variant="body2" sx={{ ml: 1 }}>
                        Active warehouse
                      </Typography>
                    </Box>
                  </FormControl>
                </Grid>
              </Grid>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>
            {dialogMode === 'view' ? 'Close' : 'Cancel'}
          </Button>
          {dialogMode !== 'view' && (
            <Button
              variant="contained"
              onClick={handleSubmit}
              disabled={loading}
            >
              {dialogMode === 'create' ? 'Create' : 'Update'}
            </Button>
          )}
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default EnhancedWarehouseManagement;
