import api from './api';

// Customer Invoice Types
export interface CustomerInvoiceCustomer {
  id: number;
  display_name: string;
  email?: string;
  phone?: string;
  billing_address?: string;
  payment_terms?: string;
}

export interface CustomerInvoiceItem {
  id?: number;
  product?: number;
  product_name?: string;
  item_description: string;
  quantity: number;
  unit_price: number;
  line_total: number;
  tax_rate?: number;
  tax_amount?: number;
  account_code?: string;
  line_order?: number;
}

export interface CustomerInvoice {
  id: number;
  invoice_number: string;
  customer: number;
  customer_details?: CustomerInvoiceCustomer;
  customer_name?: string; // Computed field for display
  invoice_date: string;
  due_date: string;
  status: 'draft' | 'posted' | 'sent' | 'partial' | 'paid' | 'overdue' | 'void';
  line_items: CustomerInvoiceItem[];
  subtotal: number;
  discount_amount: number;
  shipping_amount: number;
  tax_amount: number;
  total_amount: number;
  amount_paid: number;
  balance_due: number;
  notes?: string;
  terms_and_conditions?: string;
  reference_number?: string;
  sales_order?: number;
  delivery_note?: number;
  created_at?: string;
  updated_at?: string;
}

export interface CustomerInvoiceFilters {
  status?: string;
  customer?: number;
  invoice_date_from?: string;
  invoice_date_to?: string;
  due_date_from?: string;
  due_date_to?: string;
  search?: string;
}

export interface CustomerInvoiceStats {
  total_invoices: number;
  draft_invoices: number;
  posted_invoices: number;
  paid_invoices: number;
  overdue_invoices: number;
  total_receivables: number;
}

export interface BillableSalesOrder {
  id: number;
  so_number: string;
  customer: number;
  customer_name: string;
  so_date: string;
  total_amount: number;
  status: string;
}

export interface BillableDeliveryNote {
  id: number;
  dn_number: string;
  customer: number;
  customer_name: string;
  delivery_date: string;
  total_amount: number;
  status: string;
}

class CustomerInvoiceService {
  private baseUrl = '/api/sales/customer-invoices';

  // Get all customer invoices with optional filters
  async getCustomerInvoices(filters?: CustomerInvoiceFilters): Promise<{ results: CustomerInvoice[]; count: number }> {
    const params = new URLSearchParams();
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, value.toString());
        }
      });
    }

    const response = await api.get(`${this.baseUrl}/?${params.toString()}`);
    return response.data;
  }

  // Get single customer invoice
  async getCustomerInvoice(id: number): Promise<CustomerInvoice> {
    const response = await api.get(`${this.baseUrl}/${id}/`);
    return response.data;
  }

  // Create customer invoice
  async createCustomerInvoice(invoiceData: Partial<CustomerInvoice>): Promise<CustomerInvoice> {
    const response = await api.post(`${this.baseUrl}/`, invoiceData);
    return response.data;
  }

  // Update customer invoice
  async updateCustomerInvoice(id: number, invoiceData: Partial<CustomerInvoice>): Promise<CustomerInvoice> {
    const response = await api.put(`${this.baseUrl}/${id}/`, invoiceData);
    return response.data;
  }

  // Delete customer invoice
  async deleteCustomerInvoice(id: number): Promise<void> {
    await api.delete(`${this.baseUrl}/${id}/`);
  }

  // Get customer invoice statistics
  async getCustomerInvoiceStats(): Promise<CustomerInvoiceStats> {
    const response = await api.get(`${this.baseUrl}/stats/`);
    return response.data;
  }

  // Get billable sales orders
  async getBillableSalesOrders(): Promise<BillableSalesOrder[]> {
    const response = await api.get(`${this.baseUrl}/billable_sales_orders/`);
    return response.data;
  }

  // Get billable delivery notes
  async getBillableDeliveryNotes(): Promise<BillableDeliveryNote[]> {
    const response = await api.get(`${this.baseUrl}/billable_delivery_notes/`);
    return response.data;
  }

  // Create customer invoice from sales order
  async createCustomerInvoiceFromSalesOrder(salesOrderId: number, invoiceData?: {
    invoice_date?: string;
    due_date?: string;
    notes?: string;
  }): Promise<CustomerInvoice> {
    const response = await api.post(`${this.baseUrl}/create_from_sales_order/`, {
      sales_order_id: salesOrderId,
      ...invoiceData
    });
    return response.data;
  }

  // Create customer invoice from delivery note
  async createCustomerInvoiceFromDeliveryNote(deliveryNoteId: number, invoiceData?: {
    invoice_date?: string;
    due_date?: string;
    notes?: string;
  }): Promise<CustomerInvoice> {
    const response = await api.post(`${this.baseUrl}/create_from_delivery_note/`, {
      delivery_note_id: deliveryNoteId,
      ...invoiceData
    });
    return response.data;
  }

  // Post customer invoice (change status from draft to posted)
  async postCustomerInvoice(id: number): Promise<{ message: string }> {
    const response = await api.post(`${this.baseUrl}/${id}/post_invoice/`);
    return response.data;
  }

  // Void customer invoice
  async voidCustomerInvoice(id: number): Promise<{ message: string }> {
    const response = await api.post(`${this.baseUrl}/${id}/void_invoice/`);
    return response.data;
  }
}

export const customerInvoiceService = new CustomerInvoiceService();
