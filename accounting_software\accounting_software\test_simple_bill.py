#!/usr/bin/env python3
"""
Simple test for vendor bill creation
"""
import requests
import json

# Configuration
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api"
TOKEN = "e15bc01f831c5111f413f534ef82288744cb7d41"

HEADERS = {
    'Content-Type': 'application/json',
    'Authorization': f'Token {TOKEN}'
}

def test_simple_bill():
    print("🧪 SIMPLE VENDOR BILL TEST")
    print("=" * 30)
    
    try:
        # Get vendor
        vendors_response = requests.get(f"{API_BASE}/contacts/vendors/", headers=HEADERS, timeout=5)
        vendor_id = vendors_response.json()['results'][0]['contact']
        
        # Simple bill without products
        bill_data = {
            "vendor": vendor_id,
            "bill_date": "2025-07-06",
            "due_date": "2025-08-05",
            "status": "draft",
            "payment_terms": "Net 30",
            "reference_number": "SIMPLE-001",
            "notes": "Simple test",
            "line_items": [
                {
                    "item_description": "Simple service item",
                    "quantity": 1,
                    "unit_price": 100.00,
                    "tax_rate": 10.0,
                    "account_code": "5010-COGS",
                    "line_order": 1
                }
            ]
        }
        
        print("Creating simple bill...")
        response = requests.post(f"{API_BASE}/purchase/vendor-bills/", json=bill_data, headers=HEADERS, timeout=10)
        
        print(f"Response status: {response.status_code}")
        if response.status_code == 201:
            bill = response.json()
            print(f"✅ Bill created: {bill['bill_number']}")
            return True
        else:
            print(f"❌ Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        return False

if __name__ == "__main__":
    success = test_simple_bill()
    print(f"\nResult: {'✅ SUCCESS' if success else '❌ FAILED'}")
