#!/usr/bin/env python
import os
import sys
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'erp_backend.settings')
django.setup()

from purchase.models import VendorBill
from contacts.models import Contact
from purchase.serializers import VendorBillSerializer

def test_direct_creation():
    """Test creating VendorBill directly"""
    try:
        print("Testing direct VendorBill creation...")
        
        # Get a vendor
        vendor = Contact.objects.filter(contact_type='vendor').first()
        if not vendor:
            print("❌ No vendor found")
            return
            
        print(f"Using vendor: {vendor.name} (ID: {vendor.id})")
        
        # Test data matching what frontend sends
        bill_data = {
            'vendor': vendor.id,
            'bill_date': '2025-07-06',
            'due_date': '2025-08-05',
            'status': 'draft',
            'line_items': []
        }
        
        print("Testing serializer...")
        serializer = VendorBillSerializer(data=bill_data)
        
        if serializer.is_valid():
            print("✅ Serializer validation passed")
            print("Validated data:", serializer.validated_data)
            
            # Try to save
            try:
                bill = serializer.save()
                print(f"✅ VendorBill created successfully! ID: {bill.id}")
                print(f"Bill number: {bill.bill_number}")
            except Exception as save_error:
                print(f"❌ Error during save: {save_error}")
                import traceback
                traceback.print_exc()
        else:
            print("❌ Serializer validation failed")
            print("Errors:", serializer.errors)
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_direct_creation()
