import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Paper,
  Typography,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  IconButton,
  Menu,
  MenuItem,
  Alert,
  Snackbar,
  TextField,
  Grid,
  Card,
  CardContent,
  CircularProgress,
  Backdrop,
  FormControl,
  InputLabel,
  Select,
} from '@mui/material';
import {
  Add as AddIcon,
  MoreVert as MoreVertIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Receipt as ReceiptIcon,
  Cancel as CancelIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import dayjs from 'dayjs';

// Import services
import { customerInvoiceService, type CustomerInvoice, type CustomerInvoiceFilters, type CustomerInvoiceStats } from '../../../services/customer-invoice.service';
import { formatCurrency } from '../../../shared/utils/formatters';

interface SnackbarState {
  open: boolean;
  message: string;
  severity: 'success' | 'error' | 'warning' | 'info';
}

const CustomerInvoiceListPage: React.FC = () => {
  const navigate = useNavigate();

  // State
  const [loading, setLoading] = useState(true);
  const [invoices, setInvoices] = useState<CustomerInvoice[]>([]);
  const [stats, setStats] = useState<CustomerInvoiceStats | null>(null);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedInvoice, setSelectedInvoice] = useState<CustomerInvoice | null>(null);
  const [snackbar, setSnackbar] = useState<SnackbarState>({
    open: false,
    message: '',
    severity: 'success',
  });

  // Filters
  const [filters, setFilters] = useState<CustomerInvoiceFilters>({
    status: '',
    search: '',
  });

  // Load customer invoices
  const loadCustomerInvoices = async () => {
    try {
      setLoading(true);
      console.log('🔍 Loading customer invoices with filters:', filters);

      const [invoicesResponse, statsResponse] = await Promise.all([
        customerInvoiceService.getCustomerInvoices(filters),
        customerInvoiceService.getCustomerInvoiceStats()
      ]);

      console.log('📋 Invoices response:', invoicesResponse);
      console.log('📊 Stats response:', statsResponse);

      setInvoices(invoicesResponse?.results || []);
      setStats(statsResponse || null);

      console.log(`✅ Loaded ${invoicesResponse?.results?.length || 0} invoices`);
    } catch (error) {
      console.error('❌ Failed to load customer invoices:', error);
      setInvoices([]); // Set empty array on error
      setStats(null);
      setSnackbar({
        open: true,
        message: 'Failed to load customer invoices',
        severity: 'error',
      });
    } finally {
      setLoading(false);
    }
  };

  // Load data on component mount and filter changes
  useEffect(() => {
    loadCustomerInvoices();
  }, [filters]);

  // Handle menu actions
  const handleMenuClick = (event: React.MouseEvent<HTMLElement>, invoice: CustomerInvoice) => {
    setAnchorEl(event.currentTarget);
    setSelectedInvoice(invoice);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedInvoice(null);
  };

  // Handle invoice actions
  const handleEditInvoice = () => {
    if (selectedInvoice) {
      navigate(`/dashboard/sales/customer-invoices/${selectedInvoice.id}/edit`);
    }
    handleMenuClose();
  };

  const handlePostInvoice = async () => {
    if (!selectedInvoice) return;

    try {
      await customerInvoiceService.postCustomerInvoice(selectedInvoice.id);
      setSnackbar({
        open: true,
        message: `Invoice ${selectedInvoice.invoice_number} posted successfully!`,
        severity: 'success',
      });
      loadCustomerInvoices(); // Refresh the list
    } catch (error) {
      console.error('Failed to post invoice:', error);
      setSnackbar({
        open: true,
        message: `Failed to post invoice: ${error instanceof Error ? error.message : 'Unknown error'}`,
        severity: 'error',
      });
    }
    handleMenuClose();
  };

  const handleVoidInvoice = async () => {
    if (!selectedInvoice) return;

    try {
      await customerInvoiceService.voidCustomerInvoice(selectedInvoice.id);
      setSnackbar({
        open: true,
        message: `Invoice ${selectedInvoice.invoice_number} voided successfully!`,
        severity: 'success',
      });
      loadCustomerInvoices(); // Refresh the list
    } catch (error) {
      console.error('Failed to void invoice:', error);
      setSnackbar({
        open: true,
        message: `Failed to void invoice: ${error instanceof Error ? error.message : 'Unknown error'}`,
        severity: 'error',
      });
    }
    handleMenuClose();
  };

  const handleDeleteInvoice = async () => {
    if (!selectedInvoice) return;

    if (selectedInvoice.status !== 'draft') {
      setSnackbar({
        open: true,
        message: 'Only draft invoices can be deleted',
        severity: 'error',
      });
      handleMenuClose();
      return;
    }

    try {
      await customerInvoiceService.deleteCustomerInvoice(selectedInvoice.id);
      setSnackbar({
        open: true,
        message: `Invoice ${selectedInvoice.invoice_number} deleted successfully!`,
        severity: 'success',
      });
      loadCustomerInvoices(); // Refresh the list
    } catch (error) {
      console.error('Failed to delete invoice:', error);
      setSnackbar({
        open: true,
        message: `Failed to delete invoice: ${error instanceof Error ? error.message : 'Unknown error'}`,
        severity: 'error',
      });
    }
    handleMenuClose();
  };

  // Get status color
  const getStatusColor = (status: string): 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning' => {
    switch (status) {
      case 'draft': return 'default';
      case 'posted': return 'primary';
      case 'sent': return 'info';
      case 'partial': return 'warning';
      case 'paid': return 'success';
      case 'overdue': return 'error';
      case 'void': return 'error';
      default: return 'default';
    }
  };

  const handleCloseSnackbar = () => {
    setSnackbar(prev => ({ ...prev, open: false }));
  };

  return (
    <Box sx={{ minHeight: '100vh', bgcolor: '#f5f5f5' }}>
      <Backdrop open={loading} sx={{ zIndex: 9999 }}>
        <CircularProgress color="primary" />
      </Backdrop>

      {/* Header */}
      <Box sx={{ bgcolor: 'white', borderBottom: 1, borderColor: 'divider', p: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box>
            <Typography variant="h4" component="h1" sx={{ fontWeight: 600, color: '#1a1a1a' }}>
              Customer Invoices
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
              Manage customer invoices and track receivables
            </Typography>
          </Box>
          <Box sx={{ display: 'flex', gap: 2 }}>
            <Button
              variant="outlined"
              startIcon={<RefreshIcon />}
              onClick={loadCustomerInvoices}
              sx={{ borderRadius: '8px' }}
            >
              Refresh
            </Button>
            <Button
              variant="contained"
              startIcon={<AddIcon />}
              onClick={() => navigate('/dashboard/sales/customer-invoices/create')}
              sx={{ borderRadius: '8px' }}
            >
              Create Invoice
            </Button>
          </Box>
        </Box>
      </Box>

      {/* Stats Cards */}
      {stats && (
        <Box sx={{ p: 3 }}>
          <Grid container spacing={3}>
            <Grid item xs={12} sm={6} md={2}>
              <Card>
                <CardContent sx={{ textAlign: 'center' }}>
                  <Typography variant="h4" sx={{ fontWeight: 600, color: 'primary.main' }}>
                    {stats.total_invoices}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total Invoices
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <Card>
                <CardContent sx={{ textAlign: 'center' }}>
                  <Typography variant="h4" sx={{ fontWeight: 600, color: 'warning.main' }}>
                    {stats.draft_invoices}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Draft
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <Card>
                <CardContent sx={{ textAlign: 'center' }}>
                  <Typography variant="h4" sx={{ fontWeight: 600, color: 'info.main' }}>
                    {stats.posted_invoices}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Posted
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <Card>
                <CardContent sx={{ textAlign: 'center' }}>
                  <Typography variant="h4" sx={{ fontWeight: 600, color: 'success.main' }}>
                    {stats.paid_invoices}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Paid
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <Card>
                <CardContent sx={{ textAlign: 'center' }}>
                  <Typography variant="h4" sx={{ fontWeight: 600, color: 'error.main' }}>
                    {stats.overdue_invoices}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Overdue
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <Card>
                <CardContent sx={{ textAlign: 'center' }}>
                  <Typography variant="h4" sx={{ fontWeight: 600, color: 'primary.main' }}>
                    {formatCurrency(stats.total_receivables, '$')}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total Receivables
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Box>
      )}

      {/* Filters */}
      <Box sx={{ p: 3, pt: 0 }}>
        <Card>
          <CardContent>
            <Grid container spacing={3} alignItems="center">
              <Grid item xs={12} md={4}>
                <TextField
                  fullWidth
                  label="Search"
                  value={filters.search}
                  onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                  placeholder="Search by invoice number, customer name..."
                />
              </Grid>
              <Grid item xs={12} md={3}>
                <FormControl fullWidth>
                  <InputLabel>Status</InputLabel>
                  <Select
                    value={filters.status || ''}
                    onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
                    label="Status"
                  >
                    <MenuItem value="">All Statuses</MenuItem>
                    <MenuItem value="draft">Draft</MenuItem>
                    <MenuItem value="posted">Posted</MenuItem>
                    <MenuItem value="sent">Sent</MenuItem>
                    <MenuItem value="partial">Partially Paid</MenuItem>
                    <MenuItem value="paid">Paid</MenuItem>
                    <MenuItem value="overdue">Overdue</MenuItem>
                    <MenuItem value="void">Void</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      </Box>

      {/* Invoice Table */}
      <Box sx={{ p: 3, pt: 0 }}>
        <Card>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Invoice Number</TableCell>
                  <TableCell>Customer</TableCell>
                  <TableCell>Invoice Date</TableCell>
                  <TableCell>Due Date</TableCell>
                  <TableCell align="right">Total Amount</TableCell>
                  <TableCell align="right">Balance Due</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell align="center">Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {invoices.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={8} align="center" sx={{ py: 4 }}>
                      <Typography variant="body2" color="text.secondary">
                        No customer invoices found. Create your first invoice to get started.
                      </Typography>
                    </TableCell>
                  </TableRow>
                ) : (
                  invoices.map((invoice) => (
                    <TableRow key={invoice.id} hover>
                      <TableCell>
                        <Typography variant="body2" sx={{ fontWeight: 500 }}>
                          {invoice.invoice_number}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {invoice.customer_details?.name || invoice.customer_details?.display_name || invoice.customer_name || 'No Customer'}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {dayjs(invoice.invoice_date).format('DD/MM/YYYY')}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {dayjs(invoice.due_date).format('DD/MM/YYYY')}
                        </Typography>
                      </TableCell>
                      <TableCell align="right">
                        <Typography variant="body2" sx={{ fontWeight: 500 }}>
                          {formatCurrency(invoice.total_amount, '$')}
                        </Typography>
                      </TableCell>
                      <TableCell align="right">
                        <Typography variant="body2" sx={{ fontWeight: 500 }}>
                          {formatCurrency(invoice.balance_due, '$')}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={invoice.status}
                          size="small"
                          color={getStatusColor(invoice.status)}
                          sx={{ textTransform: 'capitalize' }}
                        />
                      </TableCell>
                      <TableCell align="center">
                        <IconButton
                          size="small"
                          onClick={(e) => handleMenuClick(e, invoice)}
                        >
                          <MoreVertIcon />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </Card>
      </Box>

      {/* Action Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        transformOrigin={{ vertical: 'top', horizontal: 'right' }}
      >
        <MenuItem onClick={handleEditInvoice}>
          <EditIcon sx={{ mr: 1, fontSize: 20 }} />
          Edit Invoice
        </MenuItem>

        {selectedInvoice?.status === 'draft' && (
          <MenuItem onClick={handlePostInvoice}>
            <ReceiptIcon sx={{ mr: 1, fontSize: 20 }} />
            Post Invoice
          </MenuItem>
        )}

        {selectedInvoice?.status !== 'void' && selectedInvoice?.status !== 'paid' && (
          <MenuItem onClick={handleVoidInvoice}>
            <CancelIcon sx={{ mr: 1, fontSize: 20 }} />
            Void Invoice
          </MenuItem>
        )}

        {selectedInvoice?.status === 'draft' && (
          <MenuItem onClick={handleDeleteInvoice} sx={{ color: 'error.main' }}>
            <DeleteIcon sx={{ mr: 1, fontSize: 20 }} />
            Delete Invoice
          </MenuItem>
        )}
      </Menu>

      {/* Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity} sx={{ width: '100%' }}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default CustomerInvoiceListPage;
