#!/usr/bin/env python3
"""
Quick test for validation fix
"""
import requests
import json

# Configuration
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api"
TOKEN = "e15bc01f831c5111f413f534ef82288744cb7d41"

HEADERS = {
    'Content-Type': 'application/json',
    'Authorization': f'Token {TOKEN}'
}

def test_validation_fix():
    print("Testing validation fix...")
    
    # Get vendor
    vendors_response = requests.get(f"{API_BASE}/contacts/vendors/", headers=HEADERS, timeout=5)
    vendor_id = vendors_response.json()['results'][0]['contact']
    
    # Test data with validation issues
    bill_data = {
        "vendor": vendor_id,
        "bill_date": "2025-07-06",
        "due_date": "2025-08-05",
        "status": "draft",
        "line_items": [
            {
                "product": 999,  # Non-existent product
                "item_description": "Test Item",
                "quantity": 1,
                "unit_price": 100.00,
                "tax_rate": 10.0,
                "account_code": "5010-COGS",
                "line_order": 1
            },
            {},  # Empty item
            {
                "item_description": "",  # Blank description
                "quantity": 1,
                "unit_price": 50.00
            }
        ]
    }
    
    response = requests.post(f"{API_BASE}/purchase/vendor-bills/", json=bill_data, headers=HEADERS, timeout=10)
    
    print(f"Status: {response.status_code}")
    if response.status_code == 201:
        print("✅ SUCCESS! Validation issues handled!")
        data = response.json()
        print(f"Created bill {data['bill_number']} with {len(data['line_items'])} line items")
        return True
    else:
        print(f"❌ Still failing: {response.text[:200]}")
        return False

if __name__ == "__main__":
    success = test_validation_fix()
    if success:
        print("\n🎉 Frontend validation errors are now handled!")
    else:
        print("\n❌ Still need to fix validation issues")
