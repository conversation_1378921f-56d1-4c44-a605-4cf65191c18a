# Generated by Django 4.2.21 on 2025-07-08 12:44

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('sales', '0012_customerinvoice_customerinvoiceitem_and_more'),
    ]

    operations = [
        # Remove all unwanted models - keeping only ProductCategory and Product
        migrations.DeleteModel(name='CustomerInvoice'),
        migrations.DeleteModel(name='CustomerInvoiceItem'),
        migrations.DeleteModel(name='SalesOrder'),
        migrations.DeleteModel(name='SalesOrderLineItem'),
        migrations.DeleteModel(name='DeliveryNote'),
        migrations.DeleteModel(name='DeliveryNoteItem'),
        migrations.DeleteModel(name='Estimate'),
        migrations.DeleteModel(name='EstimateLineItem'),
        migrations.DeleteModel(name='Payment'),
        migrations.DeleteModel(name='PaymentTerm'),
    ]
