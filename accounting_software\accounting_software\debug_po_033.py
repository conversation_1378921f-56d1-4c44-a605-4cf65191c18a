#!/usr/bin/env python3

import requests
import json

# Configuration
BASE_URL = "http://localhost:8000/api"
USERNAME = "admin"
PASSWORD = "admin123"

def get_auth_token():
    """Get authentication token"""
    url = f"{BASE_URL}/../api-token-auth/"
    data = {
        "username": USERNAME,
        "password": PASSWORD
    }
    
    try:
        response = requests.post(url, json=data)
        if response.status_code == 200:
            token = response.json().get('token')
            return token
        else:
            print(f"❌ Authentication failed: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ Authentication error: {e}")
        return None

def get_po_details(token, po_id):
    """Get detailed PO information"""
    url = f"{BASE_URL}/purchase/purchase-orders/{po_id}/"
    headers = {
        "Authorization": f"Token {token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(url, headers=headers)
        
        if response.status_code == 200:
            po = response.json()
            print(f"✅ PO Details:")
            print(f"  - ID: {po.get('id')}")
            print(f"  - PO Number: {po.get('po_number')}")
            print(f"  - Status: {po.get('status')}")
            print(f"  - Vendor: {po.get('vendor_name')}")
            print(f"  - Total: {po.get('total_amount')}")
            
            print(f"\n📋 Line Items:")
            for i, item in enumerate(po.get('line_items', [])):
                print(f"  Item {i+1}:")
                print(f"    - Description: {item.get('description')}")
                print(f"    - Product ID: {item.get('product')}")
                print(f"    - Quantity: {item.get('quantity')}")
                print(f"    - Unit Price: {item.get('unit_price')}")
            
            return po
        else:
            print(f"❌ Failed to get PO details: {response.status_code}")
            print(f"Response: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

def get_all_vendor_bills(token):
    """Get all vendor bills to check for PO links"""
    url = f"{BASE_URL}/purchase/vendor-bills/"
    headers = {
        "Authorization": f"Token {token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(url, headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            bills = data.get('results', data) if isinstance(data, dict) else data
            
            print(f"✅ Found {len(bills)} vendor bills:")
            
            po_033_bills = []
            for bill in bills:
                print(f"\n📄 Bill {bill.get('bill_number')}:")
                print(f"  - Status: {bill.get('status')}")
                print(f"  - Source Type: {bill.get('source_type')}")
                print(f"  - Vendor: {bill.get('vendor_name')}")
                print(f"  - Total: {bill.get('total_amount')}")
                
                # Check if this bill might be from PO-000033
                if bill.get('source_type') == 'po':
                    # Check reference or notes for PO number
                    ref = bill.get('reference_number', '')
                    notes = bill.get('notes', '')
                    if 'PO-000033' in ref or 'PO-000033' in notes or '000033' in ref:
                        po_033_bills.append(bill)
                        print(f"  ⚠️  This bill might be from PO-000033!")
            
            return bills, po_033_bills
        else:
            print(f"❌ Failed to get vendor bills: {response.status_code}")
            return None, None
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return None, None

def find_po_by_number(token, po_number):
    """Find PO by number"""
    url = f"{BASE_URL}/purchase/purchase-orders/"
    headers = {
        "Authorization": f"Token {token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(url, headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            pos = data.get('results', data) if isinstance(data, dict) else data
            
            for po in pos:
                if po.get('po_number') == po_number:
                    return po
            
            return None
        else:
            print(f"❌ Failed to get POs: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return None

if __name__ == "__main__":
    print("🔍 Debugging PO-000033")
    print("=" * 50)
    
    token = get_auth_token()
    if not token:
        exit(1)
    
    # Find PO-000033
    po_033 = find_po_by_number(token, "PO-000033")
    if po_033:
        print(f"\n📋 Found PO-000033 with ID: {po_033.get('id')}")
        
        # Get detailed info
        po_details = get_po_details(token, po_033.get('id'))
    else:
        print("❌ PO-000033 not found!")
        exit(1)
    
    # Check all vendor bills
    print("\n" + "=" * 50)
    print("📄 VENDOR BILLS ANALYSIS")
    print("=" * 50)
    
    all_bills, po_033_bills = get_all_vendor_bills(token)
    
    print("\n" + "=" * 50)
    print("📊 CONCLUSION")
    print("=" * 50)
    
    if po_033_bills:
        print(f"❌ Found {len(po_033_bills)} vendor bills that might be from PO-000033")
        print("This explains why PO-000033 is not showing in billable service POs")
    else:
        print("✅ No vendor bills found for PO-000033")
        print("PO-000033 should be showing in billable service POs")
        print("The issue might be elsewhere in the filtering logic")
