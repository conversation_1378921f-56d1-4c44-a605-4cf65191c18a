import React, { useState, useEffect } from 'react';
import {
  Box,
  Typo<PERSON>,
  Card,
  CardContent,
  Button,
  Alert,
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  Stepper,
  Step,
  StepLabel,
  Paper,
  Divider,
} from '@mui/material';
import {
  Assessment as ValuationIcon,
  ArrowBack as BackIcon,
  Save as SaveIcon,
  PlayArrow as GenerateIcon,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useSnackbar } from 'notistack';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import dayjs, { Dayjs } from 'dayjs';
import { PageContainer, PageHeader } from '../../../layouts/components/PageComponents';
import { inventoryService, Warehouse } from '../services/inventory.service';

interface ValuationFormData {
  valuation_date: Dayjs;
  valuation_type: string;
  warehouse_id: string;
  product_category: string;
  notes: string;
}

const CreateValuationPage: React.FC = () => {
  const [formData, setFormData] = useState<ValuationFormData>({
    valuation_date: dayjs(),
    valuation_type: 'AD_HOC',
    warehouse_id: '',
    product_category: '',
    notes: '',
  });
  const [warehouses, setWarehouses] = useState<Warehouse[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeStep, setActiveStep] = useState(0);
  const [generating, setGenerating] = useState(false);

  const navigate = useNavigate();
  const { enqueueSnackbar } = useSnackbar();

  const steps = ['Configuration', 'Review', 'Generate'];

  useEffect(() => {
    loadWarehouses();
  }, []);

  const loadWarehouses = async () => {
    try {
      setLoading(true);
      const response = await inventoryService.getAllWarehouses();
      setWarehouses(response);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load warehouses');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof ValuationFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleNext = () => {
    if (activeStep < steps.length - 1) {
      setActiveStep(prev => prev + 1);
    }
  };

  const handleBack = () => {
    if (activeStep > 0) {
      setActiveStep(prev => prev - 1);
    }
  };

  const handleGenerate = async () => {
    try {
      setGenerating(true);
      setError(null);

      const requestData = {
        valuation_date: formData.valuation_date.format('YYYY-MM-DD'),
        valuation_type: formData.valuation_type,
        warehouse_id: formData.warehouse_id ? parseInt(formData.warehouse_id) : undefined,
        product_category: formData.product_category || undefined,
      };

      const result = await inventoryService.generateStockValuationReport(requestData);
      
      enqueueSnackbar('Valuation report generated successfully', { variant: 'success' });
      navigate(`/dashboard/inventory/valuations/${result.valuation_id}`);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to generate valuation report');
    } finally {
      setGenerating(false);
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'MONTHLY': return 'Monthly Valuation';
      case 'QUARTERLY': return 'Quarterly Valuation';
      case 'ANNUAL': return 'Annual Valuation';
      case 'AD_HOC': return 'Ad-hoc Valuation';
      default: return type;
    }
  };

  const renderStepContent = () => {
    switch (activeStep) {
      case 0:
        return (
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <DatePicker
                label="Valuation Date"
                value={formData.valuation_date}
                onChange={(newValue) => handleInputChange('valuation_date', newValue)}
                slotProps={{
                  textField: {
                    fullWidth: true,
                    required: true,
                  },
                }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth required>
                <InputLabel>Valuation Type</InputLabel>
                <Select
                  value={formData.valuation_type}
                  label="Valuation Type"
                  onChange={(e) => handleInputChange('valuation_type', e.target.value)}
                >
                  <MenuItem value="AD_HOC">Ad-hoc Valuation</MenuItem>
                  <MenuItem value="MONTHLY">Monthly Valuation</MenuItem>
                  <MenuItem value="QUARTERLY">Quarterly Valuation</MenuItem>
                  <MenuItem value="ANNUAL">Annual Valuation</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>Warehouse</InputLabel>
                <Select
                  value={formData.warehouse_id}
                  label="Warehouse"
                  onChange={(e) => handleInputChange('warehouse_id', e.target.value)}
                >
                  <MenuItem value="">All Warehouses</MenuItem>
                  {warehouses.map((warehouse) => (
                    <MenuItem key={warehouse.warehouse_id} value={warehouse.warehouse_id.toString()}>
                      {warehouse.name} ({warehouse.warehouse_code})
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Product Category"
                value={formData.product_category}
                onChange={(e) => handleInputChange('product_category', e.target.value)}
                placeholder="Optional: Filter by category"
                helperText="Leave empty to include all products"
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                multiline
                rows={3}
                label="Notes"
                value={formData.notes}
                onChange={(e) => handleInputChange('notes', e.target.value)}
                placeholder="Optional notes about this valuation..."
              />
            </Grid>
          </Grid>
        );

      case 1:
        return (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Review Valuation Configuration
              </Typography>
              <Divider sx={{ mb: 3 }} />
            </Grid>
            <Grid item xs={12} md={6}>
              <Paper variant="outlined" sx={{ p: 2 }}>
                <Typography variant="subtitle2" color="text.secondary">
                  Valuation Date
                </Typography>
                <Typography variant="body1">
                  {formData.valuation_date.format('MMMM DD, YYYY')}
                </Typography>
              </Paper>
            </Grid>
            <Grid item xs={12} md={6}>
              <Paper variant="outlined" sx={{ p: 2 }}>
                <Typography variant="subtitle2" color="text.secondary">
                  Valuation Type
                </Typography>
                <Typography variant="body1">
                  {getTypeLabel(formData.valuation_type)}
                </Typography>
              </Paper>
            </Grid>
            <Grid item xs={12} md={6}>
              <Paper variant="outlined" sx={{ p: 2 }}>
                <Typography variant="subtitle2" color="text.secondary">
                  Warehouse Scope
                </Typography>
                <Typography variant="body1">
                  {formData.warehouse_id ? 
                    warehouses.find(w => w.warehouse_id.toString() === formData.warehouse_id)?.name || 'Unknown' :
                    'All Warehouses'
                  }
                </Typography>
              </Paper>
            </Grid>
            <Grid item xs={12} md={6}>
              <Paper variant="outlined" sx={{ p: 2 }}>
                <Typography variant="subtitle2" color="text.secondary">
                  Product Category
                </Typography>
                <Typography variant="body1">
                  {formData.product_category || 'All Categories'}
                </Typography>
              </Paper>
            </Grid>
            {formData.notes && (
              <Grid item xs={12}>
                <Paper variant="outlined" sx={{ p: 2 }}>
                  <Typography variant="subtitle2" color="text.secondary">
                    Notes
                  </Typography>
                  <Typography variant="body1">
                    {formData.notes}
                  </Typography>
                </Paper>
              </Grid>
            )}
          </Grid>
        );

      case 2:
        return (
          <Box textAlign="center" py={4}>
            {generating ? (
              <>
                <CircularProgress size={64} sx={{ mb: 3 }} />
                <Typography variant="h6" gutterBottom>
                  Generating Valuation Report...
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  This may take a few moments depending on the number of inventory items.
                </Typography>
              </>
            ) : (
              <>
                <ValuationIcon sx={{ fontSize: 64, color: 'primary.main', mb: 3 }} />
                <Typography variant="h6" gutterBottom>
                  Ready to Generate Report
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                  Click the button below to generate your stock valuation report.
                </Typography>
                <Button
                  variant="contained"
                  size="large"
                  startIcon={<GenerateIcon />}
                  onClick={handleGenerate}
                  disabled={generating}
                >
                  Generate Valuation Report
                </Button>
              </>
            )}
          </Box>
        );

      default:
        return null;
    }
  };

  return (
    <PageContainer>
      <PageHeader>
        <Box>
          <Typography variant="h5" fontWeight="bold">
            Generate Stock Valuation Report
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Create a comprehensive inventory valuation report
          </Typography>
        </Box>
        <Button
          variant="outlined"
          startIcon={<BackIcon />}
          onClick={() => navigate('/dashboard/inventory/valuations')}
        >
          Back to Valuations
        </Button>
      </PageHeader>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Card>
        <CardContent>
          <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
            {steps.map((label) => (
              <Step key={label}>
                <StepLabel>{label}</StepLabel>
              </Step>
            ))}
          </Stepper>

          {renderStepContent()}

          <Box display="flex" justifyContent="space-between" mt={4}>
            <Button
              onClick={handleBack}
              disabled={activeStep === 0 || generating}
            >
              Back
            </Button>
            <Box>
              {activeStep < steps.length - 1 && (
                <Button
                  variant="contained"
                  onClick={handleNext}
                  disabled={loading || generating}
                >
                  Next
                </Button>
              )}
            </Box>
          </Box>
        </CardContent>
      </Card>
    </PageContainer>
  );
};

export default CreateValuationPage;
