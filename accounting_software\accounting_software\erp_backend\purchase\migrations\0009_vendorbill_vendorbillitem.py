# Generated by Django 4.2.21 on 2025-07-04 19:38

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('sales', '0011_update_customer_references'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('contacts', '0006_remove_pricingrule_customer_and_more'),
        ('purchase', '0008_alter_vendorpayment_vendor'),
    ]

    operations = [
        migrations.CreateModel(
            name='VendorBill',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('bill_id', models.UUIDField(default=uuid.uuid4, unique=True)),
                ('bill_number', models.CharField(max_length=50, unique=True)),
                ('bill_date', models.DateField()),
                ('due_date', models.DateField()),
                ('subtotal', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('tax_total', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('total_amount', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('amount_paid', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('balance_due', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('pending', 'Pending'), ('paid', 'Paid'), ('overdue', 'Overdue'), ('cancelled', 'Cancelled')], default='draft', max_length=20)),
                ('reference_number', models.CharField(blank=True, help_text="Vendor's bill/invoice number", max_length=100, null=True)),
                ('notes', models.TextField(blank=True, help_text='Internal notes', null=True)),
                ('terms', models.TextField(blank=True, help_text='Payment terms and conditions', null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='vendor_bills_created', to=settings.AUTH_USER_MODEL)),
                ('vendor', models.ForeignKey(blank=True, help_text='Vendor from contacts system', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='vendor_bills', to='contacts.contact')),
            ],
            options={
                'verbose_name': 'Vendor Bill',
                'verbose_name_plural': 'Vendor Bills',
                'db_table': 'purchase_vendor_bills',
                'ordering': ['-bill_date', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='VendorBillItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('description', models.TextField(help_text='Item description')),
                ('quantity', models.DecimalField(decimal_places=2, default=1.0, max_digits=10)),
                ('unit_cost', models.DecimalField(decimal_places=2, default=0.0, help_text='Cost per unit', max_digits=15)),
                ('line_total', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('tax_rate', models.DecimalField(decimal_places=2, default=0.0, max_digits=5)),
                ('tax_amount', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('line_order', models.PositiveIntegerField(default=0)),
                ('product', models.ForeignKey(blank=True, help_text='Link to product master', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='vendor_bill_items', to='sales.product')),
                ('vendor_bill', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='purchase.vendorbill')),
            ],
            options={
                'verbose_name': 'Vendor Bill Item',
                'verbose_name_plural': 'Vendor Bill Items',
                'db_table': 'purchase_vendor_bill_items',
                'ordering': ['line_order'],
            },
        ),
    ]
