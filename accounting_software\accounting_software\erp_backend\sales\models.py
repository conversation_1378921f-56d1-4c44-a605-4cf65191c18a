from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator, MaxValueValidator
from decimal import Decimal
import uuid
from django.utils import timezone


class PaymentTerm(models.Model):
    """Payment terms that can be used across customers, invoices, etc."""
    
    name = models.CharField(max_length=100, unique=True, help_text="E.g., 'Net 30', 'Due on Receipt'")
    code = models.CharField(max_length=50, unique=True, help_text="E.g., 'net_30', 'due_on_receipt'")
    days = models.PositiveIntegerField(help_text="Number of days from invoice date")
    description = models.TextField(blank=True, null=True, help_text="Optional description")
    is_default = models.BooleanField(default=False, help_text="Is this the default payment term?")
    is_active = models.BooleanField(default=True, help_text="Is this payment term active?")
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='payment_terms_created')
    
    class Meta:
        db_table = 'sales_payment_terms'
        ordering = ['days', 'name']
        verbose_name = 'Payment Term'
        verbose_name_plural = 'Payment Terms'
    
    def __str__(self):
        return f"{self.name} ({self.days} days)"
    
    def save(self, *args, **kwargs):
        # Ensure only one default payment term
        if self.is_default:
            PaymentTerm.objects.filter(is_default=True).update(is_default=False)
        super().save(*args, **kwargs)


# Customer model is now in contacts app - using contacts.Customer instead


class ProductCategory(models.Model):
    """Enhanced Product categories for retail inventory management"""
    
    DIVISION_TYPE_CHOICES = [
        ('perishable', 'Perishable'),
        ('refrigerated', 'Refrigerated'),
        ('frozen', 'Frozen'),
        ('controlled-substance', 'Controlled Substance'),
        ('non-perishable', 'Non-Perishable'),
    ]
    
    UNIT_OF_MEASURE_CHOICES = [
        ('piece', 'Piece'),
        ('kg', 'Kilogram'),
        ('gram', 'Gram'),
        ('liter', 'Liter'),
        ('ml', 'Milliliter'),
        ('box', 'Box'),
        ('pack', 'Pack'),
        ('dozen', 'Dozen'),
        ('meter', 'Meter'),
        ('cm', 'Centimeter'),
    ]
    
    # Basic Information
    name = models.CharField(max_length=100, unique=True)
    code = models.CharField(max_length=20, unique=True, default='CAT', help_text="Short code for the category (e.g., FRT, VEG)")
    description = models.TextField(blank=True, null=True)
    
    # Hierarchy
    parent_category = models.ForeignKey('self', on_delete=models.CASCADE, blank=True, null=True, related_name='subcategories')
    level = models.PositiveIntegerField(default=1, help_text="Category hierarchy level")
    
    # Division Type
    division_type = models.CharField(max_length=30, choices=DIVISION_TYPE_CHOICES, default='non-perishable')
    
    # Visual
    image_url = models.URLField(blank=True, null=True, help_text="Category image URL")
    
    # Business Settings
    tax_category = models.CharField(max_length=50, blank=True, null=True, help_text="Default tax category for products")
    margin_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0.00, help_text="Default profit margin %")
    
    # Settings
    is_active = models.BooleanField(default=True)
    sort_order = models.PositiveIntegerField(default=1, help_text="Display order")
    allow_subcategories = models.BooleanField(default=True)
    requires_expiry_tracking = models.BooleanField(default=False)
    requires_batch_tracking = models.BooleanField(default=False)
    default_unit_of_measure = models.CharField(max_length=20, choices=UNIT_OF_MEASURE_CHOICES, default='piece')
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='categories_created')
    
    class Meta:
        db_table = 'sales_product_categories'
        verbose_name_plural = 'Product Categories'
        ordering = ['sort_order', 'name']
        unique_together = [['parent_category', 'name']]  # Allow same name in different parent categories
    
    def __str__(self):
        return f"{self.code} - {self.name}"
    
    def save(self, *args, **kwargs):
        # Calculate level based on parent
        if self.parent_category:
            self.level = self.parent_category.level + 1
        else:
            self.level = 1
        
        # Validate level doesn't exceed 3
        if self.level > 3:
            raise ValueError("Category hierarchy cannot exceed 3 levels")
        
        super().save(*args, **kwargs)
    
    @property
    def full_path(self):
        """Get full category path (e.g., 'Food > Fruits > Citrus')"""
        if self.parent_category:
            return f"{self.parent_category.full_path} > {self.name}"
        return self.name
    
    @property
    def products_count(self):
        """Count of products in this category"""
        return self.products.filter(status='active').count()
    
    @property
    def subcategories_count(self):
        """Count of subcategories"""
        return self.subcategories.filter(is_active=True).count()
    
    def get_all_subcategories(self):
        """Get all subcategories recursively"""
        subcategories = list(self.subcategories.filter(is_active=True))
        for subcategory in self.subcategories.filter(is_active=True):
            subcategories.extend(subcategory.get_all_subcategories())
        return subcategories


class Product(models.Model):
    """Enhanced Product and Service model with Sales Price Authority"""
    
    PRODUCT_TYPE_CHOICES = [
        ('product', 'Product'),
        ('service', 'Service'),
        ('bundle', 'Bundle'),
    ]
    
    STATUS_CHOICES = [
        ('active', 'Active'),
        ('inactive', 'Inactive'),
    ]
    
    # Basic Information
    product_id = models.UUIDField(default=uuid.uuid4, unique=True)
    name = models.CharField(max_length=200)
    sku = models.CharField(max_length=100, unique=True, blank=True, null=True)
    product_type = models.CharField(max_length=20, choices=PRODUCT_TYPE_CHOICES, default='product')
    category = models.ForeignKey(ProductCategory, on_delete=models.SET_NULL, null=True, blank=True, related_name='products')
    description = models.TextField(blank=True, null=True)
    
    # Pricing - Enhanced with Sales Price Authority
    unit_price = models.DecimalField(
        max_digits=15, 
        decimal_places=2, 
        default=0.00,
        help_text="Sales price (managed by Sales Department)"
    )
    cost_price = models.DecimalField(
        max_digits=15, 
        decimal_places=2, 
        default=0.00, 
        blank=True, 
        null=True,
        help_text="Purchase cost (managed by Purchase Department)"
    )
    minimum_selling_price = models.DecimalField(
        max_digits=15,
        decimal_places=2,
        default=0.00,
        help_text="Minimum allowed selling price (for validation)"
    )
    
    # Price History and Authority
    price_effective_date = models.DateField(
        blank=True, 
        null=True,
        help_text="Date when current price became effective"
    )
    price_last_updated_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='products_priced',
        help_text="User who last updated the sales price"
    )
    price_last_updated_at = models.DateTimeField(
        blank=True,
        null=True,
        help_text="When the price was last updated"
    )
    
    # GL Account Integration - PROPER FOREIGN KEY FIELDS
    income_account_gl = models.ForeignKey(
        'gl.Account',
        on_delete=models.PROTECT,
        null=True,
        blank=True,
        related_name='sales_products',
        limit_choices_to={'account_type__type': 'REVENUE'},
        help_text="Revenue account for sales of this product/service"
    )
    expense_account_gl = models.ForeignKey(
        'gl.Account', 
        on_delete=models.PROTECT,
        null=True,
        blank=True,
        related_name='expense_products',
        limit_choices_to={'account_type__type': 'EXPENSE'},
        help_text="COGS/Expense account for purchasing this product"
    )
    inventory_asset_account_gl = models.ForeignKey(
        'gl.Account',
        on_delete=models.PROTECT,
        null=True,
        blank=True,
        related_name='inventory_products',
        limit_choices_to={'account_type__type': 'ASSET', 'detail_type__code': 'INVENTORY'},
        help_text="Asset account for inventory tracking"
    )
    sales_tax_category = models.CharField(max_length=100, blank=True, null=True)
    
    # Purchasing Information (for products)
    preferred_vendor = models.CharField(max_length=200, blank=True, null=True)
    
    # Inventory (for products only)
    track_inventory = models.BooleanField(default=False)
    reorder_point = models.IntegerField(default=0)
    quantity_on_hand = models.IntegerField(default=0)
    quantity_on_purchase_order = models.IntegerField(default=0)
    quantity_on_sales_order = models.IntegerField(default=0)
    
    # Settings
    status = models.CharField(max_length=10, choices=STATUS_CHOICES, default='active')
    taxable = models.BooleanField(default=True)
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='products_created')
    
    class Meta:
        db_table = 'sales_products'
        ordering = ['name']
    
    def __str__(self):
        return self.name
    
    def save(self, *args, **kwargs):
        # Track price updates
        if self.pk:  # Existing product
            old_product = Product.objects.get(pk=self.pk)
            if old_product.unit_price != self.unit_price:
                self.price_last_updated_at = timezone.now()
                # price_last_updated_by should be set by the view
        super().save(*args, **kwargs)
    
    @property
    def margin_amount(self):
        """Calculate margin amount (sales price - cost price)"""
        if self.cost_price and self.unit_price:
            return self.unit_price - self.cost_price
        return Decimal('0.00')
    
    @property
    def margin_percentage(self):
        """Calculate margin percentage"""
        if self.cost_price and self.cost_price > 0 and self.unit_price:
            return ((self.unit_price - self.cost_price) / self.cost_price) * 100
        return Decimal('0.00')
    
    @property
    def markup_percentage(self):
        """Calculate markup percentage (margin / cost price)"""
        if self.cost_price and self.cost_price > 0:
            return (self.margin_amount / self.cost_price) * 100
        return Decimal('0.00')
    
    def get_current_average_cost(self):
        """Get current weighted average cost from inventory"""
        try:
            from inventory.models import Inventory
            inventory_items = Inventory.objects.filter(product=self)
            total_qty = sum(item.quantity_on_hand for item in inventory_items)
            total_value = sum(item.quantity_on_hand * item.average_cost for item in inventory_items)
            
            if total_qty > 0:
                return total_value / total_qty
            return self.cost_price or Decimal('0.00')
        except:
            return self.cost_price or Decimal('0.00')
    
    def get_sales_account(self):
        """Get the GL account for sales revenue"""
        return self.income_account_gl
    
    def get_cogs_account(self):
        """Get the GL account for cost of goods sold"""
        return self.expense_account_gl
    
    def get_inventory_account(self):
        """Get the GL account for inventory asset"""
        return self.inventory_asset_account_gl
    
    def create_sale_journal_entry(self, quantity, sale_price, customer=None, warehouse=None):
        """Create GL journal entry for a sale of this product with proper COGS"""
        if not self.income_account_gl:
            raise ValueError(f"No income account configured for product {self.name}")
        
        from decimal import Decimal
        from django.utils import timezone
        
        total_revenue = Decimal(str(quantity)) * Decimal(str(sale_price))
        
        # Get COGS from weighted average cost
        cogs_per_unit = self.get_current_average_cost()
        total_cogs = Decimal(str(quantity)) * cogs_per_unit
        
        # Prepare journal entry data
        journal_entry_data = {
            'entry_number': f"SALE-{self.sku}-{timezone.now().strftime('%Y%m%d%H%M%S')}",
            'description': f"Sale of {self.name}",
            'transaction_date': timezone.now().date(),
            'source_document_type': 'SALE',
            'source_document_id': f"SALE-{self.id}",
            'net_amount': total_revenue,
            'lines': []
        }
        
        # Revenue entry: Dr. Accounts Receivable, Cr. Sales Revenue
        journal_entry_data['lines'].extend([
            {
                'account': 'accounts_receivable',  # Will be resolved to proper account
                'description': f"Sale to {customer.display_name if customer else 'Customer'} - {self.name}",
                'debit_amount': total_revenue,
                'credit_amount': Decimal('0.00'),
                'customer': customer.id if customer else None,
                'product': self.id,
                    'quantity': quantity,
                    'unit_price': sale_price
                },
                {
                'account': self.income_account_gl.id,
                'description': f"Sales Revenue - {self.name}",
                'debit_amount': Decimal('0.00'),
                'credit_amount': total_revenue,
                'customer': customer.id if customer else None,
                'product': self.id,
                    'quantity': quantity,
                    'unit_price': sale_price
                }
        ])
        
        # COGS entry (if inventory product): Dr. COGS, Cr. Inventory Asset
        if self.track_inventory and self.expense_account_gl and self.inventory_asset_account_gl:
            journal_entry_data['lines'].extend([
                {
                    'account': self.expense_account_gl.id,
                    'description': f"COGS - {self.name}",
                    'debit_amount': total_cogs,
                    'credit_amount': Decimal('0.00'),
                    'product': self.id,
                    'quantity': quantity,
                    'unit_price': cogs_per_unit
                },
                {
                    'account': self.inventory_asset_account_gl.id,
                    'description': f"Inventory reduction - {self.name}",
                    'debit_amount': Decimal('0.00'),
                    'credit_amount': total_cogs,
                    'product': self.id,
                    'quantity': quantity,
                    'unit_price': cogs_per_unit
                }
            ])
        
        return journal_entry_data


class SalesOrder(models.Model):
    """Sales Order model following QuickBooks structure (mirrors PurchaseOrder)"""
    
    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('pending', 'Pending'),
        ('confirmed', 'Confirmed'),
        ('partial', 'Partially Delivered'),
        ('delivered', 'Delivered'),
        ('invoiced', 'Invoiced'),
        ('closed', 'Closed'),
        ('cancelled', 'Cancelled'),
    ]
    
    # Basic Information
    so_id = models.UUIDField(default=uuid.uuid4, unique=True)
    so_number = models.CharField(max_length=50, unique=True)
    customer = models.ForeignKey('contacts.Contact', on_delete=models.SET_NULL, null=True, blank=True, related_name='sales_orders', help_text='Customer from contacts system')
    
    # Dates
    so_date = models.DateField()
    expected_delivery_date = models.DateField(blank=True, null=True)
    
    # Sales Representative Information
    sales_rep_name = models.CharField(max_length=200, blank=True, null=True, help_text="Name of the sales representative")
    sales_rep_email = models.EmailField(blank=True, null=True, help_text="Email of the sales representative")
    sales_rep_phone = models.CharField(max_length=20, blank=True, null=True, help_text="Phone of the sales representative")
    
    # Financial Information
    subtotal = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    discount_percent = models.DecimalField(max_digits=5, decimal_places=2, default=0.00)
    discount_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    tax_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    total_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    amount_invoiced = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    balance_due = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    
    # Settings
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft')
    payment_terms = models.CharField(max_length=20, blank=True, null=True)
    
    # Additional Information
    customer_po_number = models.CharField(max_length=100, blank=True, null=True)
    memo = models.TextField(blank=True, null=True, help_text="Internal memo")
    notes = models.TextField(blank=True, null=True, help_text="Notes to customer")
    
    # Shipping Information
    ship_to_address = models.TextField(blank=True, null=True)
    shipping_method = models.CharField(max_length=100, blank=True, null=True)
    
    # Email Tracking
    email_sent = models.BooleanField(default=False)
    email_sent_date = models.DateTimeField(blank=True, null=True)
    acknowledged_date = models.DateTimeField(blank=True, null=True)
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='sales_orders_created')
    
    class Meta:
        db_table = 'sales_orders'
        ordering = ['-so_date', '-created_at']
    
    def __str__(self):
        return f"SO {self.so_number} - {self.customer.display_name}"
    
    def save(self, *args, **kwargs):
        # Auto-generate SO number if not provided
        if not self.so_number:
            last_so = SalesOrder.objects.filter(
                so_number__startswith='SO-'
            ).order_by('-created_at').first()
            
            if last_so:
                try:
                    last_number = int(last_so.so_number.split('-')[1])
                    new_number = last_number + 1
                except (ValueError, IndexError):
                    new_number = 1
            else:
                new_number = 1
            
            self.so_number = f"SO-{new_number:06d}"
        
        super().save(*args, **kwargs)
    
    def calculate_totals(self):
        """Calculate totals from line items (mirrors PurchaseOrder)"""
        from decimal import Decimal
        
        line_items = self.line_items.all()
        
        # Calculate subtotal from line items
        self.subtotal = sum(item.line_total for item in line_items) or Decimal('0.00')
        
        # Calculate discount amount
        self.discount_amount = self.subtotal * (Decimal(str(self.discount_percent)) / 100)
        
        # Calculate tax amount from line items
        self.tax_amount = sum(item.tax_amount for item in line_items) or Decimal('0.00')
        
        # Calculate total amount
        self.total_amount = self.subtotal - self.discount_amount + self.tax_amount
        
        # Calculate balance due
        self.balance_due = self.total_amount - Decimal(str(self.amount_invoiced))


class SalesOrderLineItem(models.Model):
    """Sales Order line items for products and services (mirrors PurchaseOrderLineItem)"""
    
    sales_order = models.ForeignKey(SalesOrder, on_delete=models.CASCADE, related_name='line_items')
    
    # Product/Service Link
    product = models.ForeignKey(
        Product, 
        on_delete=models.CASCADE, 
        related_name='sales_line_items',
        null=True,
        blank=True,
        help_text="Link to product master for GL integration"
    )
    
    # Item details
    description = models.TextField(help_text="Auto-filled from product, can be overridden")
    quantity = models.DecimalField(max_digits=10, decimal_places=2, default=1.00)
    unit_of_measure = models.CharField(max_length=20, default='pcs', help_text="Unit of measure (kg, L, pcs, etc.)")
    unit_price = models.DecimalField(max_digits=15, decimal_places=2, default=0.00, help_text="Sales price per unit")
    discount_percent = models.DecimalField(max_digits=5, decimal_places=2, default=0.00)
    line_total = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    
    # Delivery information
    quantity_delivered = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    quantity_pending = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    
    # Tax information
    taxable = models.BooleanField(default=True)
    tax_rate = models.DecimalField(max_digits=5, decimal_places=2, default=0.00)
    tax_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    
    # Additional information
    notes = models.TextField(blank=True, null=True, help_text="Line item specific notes")
    
    # Line item order
    line_order = models.PositiveIntegerField(default=0)
    
    class Meta:
        db_table = 'sales_order_line_items'
        ordering = ['line_order']
    
    def __str__(self):
        return f"{self.sales_order.so_number} - {self.product.name if self.product else self.description}"
    
    def save(self, *args, **kwargs):
        # Calculate line total
        subtotal = self.quantity * self.unit_price
        discount_amount = subtotal * (self.discount_percent / 100)
        self.line_total = subtotal - discount_amount
        
        # Calculate tax amount
        if self.taxable:
            self.tax_amount = self.line_total * (self.tax_rate / 100)
        else:
            self.tax_amount = 0
        
        # Calculate pending quantity
        self.quantity_pending = self.quantity - self.quantity_delivered
        
        super().save(*args, **kwargs)


class DeliveryNote(models.Model):
    """Delivery Note model for tracking product deliveries (mirrors GoodsReceiptNote)"""
    
    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('pending', 'Pending'),
        ('delivered', 'Delivered'),
        ('invoiced', 'Invoiced'),
        ('cancelled', 'Cancelled'),
    ]
    
    # Basic Information
    dn_id = models.UUIDField(default=uuid.uuid4, unique=True)
    dn_number = models.CharField(max_length=50, unique=True)
    sales_order = models.ForeignKey(SalesOrder, on_delete=models.CASCADE, related_name='delivery_notes')
    customer = models.ForeignKey('contacts.Contact', on_delete=models.SET_NULL, null=True, blank=True, related_name='delivery_notes', help_text='Customer from contacts system')
    
    # Dates
    delivery_date = models.DateField()
    expected_delivery_date = models.DateField(blank=True, null=True)
    
    # Delivery Information
    delivered_by = models.CharField(max_length=200, blank=True, null=True, help_text="Name of delivery person")
    delivery_method = models.CharField(max_length=100, blank=True, null=True, help_text="Delivery method (truck, courier, etc.)")
    tracking_number = models.CharField(max_length=100, blank=True, null=True)
    
    # Warehouse Information
    warehouse = models.ForeignKey(
        'inventory.Warehouse',
        on_delete=models.CASCADE,
        related_name='delivery_notes',
        help_text="Warehouse from which goods are delivered"
    )
    
    # Financial Information
    subtotal = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    discount_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    tax_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    total_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)

    # Status and Workflow
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft')
    posted = models.BooleanField(default=False, help_text="Whether delivery has been posted to inventory")
    posted_date = models.DateTimeField(blank=True, null=True)
    posted_by = models.ForeignKey(
        User, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True, 
        related_name='delivery_notes_posted'
    )
    
    # Additional Information
    notes = models.TextField(blank=True, null=True, help_text="Delivery notes")
    customer_signature = models.TextField(blank=True, null=True, help_text="Customer signature or acknowledgment")
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='delivery_notes_created')
    
    class Meta:
        db_table = 'sales_delivery_notes'
        ordering = ['-delivery_date', '-created_at']
    
    def __str__(self):
        return f"DN {self.dn_number} - {self.customer.display_name}"
    
    def save(self, *args, **kwargs):
        # Auto-generate DN number if not provided
        if not self.dn_number:
            last_dn = DeliveryNote.objects.filter(
                dn_number__startswith='DN-'
            ).order_by('-created_at').first()
            
            if last_dn:
                try:
                    last_number = int(last_dn.dn_number.split('-')[1])
                    new_number = last_number + 1
                except (ValueError, IndexError):
                    new_number = 1
            else:
                new_number = 1
            
            self.dn_number = f"DN-{new_number:06d}"
        
        super().save(*args, **kwargs)
    
    def calculate_totals(self):
        """Calculate totals from line items"""
        from decimal import Decimal
        
        line_items = self.line_items.all()
        
        # Calculate subtotal from line items
        self.subtotal = sum(item.line_total for item in line_items) or Decimal('0.00')
        
        # Calculate tax amount from line items
        self.tax_amount = sum(item.tax_amount for item in line_items) or Decimal('0.00')
        
        # Calculate total amount
        self.total_amount = self.subtotal - self.discount_amount + self.tax_amount
    
    def post_delivery(self, user):
        """Post delivery to inventory and update sales order quantities"""
        if self.posted:
            raise ValueError("Delivery note already posted")
        
        from inventory.models import StockTransaction
        from decimal import Decimal
        
        # Create stock transactions for each line item
        for line_item in self.line_items.all():
            if line_item.product and line_item.product.track_inventory:
                # Create outbound stock transaction
                StockTransaction.objects.create(
                    product=line_item.product,
                    warehouse=self.warehouse,
                    transaction_type='SALE',
                    quantity=-abs(line_item.quantity_delivered),  # Negative for outbound
                    notes=f"Delivery via DN {self.dn_number}",
                    reference_number=self.dn_number,
                    transaction_date=self.delivery_date,
                    created_by=user
                )
                
                # Update sales order line item delivered quantities
                so_line_item = line_item.sales_order_line_item
                if so_line_item:
                    so_line_item.quantity_delivered += line_item.quantity_delivered
                    so_line_item.save()
        
        # Mark as posted
        self.posted = True
        self.posted_date = timezone.now()
        self.posted_by = user
        self.status = 'delivered'
        self.save()


class DeliveryNoteItem(models.Model):
    """Delivery Note line items for products delivered"""
    
    delivery_note = models.ForeignKey(DeliveryNote, on_delete=models.CASCADE, related_name='line_items')
    
    # Links to Sales Order
    sales_order_line_item = models.ForeignKey(
        SalesOrderLineItem,
        on_delete=models.CASCADE,
        related_name='delivery_items',
        help_text="Link to original sales order line item"
    )
    
    # Product Information
    product = models.ForeignKey(
        Product,
        on_delete=models.CASCADE,
        related_name='delivery_items',
        help_text="Product being delivered"
    )
    
    # Delivery Details
    description = models.TextField(help_text="Product description")
    quantity_ordered = models.DecimalField(max_digits=10, decimal_places=2, help_text="Original quantity ordered")
    quantity_delivered = models.DecimalField(max_digits=10, decimal_places=2, help_text="Quantity actually delivered")
    unit_of_measure = models.CharField(max_length=20, default='pcs')
    unit_price = models.DecimalField(max_digits=15, decimal_places=2, help_text="Unit price from sales order")
    line_total = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    
    # Tax information
    taxable = models.BooleanField(default=True)
    tax_rate = models.DecimalField(max_digits=5, decimal_places=2, default=0.00)
    tax_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    
    # Inventory Information
    batch_number = models.CharField(max_length=100, blank=True, null=True)
    expiry_date = models.DateField(blank=True, null=True)
    
    # Additional Information
    notes = models.TextField(blank=True, null=True, help_text="Item-specific delivery notes")
    
    # Line item order
    line_order = models.PositiveIntegerField(default=0)
    
    class Meta:
        db_table = 'sales_delivery_note_items'
        ordering = ['line_order']
    
    def __str__(self):
        return f"{self.delivery_note.dn_number} - {self.product.name}"
    
    def save(self, *args, **kwargs):
        # Calculate line total
        self.line_total = self.quantity_delivered * self.unit_price
        
        # Calculate tax amount
        if self.taxable:
            self.tax_amount = self.line_total * (self.tax_rate / 100)
        else:
            self.tax_amount = 0
        
        super().save(*args, **kwargs)
    
    def create_stock_transaction(self):
        """Create stock transaction for this delivery item"""
        from inventory.models import StockTransaction
        
        if self.product.track_inventory:
            return StockTransaction.objects.create(
                product=self.product,
                warehouse=self.delivery_note.warehouse,
                transaction_type='SALE',
                quantity=-abs(self.quantity_delivered),  # Negative for outbound
                notes=f"Delivery via DN {self.delivery_note.dn_number} - {self.product.name}",
                reference_number=self.delivery_note.dn_number,
                transaction_date=self.delivery_note.delivery_date,
                batch_number=self.batch_number,
                expiry_date=self.expiry_date,
                created_by=self.delivery_note.created_by
            )
        return None


# CustomerInvoice models moved to customer_invoice_models.py to avoid conflicts


class Payment(models.Model):
    """Payment model for tracking invoice payments"""
    
    PAYMENT_METHOD_CHOICES = [
        ('cash', 'Cash'),
        ('check', 'Check'),
        ('credit_card', 'Credit Card'),
        ('bank_transfer', 'Bank Transfer'),
        ('online', 'Online Payment'),
        ('other', 'Other'),
    ]
    
    payment_id = models.UUIDField(default=uuid.uuid4, unique=True)
    customer = models.ForeignKey('contacts.Contact', on_delete=models.SET_NULL, null=True, blank=True, related_name='payments', help_text='Customer from contacts system')
    invoice = models.ForeignKey('customer_invoice_models.CustomerInvoice', on_delete=models.CASCADE, related_name='payments', blank=True, null=True)
    
    # Payment details
    payment_date = models.DateField()
    amount = models.DecimalField(max_digits=15, decimal_places=2)
    payment_method = models.CharField(max_length=20, choices=PAYMENT_METHOD_CHOICES, default='cash')
    reference_number = models.CharField(max_length=100, blank=True, null=True)
    notes = models.TextField(blank=True, null=True)
    
    # Deposit information
    deposit_to_account = models.CharField(max_length=200, blank=True, null=True)  # Link to GL account
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='payments_created')
    
    class Meta:
        db_table = 'sales_payments'
        ordering = ['-payment_date']
    
    def __str__(self):
        return f"Payment {self.payment_id} - {self.customer.display_name} - {self.amount}"


class Estimate(models.Model):
    """Estimate/Quote model following QuickBooks structure"""
    
    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('pending', 'Pending'),
        ('sent', 'Sent'),
        ('viewed', 'Viewed'),
        ('accepted', 'Accepted'),
        ('rejected', 'Rejected'),
        ('expired', 'Expired'),
        ('invoiced', 'Converted to Invoice'),
    ]
    
    # Basic Information
    estimate_id = models.UUIDField(default=uuid.uuid4, unique=True)
    estimate_number = models.CharField(max_length=50, unique=True)
    customer = models.ForeignKey('contacts.Contact', on_delete=models.SET_NULL, null=True, blank=True, related_name='estimates', help_text='Customer from contacts system')
    
    # Dates
    estimate_date = models.DateField()
    expiration_date = models.DateField()
    
    # Financial Information
    subtotal = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    discount_percent = models.DecimalField(max_digits=5, decimal_places=2, default=0.00)
    discount_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    tax_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    total_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)

    # Settings
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft')
    
    # Additional Information
    po_number = models.CharField(max_length=100, blank=True, null=True)
    memo = models.TextField(blank=True, null=True)
    message_to_customer = models.TextField(blank=True, null=True)
    
    # Conversion tracking
    converted_to_invoice = models.ForeignKey('customer_invoice_models.CustomerInvoice', on_delete=models.SET_NULL, null=True, blank=True)
    
    # Email Tracking
    email_sent = models.BooleanField(default=False)
    email_sent_date = models.DateTimeField(blank=True, null=True)
    viewed_date = models.DateTimeField(blank=True, null=True)
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='estimates_created')
    
    class Meta:
        db_table = 'sales_estimates'
        ordering = ['-estimate_date', '-created_at']
    
    def __str__(self):
        return f"Estimate {self.estimate_number} - {self.customer.display_name}"
    
    def save(self, *args, **kwargs):
        # Auto-generate estimate number if not provided
        if not self.estimate_number:
            last_estimate = Estimate.objects.filter(
                estimate_number__startswith='EST-'
            ).order_by('-created_at').first()
            
            if last_estimate:
                try:
                    last_number = int(last_estimate.estimate_number.split('-')[1])
                    new_number = last_number + 1
                except (ValueError, IndexError):
                    new_number = 1
            else:
                new_number = 1
            
            self.estimate_number = f'EST-{new_number:06d}'
        
        super().save(*args, **kwargs)


class EstimateLineItem(models.Model):
    """Estimate line items for products and services"""
    
    estimate = models.ForeignKey(Estimate, on_delete=models.CASCADE, related_name='line_items')
    product = models.ForeignKey(Product, on_delete=models.CASCADE, blank=True, null=True)
    
    # Item details (can override product details)
    description = models.TextField()
    quantity = models.DecimalField(max_digits=10, decimal_places=2, default=1.00)
    unit_price = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    discount_percent = models.DecimalField(max_digits=5, decimal_places=2, default=0.00)
    line_total = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    
    # Tax information
    taxable = models.BooleanField(default=True)
    tax_rate = models.DecimalField(max_digits=5, decimal_places=2, default=0.00)
    tax_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    
    # Line item order
    line_order = models.PositiveIntegerField(default=0)
    
    class Meta:
        db_table = 'sales_estimate_line_items'
        ordering = ['line_order']
    
    def save(self, *args, **kwargs):
        # Calculate line total
        subtotal = self.quantity * self.unit_price
        discount_amount = subtotal * (self.discount_percent / 100)
        self.line_total = subtotal - discount_amount
        
        # Calculate tax
        if self.taxable:
            self.tax_amount = self.line_total * (self.tax_rate / 100)
        else:
            self.tax_amount = 0
        
        super().save(*args, **kwargs)
