from django.db import models
from django.contrib.auth.models import User
from decimal import Decimal
import uuid

# Sales module cleaned up - keeping only ProductCategory and Product models


class ProductCategory(models.Model):
    """Enhanced Product categories for retail inventory management"""
    
    DIVISION_TYPE_CHOICES = [
        ('perishable', 'Perishable'),
        ('refrigerated', 'Refrigerated'),
        ('frozen', 'Frozen'),
        ('controlled-substance', 'Controlled Substance'),
        ('non-perishable', 'Non-Perishable'),
    ]
    
    UNIT_OF_MEASURE_CHOICES = [
        ('piece', 'Piece'),
        ('kg', 'Kilogram'),
        ('gram', 'Gram'),
        ('liter', 'Liter'),
        ('ml', 'Milliliter'),
        ('box', 'Box'),
        ('pack', 'Pack'),
        ('dozen', 'Dozen'),
        ('meter', 'Meter'),
        ('cm', 'Centimeter'),
    ]
    
    # Basic Information
    category_id = models.UUIDField(default=uuid.uuid4, unique=True)
    name = models.CharField(max_length=200, unique=True)
    code = models.CharField(max_length=50, unique=True, blank=True, null=True)
    description = models.TextField(blank=True, null=True)
    
    # Hierarchy
    parent_category = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True, related_name='subcategories')
    
    # Category Properties
    division_type = models.CharField(max_length=50, choices=DIVISION_TYPE_CHOICES, default='non-perishable')
    default_unit_of_measure = models.CharField(max_length=20, choices=UNIT_OF_MEASURE_CHOICES, default='piece')
    
    # Inventory Settings
    track_inventory = models.BooleanField(default=True, help_text="Track inventory for products in this category")
    requires_expiry_date = models.BooleanField(default=False, help_text="Products in this category require expiry dates")
    requires_batch_tracking = models.BooleanField(default=False, help_text="Products in this category require batch tracking")
    
    # Pricing Settings
    default_markup_percentage = models.DecimalField(max_digits=5, decimal_places=2, default=0.00, help_text="Default markup % for products in this category")
    
    # Tax Settings
    default_tax_category = models.CharField(max_length=100, blank=True, null=True, help_text="Default tax category for products")
    
    # Status
    is_active = models.BooleanField(default=True)
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='product_categories_created')
    
    class Meta:
        db_table = 'sales_product_categories'
        ordering = ['name']
        verbose_name = 'Product Category'
        verbose_name_plural = 'Product Categories'
    
    def __str__(self):
        if self.parent_category:
            return f"{self.parent_category.name} > {self.name}"
        return self.name
    
    def get_full_path(self):
        """Get the full category path"""
        path = [self.name]
        parent = self.parent_category
        while parent:
            path.insert(0, parent.name)
            parent = parent.parent_category
        return " > ".join(path)
    
    def get_all_subcategories(self):
        """Get all subcategories recursively"""
        subcategories = list(self.subcategories.filter(is_active=True))
        for subcategory in self.subcategories.filter(is_active=True):
            subcategories.extend(subcategory.get_all_subcategories())
        return subcategories


class Product(models.Model):
    """Enhanced Product and Service model with Sales Price Authority"""
    
    PRODUCT_TYPE_CHOICES = [
        ('product', 'Product'),
        ('service', 'Service'),
        ('bundle', 'Bundle'),
    ]
    
    STATUS_CHOICES = [
        ('active', 'Active'),
        ('inactive', 'Inactive'),
    ]
    
    # Basic Information
    product_id = models.UUIDField(default=uuid.uuid4, unique=True)
    name = models.CharField(max_length=200)
    sku = models.CharField(max_length=100, unique=True, blank=True, null=True)
    product_type = models.CharField(max_length=20, choices=PRODUCT_TYPE_CHOICES, default='product')
    category = models.ForeignKey(ProductCategory, on_delete=models.SET_NULL, null=True, blank=True, related_name='products')
    description = models.TextField(blank=True, null=True)
    
    # Pricing - Enhanced with Sales Price Authority
    unit_price = models.DecimalField(
        max_digits=15, 
        decimal_places=2, 
        default=0.00,
        help_text="Sales price (managed by Sales Department)"
    )
    cost_price = models.DecimalField(
        max_digits=15, 
        decimal_places=2, 
        default=0.00,
        help_text="Cost price (managed by Purchase Department)"
    )
    
    # Price Authority Tracking
    price_last_updated_at = models.DateTimeField(auto_now_add=True)
    price_last_updated_by = models.ForeignKey(
        User, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        related_name='product_prices_updated',
        help_text="User who last updated the sales price"
    )
    cost_last_updated_at = models.DateTimeField(auto_now_add=True)
    cost_last_updated_by = models.ForeignKey(
        User, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        related_name='product_costs_updated',
        help_text="User who last updated the cost price"
    )
    
    # Unit of Measure
    unit_of_measure = models.CharField(max_length=20, default='piece')
    
    # GL Account Integration
    income_account_gl = models.CharField(max_length=20, blank=True, null=True, help_text="GL account for sales revenue")
    expense_account_gl = models.CharField(max_length=20, blank=True, null=True, help_text="GL account for COGS")
    inventory_asset_account_gl = models.CharField(max_length=20, blank=True, null=True, help_text="GL account for inventory asset")
    
    # Tax Information
    sales_tax_category = models.CharField(max_length=100, blank=True, null=True)
    
    # Purchasing Information (for products)
    preferred_vendor = models.CharField(max_length=200, blank=True, null=True)
    
    # Inventory (for products only)
    track_inventory = models.BooleanField(default=False)
    reorder_point = models.IntegerField(default=0)
    quantity_on_hand = models.IntegerField(default=0)
    quantity_on_purchase_order = models.IntegerField(default=0)
    quantity_on_sales_order = models.IntegerField(default=0)
    
    # Settings
    status = models.CharField(max_length=10, choices=STATUS_CHOICES, default='active')
    taxable = models.BooleanField(default=True)
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='products_created')
    
    class Meta:
        db_table = 'sales_products'
        ordering = ['name']
        verbose_name = 'Product'
        verbose_name_plural = 'Products'
    
    def __str__(self):
        return f"{self.name} ({self.sku})" if self.sku else self.name
    
    @property
    def margin_amount(self):
        """Calculate margin amount (sales price - cost price)"""
        if self.cost_price and self.unit_price:
            return self.unit_price - self.cost_price
        return Decimal('0.00')
    
    @property
    def margin_percentage(self):
        """Calculate margin percentage"""
        if self.cost_price and self.cost_price > 0 and self.unit_price:
            return ((self.unit_price - self.cost_price) / self.cost_price) * 100
        return Decimal('0.00')
    
    @property
    def markup_percentage(self):
        """Calculate markup percentage (margin / cost price)"""
        if self.cost_price and self.cost_price > 0:
            return (self.margin_amount / self.cost_price) * 100
        return Decimal('0.00')
    
    def get_current_average_cost(self):
        """Get current weighted average cost from inventory"""
        try:
            from inventory.models import Inventory
            inventory_items = Inventory.objects.filter(product=self)
            total_qty = sum(item.quantity_on_hand for item in inventory_items)
            total_value = sum(item.quantity_on_hand * item.average_cost for item in inventory_items)
            
            if total_qty > 0:
                return total_value / total_qty
            return self.cost_price or Decimal('0.00')
        except:
            return self.cost_price or Decimal('0.00')
    
    def get_sales_account(self):
        """Get the GL account for sales revenue"""
        return self.income_account_gl
    
    def get_cogs_account(self):
        """Get the GL account for cost of goods sold"""
        return self.expense_account_gl
    
    def get_inventory_account(self):
        """Get the GL account for inventory asset"""
        return self.inventory_asset_account_gl
    
    def save(self, *args, **kwargs):
        # Track price changes
        if self.pk:  # Existing product
            old_product = Product.objects.get(pk=self.pk)
            if old_product.unit_price != self.unit_price:
                from django.utils import timezone
                self.price_last_updated_at = timezone.now()
                # price_last_updated_by should be set by the view
        super().save(*args, **kwargs)
