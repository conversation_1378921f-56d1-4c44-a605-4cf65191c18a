"""
Enterprise Inventory Serializers for ERP System

This module contains DRF serializers for inventory operations:
- Warehouse and location serializers
- Inventory and cost layer serializers
- Stock transaction serializers
- Stock adjustment serializers
- Alert and valuation serializers
"""

from rest_framework import serializers
from decimal import Decimal
from .models import (
    UnitsOfMeasure, Warehouse, WarehouseLocation, Inventory, InventoryCostLayer,
    StockTransaction, StockAdjustment, StockAdjustmentItem, LowStockAlert,
    StockValuation, StockValuationItem, GoodsReceiptNote, GoodsReceiptNoteItem,
    VendorInvoice, GoodsReturnNote, GoodsReturnNoteItem, InventoryTransfer,
    InventoryTransferItem
)
from purchase.models import PurchaseOrder
from sales.models import Product


class UnitsOfMeasureSerializer(serializers.ModelSerializer):
    """Enhanced serializer for Units of Measure"""

    class Meta:
        model = UnitsOfMeasure
        fields = '__all__'
        read_only_fields = ('created_at', 'updated_at')


class WarehouseLocationSerializer(serializers.ModelSerializer):
    """Serializer for Warehouse Locations"""

    warehouse_name = serializers.CharField(source='warehouse.name', read_only=True)
    current_utilization_percent = serializers.SerializerMethodField()

    class Meta:
        model = WarehouseLocation
        fields = '__all__'
        read_only_fields = ('created_at', 'updated_at', 'location_code')

    def get_current_utilization_percent(self, obj):
        return float(obj.current_utilization)


class WarehouseSerializer(serializers.ModelSerializer):
    """Enhanced serializer for Warehouses"""

    locations = WarehouseLocationSerializer(many=True, read_only=True)
    total_locations = serializers.SerializerMethodField()
    active_locations = serializers.SerializerMethodField()
    created_by_name = serializers.CharField(source='created_by.username', read_only=True)

    class Meta:
        model = Warehouse
        fields = '__all__'
        read_only_fields = ('created_at', 'updated_at')

    def get_total_locations(self, obj):
        return obj.locations.count()

    def get_active_locations(self, obj):
        return obj.locations.filter(is_active=True).count()


class InventoryCostLayerSerializer(serializers.ModelSerializer):
    """Serializer for Inventory Cost Layers"""

    product_name = serializers.CharField(source='inventory.product.name', read_only=True)
    warehouse_name = serializers.CharField(source='inventory.warehouse.name', read_only=True)
    is_depleted = serializers.BooleanField(read_only=True)
    total_value = serializers.DecimalField(max_digits=15, decimal_places=2, read_only=True)

    class Meta:
        model = InventoryCostLayer
        fields = '__all__'
        read_only_fields = ('created_at', 'updated_at', 'is_depleted', 'total_value')


class EnhancedInventorySerializer(serializers.ModelSerializer):
    """Enhanced serializer for Inventory with enterprise features"""

    product_name = serializers.CharField(source='product.name', read_only=True)
    product_sku = serializers.CharField(source='product.sku', read_only=True)
    warehouse_name = serializers.CharField(source='warehouse.name', read_only=True)
    location_code = serializers.CharField(source='location.location_code', read_only=True)

    # Calculated fields
    available_quantity = serializers.DecimalField(max_digits=15, decimal_places=4, read_only=True)
    is_below_reorder_point = serializers.BooleanField(read_only=True)
    is_out_of_stock = serializers.BooleanField(read_only=True)
    total_value_average = serializers.DecimalField(max_digits=15, decimal_places=2, read_only=True)
    total_value_last = serializers.DecimalField(max_digits=15, decimal_places=2, read_only=True)

    # Cost layers
    cost_layers = InventoryCostLayerSerializer(many=True, read_only=True)

    class Meta:
        model = Inventory
        fields = '__all__'
        read_only_fields = ('last_updated', 'last_transaction_date', 'last_count_date')


class StockAdjustmentItemSerializer(serializers.ModelSerializer):
    """Serializer for Stock Adjustment Items"""

    product_name = serializers.CharField(source='product.name', read_only=True)
    product_sku = serializers.CharField(source='product.sku', read_only=True)
    warehouse_name = serializers.CharField(source='warehouse.name', read_only=True)
    location_code = serializers.CharField(source='location.location_code', read_only=True)
    adjustment_quantity = serializers.DecimalField(max_digits=15, decimal_places=4, read_only=True)
    adjustment_value = serializers.DecimalField(max_digits=15, decimal_places=2, read_only=True)

    class Meta:
        model = StockAdjustmentItem
        fields = '__all__'
        read_only_fields = ('adjustment_quantity', 'adjustment_value')


class StockAdjustmentSerializer(serializers.ModelSerializer):
    """Serializer for Stock Adjustments"""

    items = StockAdjustmentItemSerializer(many=True, read_only=True)
    created_by_name = serializers.CharField(source='created_by.username', read_only=True)
    approved_by_name = serializers.CharField(source='approved_by.username', read_only=True)
    posted_by_name = serializers.CharField(source='posted_by.username', read_only=True)

    class Meta:
        model = StockAdjustment
        fields = '__all__'
        read_only_fields = (
            'adjustment_number', 'total_adjustment_value', 'created_at', 'updated_at',
            'approved_at', 'posted_at'
        )


class LowStockAlertSerializer(serializers.ModelSerializer):
    """Serializer for Low Stock Alerts"""

    product_name = serializers.CharField(source='inventory.product.name', read_only=True)
    product_sku = serializers.CharField(source='inventory.product.sku', read_only=True)
    warehouse_name = serializers.CharField(source='inventory.warehouse.name', read_only=True)
    location_code = serializers.CharField(source='inventory.location.location_code', read_only=True)
    acknowledged_by_name = serializers.CharField(source='acknowledged_by.username', read_only=True)
    resolved_by_name = serializers.CharField(source='resolved_by.username', read_only=True)

    class Meta:
        model = LowStockAlert
        fields = '__all__'
        read_only_fields = ('created_at', 'acknowledged_at', 'resolved_at')


class GoodsReceiptNoteItemSerializer(serializers.ModelSerializer):
    product_name = serializers.CharField(source='product.name', read_only=True, allow_null=True)
    product_sku = serializers.CharField(source='product.sku', read_only=True, allow_null=True)
    product = serializers.PrimaryKeyRelatedField(
        queryset=Product.objects.all(),
        allow_null=True, 
        required=False
    )
    
    class Meta:
        model = GoodsReceiptNoteItem
        fields = [
            'grn_item_id', 'product', 'product_name', 'product_sku',
            'quantity_ordered', 'quantity_received', 'unit_cost', 'total_cost',
            'condition', 'batch_number', 'expiry_date', 'notes', 'line_order'
        ]


class GoodsReceiptNoteSerializer(serializers.ModelSerializer):
    items = GoodsReceiptNoteItemSerializer(many=True, read_only=True)
    purchase_order_number = serializers.CharField(source='purchase_order.po_number', read_only=True)
    vendor_name = serializers.CharField(source='purchase_order.vendor.display_name', read_only=True)
    warehouse_name = serializers.CharField(source='warehouse.name', read_only=True)
    received_by_name = serializers.CharField(source='received_by.get_full_name', read_only=True)
    
    class Meta:
        model = GoodsReceiptNote
        fields = [
            'grn_id', 'grn_number', 'purchase_order', 'purchase_order_number',
            'vendor_name', 'warehouse', 'warehouse_name', 'received_by', 
            'received_by_name', 'receipt_date', 'status', 'notes',
            'total_quantity', 'total_value', 'created_at', 'updated_at',
            'posted_at', 'posted_by', 'items'
        ]
        read_only_fields = ['grn_id', 'grn_number', 'created_at', 'updated_at']


class GoodsReceiptNoteCreateSerializer(serializers.ModelSerializer):
    items = GoodsReceiptNoteItemSerializer(many=True)
    
    class Meta:
        model = GoodsReceiptNote
        fields = [
            'purchase_order', 'warehouse', 'receipt_date', 'notes', 'items'
        ]
    
    def validate(self, data):
        """Validate GRN creation with proper duplicate and quantity checks"""
        purchase_order = data.get('purchase_order')
        items_data = data.get('items', [])
        
        if not purchase_order:
            raise serializers.ValidationError("Purchase Order is required")
        
        # Check if PO is in valid status for GRN creation
        if purchase_order.status not in ['sent', 'acknowledged', 'partial']:
            raise serializers.ValidationError(
                f"Cannot create GRN for PO with status '{purchase_order.status}'. "
                f"PO must be in 'sent', 'acknowledged', or 'partial' status."
            )

        # Check if PO contains services (services don't require GRN)
        has_services = False
        has_goods = False

        for line_item in purchase_order.line_items.all():
            if line_item.product:
                if line_item.product.product_type == 'service':
                    has_services = True
                elif line_item.product.product_type == 'product':
                    has_goods = True
            else:
                # If no product linked, assume it's a service line item
                has_services = True

        # Block service POs from GRN creation
        if has_services and not has_goods:
            raise serializers.ValidationError(
                "Cannot create GRN for service Purchase Orders. "
                "Services are intangible and don't require physical receipt. "
                "Create vendor bills directly from service POs instead."
            )

        # For mixed POs (both goods and services), allow GRN but warn
        if has_services and has_goods:
            # This is allowed but we could add a warning in the future
            pass
        
        # Get existing GRNs for this PO (excluding cancelled ones)
        existing_grns = GoodsReceiptNote.objects.filter(
            purchase_order=purchase_order,
            status__in=['DRAFT', 'RECEIVED', 'POSTED']
        )
        
        # Calculate total quantities from PO line items
        po_line_items = purchase_order.line_items.all()
        po_quantities = {item.product_id: item.quantity for item in po_line_items if item.product_id}
        total_po_quantity = sum(item.quantity for item in po_line_items)
        
        # Calculate already received quantities from existing GRNs
        already_received = {}
        total_already_received = 0
        
        for grn in existing_grns:
            for grn_item in grn.items.all():
                product_id = grn_item.product_id
                if product_id:  # Only process if product is linked
                    if product_id in already_received:
                        already_received[product_id] += grn_item.quantity_received
                    else:
                        already_received[product_id] = grn_item.quantity_received
                total_already_received += grn_item.quantity_received
        
        # Check if PO is already fully received
        if total_already_received >= total_po_quantity:
            raise serializers.ValidationError(
                f"Cannot create GRN: Purchase Order {purchase_order.po_number} is already fully received. "
                f"All {total_po_quantity} units have been received in previous GRNs."
            )
        
        # Validate each item in the new GRN
        for item_data in items_data:
            product = item_data.get('product')
            quantity_to_receive = item_data.get('quantity_received', 0)
            
            if not product:
                continue  # Skip validation for items without product link
            
            # Get product_id from the Product instance or integer
            if hasattr(product, 'id'):
                product_id = product.id
            else:
                # Handle case where product is already an integer ID
                try:
                    product_id = int(product)
                except (ValueError, TypeError):
                    raise serializers.ValidationError(
                        f"Invalid product ID '{product}'. Product ID must be a number or Product instance."
                    )
            
            # Get ordered quantity for this product
            ordered_quantity = po_quantities.get(product_id, 0)
            if ordered_quantity == 0:
                # Provide more detailed error message
                available_products = list(po_quantities.keys())
                raise serializers.ValidationError(
                    f"Product ID {product_id} was not ordered in PO {purchase_order.po_number}. "
                    f"Available products in this PO: {available_products}"
                )
            
            # Get already received quantity for this product
            received_quantity = already_received.get(product_id, 0)
            
            # Check if trying to receive more than remaining
            remaining_quantity = ordered_quantity - received_quantity
            if quantity_to_receive > remaining_quantity:
                raise serializers.ValidationError(
                    f"Cannot receive {quantity_to_receive} units of product ID {product_id}. "
                    f"Ordered: {ordered_quantity}, Already received: {received_quantity}, "
                    f"Remaining: {remaining_quantity}"
                )
        
        return data
    
    def create(self, validated_data):
        items_data = validated_data.pop('items', [])
        validated_data['received_by'] = self.context['request'].user
        
        grn = GoodsReceiptNote.objects.create(**validated_data)
        
        total_quantity = 0
        total_value = 0
        
        for item_data in items_data:
            item = GoodsReceiptNoteItem.objects.create(
                grn=grn,
                **item_data
            )
            total_quantity += item.quantity_received
            total_value += item.total_cost
        
        # Update totals
        grn.total_quantity = total_quantity
        grn.total_value = total_value
        grn.save()
        
        # DON'T update PO line item quantities during GRN creation (DRAFT status)
        # This will be done when the GRN is posted to inventory
        # This allows multiple draft GRNs to be created for the same PO
        
        # Only update PO status to show that GRN creation has started
        purchase_order = grn.purchase_order
        if purchase_order.status == 'sent' or purchase_order.status == 'acknowledged':
            # Don't change status during GRN creation - wait for posting
            pass  # Keep current status
        
        return grn


class InventorySerializer(serializers.ModelSerializer):
    product_name = serializers.CharField(source='product.name', read_only=True)
    product_sku = serializers.CharField(source='product.sku', read_only=True)
    warehouse_name = serializers.CharField(source='warehouse.name', read_only=True)
    quantity_available = serializers.DecimalField(source='available_quantity', max_digits=15, decimal_places=4, read_only=True)
    
    class Meta:
        model = Inventory
        fields = [
            'inventory_id', 'product', 'product_name', 'product_sku',
            'warehouse', 'warehouse_name', 'quantity_on_hand', 
            'quantity_reserved', 'quantity_available', 'reorder_point',
            'reorder_quantity', 'average_cost', 'last_cost', 
            'last_transaction_date', 'last_updated'
        ]


class StockTransactionSerializer(serializers.ModelSerializer):
    product_name = serializers.CharField(source='product.name', read_only=True)
    product_sku = serializers.CharField(source='product.sku', read_only=True)
    warehouse_name = serializers.CharField(source='warehouse.name', read_only=True)
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    
    class Meta:
        model = StockTransaction
        fields = [
            'stock_txn_id', 'product', 'product_name', 'product_sku', 
            'warehouse', 'warehouse_name', 'transaction_type', 'reference_type',
            'reference_id', 'quantity', 'unit_cost', 'total_cost', 'txn_date',
            'description', 'batch_number', 'created_by', 'created_by_name', 'created_at'
        ]


class GoodsReturnNoteItemSerializer(serializers.ModelSerializer):
    product_name = serializers.CharField(source='product.name', read_only=True)
    product_sku = serializers.CharField(source='product.sku', read_only=True)
    original_grn_number = serializers.CharField(source='original_grn_item.grn.grn_number', read_only=True)
    
    class Meta:
        model = GoodsReturnNoteItem
        fields = [
            'grn_return_item_id', 'product', 'product_name', 'product_sku',
            'original_grn_item', 'original_grn_number', 'quantity_received', 
            'quantity_returned', 'unit_cost', 'return_value', 'return_reason',
            'batch_number', 'condition_notes', 'photos_attached', 'line_order'
        ]
        read_only_fields = ['return_value']


class GoodsReturnNoteSerializer(serializers.ModelSerializer):
    items = GoodsReturnNoteItemSerializer(many=True, read_only=True)
    vendor_name = serializers.CharField(source='vendor.name', read_only=True)
    warehouse_name = serializers.CharField(source='warehouse.name', read_only=True)
    returned_by_name = serializers.CharField(source='returned_by.get_full_name', read_only=True)
    original_grn_number = serializers.CharField(source='original_grn.grn_number', read_only=True)
    approved_by_name = serializers.CharField(source='approved_by.get_full_name', read_only=True)
    posted_by_name = serializers.CharField(source='posted_by.get_full_name', read_only=True)
    
    class Meta:
        model = GoodsReturnNote
        fields = [
            'grn_return_id', 'grn_return_number', 'original_grn', 'original_grn_number',
            'vendor', 'vendor_name', 'warehouse', 'warehouse_name', 'returned_by', 
            'returned_by_name', 'return_date', 'return_reason', 'status', 'notes',
            'total_quantity', 'total_value', 'expected_credit_amount', 'actual_credit_amount',
            'credit_received_date', 'created_at', 'updated_at', 'approved_at', 'approved_by',
            'approved_by_name', 'posted_at', 'posted_by', 'posted_by_name', 'items'
        ]
        read_only_fields = [
            'grn_return_id', 'grn_return_number', 'created_at', 'updated_at',
            'total_quantity', 'total_value'
        ]


class GoodsReturnNoteCreateSerializer(serializers.ModelSerializer):
    items = GoodsReturnNoteItemSerializer(many=True)
    
    class Meta:
        model = GoodsReturnNote
        fields = [
            'original_grn', 'warehouse', 'return_date', 'return_reason', 'notes', 'items'
        ]
    
    def validate(self, data):
        """Validate Good Return Note creation"""
        original_grn = data.get('original_grn')
        items_data = data.get('items', [])
        
        if not original_grn:
            raise serializers.ValidationError("Original GRN is required")
        
        # Check if original GRN is in valid status for returns
        if original_grn.status not in ['RECEIVED', 'POSTED']:
            raise serializers.ValidationError(
                f"Cannot create return for GRN with status '{original_grn.status}'. "
                f"GRN must be in 'RECEIVED' or 'POSTED' status."
            )
        
        # Get original GRN items for validation
        original_grn_items = {item.grn_item_id: item for item in original_grn.items.all()}
        
        # Get existing returns for this GRN to calculate already returned quantities
        existing_returns = GoodsReturnNote.objects.filter(
            original_grn=original_grn,
            status__in=['DRAFT', 'APPROVED', 'RETURNED', 'POSTED']
        )
        
        already_returned = {}
        for return_note in existing_returns:
            for return_item in return_note.items.all():
                original_item_id = return_item.original_grn_item_id
                if original_item_id in already_returned:
                    already_returned[original_item_id] += return_item.quantity_returned
                else:
                    already_returned[original_item_id] = return_item.quantity_returned
        
        # Validate each return item
        for item_data in items_data:
            original_grn_item = item_data.get('original_grn_item')
            quantity_to_return = item_data.get('quantity_returned', 0)
            
            if not original_grn_item:
                raise serializers.ValidationError("Original GRN item is required for each return item")
            
            # Get original_grn_item_id
            if hasattr(original_grn_item, 'grn_item_id'):
                original_item_id = original_grn_item.grn_item_id
            else:
                try:
                    original_item_id = int(original_grn_item)
                    original_grn_item = original_grn_items.get(original_item_id)
                    if not original_grn_item:
                        raise serializers.ValidationError(
                            f"Original GRN item ID {original_item_id} not found in GRN {original_grn.grn_number}"
                        )
                except (ValueError, TypeError):
                    raise serializers.ValidationError(
                        f"Invalid original GRN item ID '{original_grn_item}'"
                    )
            
            # Validate return quantity
            if quantity_to_return <= 0:
                raise serializers.ValidationError("Return quantity must be greater than 0")
            
            # Check available quantity for return
            original_quantity = original_grn_item.quantity_received
            already_returned_qty = already_returned.get(original_item_id, 0)
            available_for_return = original_quantity - already_returned_qty
            
            if quantity_to_return > available_for_return:
                raise serializers.ValidationError(
                    f"Cannot return {quantity_to_return} units of {original_grn_item.product.name if original_grn_item.product else 'item'}. "
                    f"Original quantity: {original_quantity}, Already returned: {already_returned_qty}, "
                    f"Available for return: {available_for_return}"
                )
            
            # Ensure product matches
            expected_product = original_grn_item.product
            return_product = item_data.get('product')
            if expected_product and return_product and expected_product != return_product:
                raise serializers.ValidationError(
                    f"Product mismatch: Expected {expected_product.name}, got {return_product.name}"
                )
        
        return data
    
    def create(self, validated_data):
        items_data = validated_data.pop('items', [])
        
        # Set vendor from original GRN
        original_grn = validated_data['original_grn']
        validated_data['vendor'] = original_grn.purchase_order.vendor
        validated_data['returned_by'] = self.context['request'].user
        
        return_note = GoodsReturnNote.objects.create(**validated_data)
        
        total_quantity = 0
        total_value = 0
        
        for item_data in items_data:
            # Ensure required fields are set from original GRN item
            original_grn_item = item_data['original_grn_item']
            if hasattr(original_grn_item, 'grn_item_id'):
                # It's already a GoodsReceiptNoteItem instance
                pass
            else:
                # It's an ID, get the instance
                original_grn_item = GoodsReceiptNoteItem.objects.get(grn_item_id=int(original_grn_item))
                item_data['original_grn_item'] = original_grn_item
            
            # Set product and unit cost from original item
            item_data['product'] = original_grn_item.product
            item_data['unit_cost'] = original_grn_item.unit_cost
            item_data['quantity_received'] = original_grn_item.quantity_received
            
            item = GoodsReturnNoteItem.objects.create(
                grn_return=return_note,
                **item_data
            )
            total_quantity += item.quantity_returned
            total_value += item.return_value
        
        # Update totals
        return_note.total_quantity = total_quantity
        return_note.total_value = total_value
        return_note.expected_credit_amount = total_value
        return_note.save()
        
        return return_note


class InventoryTransferItemSerializer(serializers.ModelSerializer):
    product_name = serializers.CharField(source='product.name', read_only=True)
    product_sku = serializers.CharField(source='product.sku', read_only=True)
    
    class Meta:
        model = InventoryTransferItem
        fields = [
            'transfer_item_id', 'product', 'product_name', 'product_sku',
            'quantity', 'batch_number', 'notes', 'line_order'
        ]


class InventoryTransferSerializer(serializers.ModelSerializer):
    items = InventoryTransferItemSerializer(many=True, read_only=True)
    from_warehouse_name = serializers.CharField(source='from_warehouse.name', read_only=True)
    to_warehouse_name = serializers.CharField(source='to_warehouse.name', read_only=True)
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)
    completed_by_name = serializers.CharField(source='completed_by.get_full_name', read_only=True)
    
    class Meta:
        model = InventoryTransfer
        fields = [
            'transfer_id', 'transfer_number', 'from_warehouse', 'from_warehouse_name',
            'to_warehouse', 'to_warehouse_name', 'transfer_date', 'notes', 'status',
            'total_quantity', 'total_items', 'created_at', 'updated_at', 'created_by',
            'created_by_name', 'completed_at', 'completed_by', 'completed_by_name', 'items'
        ]
        read_only_fields = [
            'transfer_id', 'transfer_number', 'created_at', 'updated_at',
            'total_quantity', 'total_items', 'completed_at', 'completed_by'
        ]


class InventoryTransferCreateSerializer(serializers.ModelSerializer):
    items = InventoryTransferItemSerializer(many=True)
    
    class Meta:
        model = InventoryTransfer
        fields = [
            'from_warehouse', 'to_warehouse', 'transfer_date', 'notes', 'items'
        ]
    
    def validate(self, data):
        """Validate inventory transfer creation"""
        from_warehouse = data.get('from_warehouse')
        to_warehouse = data.get('to_warehouse')
        items_data = data.get('items', [])
        
        # Check warehouse validity
        if from_warehouse == to_warehouse:
            raise serializers.ValidationError("Source and destination warehouses cannot be the same")
        
        # Validate items
        if not items_data:
            raise serializers.ValidationError("At least one item is required for transfer")
        
        # Check stock availability for each item
        for item_data in items_data:
            product = item_data.get('product')
            quantity = item_data.get('quantity', 0)
            
            if quantity <= 0:
                raise serializers.ValidationError("Transfer quantity must be greater than 0")
            
            # Check available stock in source warehouse
            try:
                inventory = Inventory.objects.get(
                    product=product,
                    warehouse=from_warehouse
                )
                available_qty = inventory.available_quantity
                
                if quantity > available_qty:
                    raise serializers.ValidationError(
                        f"Insufficient stock for {product.name}. "
                        f"Available: {available_qty}, Requested: {quantity}"
                    )
            except Inventory.DoesNotExist:
                raise serializers.ValidationError(
                    f"Product {product.name} not found in source warehouse {from_warehouse.name}"
                )
        
        return data
    
    def create(self, validated_data):
        items_data = validated_data.pop('items', [])
        
        # Set created_by from request user
        validated_data['created_by'] = self.context['request'].user
        
        transfer = InventoryTransfer.objects.create(**validated_data)
        
        total_quantity = 0
        
        for item_data in items_data:
            item = InventoryTransferItem.objects.create(
                transfer=transfer,
                **item_data
            )
            total_quantity += item.quantity
        
        # Update totals
        transfer.total_quantity = total_quantity
        transfer.total_items = len(items_data)
        transfer.save()

        return transfer


class StockValuationItemSerializer(serializers.ModelSerializer):
    """Serializer for Stock Valuation Items"""

    product_name = serializers.CharField(source='inventory.product.name', read_only=True)
    product_sku = serializers.CharField(source='inventory.product.sku', read_only=True)
    warehouse_name = serializers.CharField(source='inventory.warehouse.name', read_only=True)

    class Meta:
        model = StockValuationItem
        fields = [
            'valuation_item_id', 'valuation', 'inventory', 'product_name', 'product_sku',
            'warehouse_name', 'quantity_on_hand',
            'fifo_unit_cost', 'fifo_total_value',
            'lifo_unit_cost', 'lifo_total_value',
            'average_unit_cost', 'average_total_value',
            'standard_unit_cost', 'standard_total_value'
        ]
        read_only_fields = ['valuation_item_id']


class StockValuationSerializer(serializers.ModelSerializer):
    """Serializer for Stock Valuations"""

    items = StockValuationItemSerializer(many=True, read_only=True)
    warehouse_name = serializers.CharField(source='warehouse.name', read_only=True)
    created_by_name = serializers.CharField(source='created_by.get_full_name', read_only=True)

    class Meta:
        model = StockValuation
        fields = [
            'valuation_id', 'valuation_date', 'valuation_type', 'warehouse', 'warehouse_name',
            'product_category', 'status', 'total_items', 'total_quantity',
            'fifo_total_value', 'lifo_total_value', 'average_total_value', 'standard_total_value',
            'created_by', 'created_by_name', 'created_at', 'completed_at', 'items'
        ]
        read_only_fields = [
            'valuation_id', 'total_items', 'total_quantity',
            'fifo_total_value', 'lifo_total_value', 'average_total_value', 'standard_total_value',
            'created_at', 'completed_at'
        ]