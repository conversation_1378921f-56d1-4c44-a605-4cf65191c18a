#!/usr/bin/env python3
"""
Test vendor bill creation with products
"""
import requests
import json

# Configuration
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api"
TOKEN = "e15bc01f831c5111f413f534ef82288744cb7d41"

HEADERS = {
    'Content-Type': 'application/json',
    'Authorization': f'Token {TOKEN}'
}

def test_bill_with_product():
    print("🧪 VENDOR BILL WITH PRODUCT TEST")
    print("=" * 35)
    
    try:
        # Get vendor and products
        vendors_response = requests.get(f"{API_BASE}/contacts/vendors/", headers=HEADERS, timeout=5)
        vendor_id = vendors_response.json()['results'][0]['contact']
        
        products_response = requests.get(f"{API_BASE}/sales/products/", headers=HEADERS, timeout=5)
        products = products_response.json()['results']
        
        if not products:
            print("❌ No products found")
            return False
            
        product_id = products[0]['id']
        print(f"Using product ID: {product_id}")
        
        # Bill with one product
        bill_data = {
            "vendor": vendor_id,
            "bill_date": "2025-07-06",
            "due_date": "2025-08-05",
            "status": "draft",
            "payment_terms": "Net 30",
            "reference_number": "PRODUCT-001",
            "notes": "Test with product",
            "line_items": [
                {
                    "product": product_id,
                    "item_description": "Product line item",
                    "quantity": 2,
                    "unit_price": 150.00,
                    "tax_rate": 10.0,
                    "account_code": "5010-COGS",
                    "line_order": 1
                }
            ]
        }
        
        print("Creating bill with product...")
        response = requests.post(f"{API_BASE}/purchase/vendor-bills/", json=bill_data, headers=HEADERS, timeout=10)
        
        print(f"Response status: {response.status_code}")
        if response.status_code == 201:
            bill = response.json()
            print(f"✅ Bill created: {bill['bill_number']}")
            print(f"Line items: {len(bill['line_items'])}")
            
            # Check if product is preserved
            line_item = bill['line_items'][0]
            print(f"Product ID in response: {line_item.get('product', 'None')}")
            print(f"Product name: {line_item.get('product_name', 'None')}")
            
            return True
        else:
            print(f"❌ Error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Exception: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_bill_with_product()
    print(f"\nResult: {'✅ SUCCESS' if success else '❌ FAILED'}")
