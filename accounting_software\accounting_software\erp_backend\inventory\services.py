"""
Enterprise Inventory Services for ERP System

This module contains business logic for inventory operations:
- Stock valuation methods (FIFO, LIFO, Weighted Average)
- Cost layer management
- Inventory transaction processing
- Low stock alert generation
- Stock adjustment processing
"""

from django.db import transaction
from django.utils import timezone
from decimal import Decimal
from datetime import datetime, date
from typing import List, Tuple, Dict, Optional
from .models import (
    Inventory, InventoryCostLayer, StockTransaction, StockAdjustment,
    StockAdjustmentItem, LowStockAlert, StockValuation, StockValuationItem,
    Warehouse, WarehouseLocation
)


class InventoryValuationService:
    """
    Service for handling inventory valuation using different costing methods
    """
    
    @staticmethod
    def calculate_fifo_cost(inventory: Inventory, quantity: Decimal) -> Tuple[Decimal, Decimal]:
        """
        Calculate cost using FIFO (First In, First Out) method
        Returns: (total_cost, average_unit_cost)
        """
        if quantity <= 0:
            return Decimal('0.00'), Decimal('0.00')
        
        cost_layers = inventory.cost_layers.filter(
            remaining_quantity__gt=0
        ).order_by('layer_date', 'cost_layer_id')
        
        total_cost = Decimal('0.00')
        remaining_qty = quantity
        
        for layer in cost_layers:
            if remaining_qty <= 0:
                break
            
            qty_from_layer = min(remaining_qty, layer.remaining_quantity)
            layer_cost = qty_from_layer * layer.unit_cost
            total_cost += layer_cost
            remaining_qty -= qty_from_layer
        
        if remaining_qty > 0:
            # Not enough stock in cost layers, use average cost for remainder
            avg_cost = inventory.average_cost or Decimal('0.00')
            total_cost += remaining_qty * avg_cost
        
        average_unit_cost = total_cost / quantity if quantity > 0 else Decimal('0.00')
        return total_cost, average_unit_cost
    
    @staticmethod
    def calculate_lifo_cost(inventory: Inventory, quantity: Decimal) -> Tuple[Decimal, Decimal]:
        """
        Calculate cost using LIFO (Last In, First Out) method
        Returns: (total_cost, average_unit_cost)
        """
        if quantity <= 0:
            return Decimal('0.00'), Decimal('0.00')
        
        cost_layers = inventory.cost_layers.filter(
            remaining_quantity__gt=0
        ).order_by('-layer_date', '-cost_layer_id')
        
        total_cost = Decimal('0.00')
        remaining_qty = quantity
        
        for layer in cost_layers:
            if remaining_qty <= 0:
                break
            
            qty_from_layer = min(remaining_qty, layer.remaining_quantity)
            layer_cost = qty_from_layer * layer.unit_cost
            total_cost += layer_cost
            remaining_qty -= qty_from_layer
        
        if remaining_qty > 0:
            # Not enough stock in cost layers, use average cost for remainder
            avg_cost = inventory.average_cost or Decimal('0.00')
            total_cost += remaining_qty * avg_cost
        
        average_unit_cost = total_cost / quantity if quantity > 0 else Decimal('0.00')
        return total_cost, average_unit_cost
    
    @staticmethod
    def calculate_weighted_average_cost(inventory: Inventory, quantity: Decimal) -> Tuple[Decimal, Decimal]:
        """
        Calculate cost using Weighted Average method
        Returns: (total_cost, average_unit_cost)
        """
        if quantity <= 0:
            return Decimal('0.00'), Decimal('0.00')
        
        unit_cost = inventory.average_cost or Decimal('0.00')
        total_cost = quantity * unit_cost
        
        return total_cost, unit_cost
    
    @staticmethod
    def calculate_standard_cost(inventory: Inventory, quantity: Decimal) -> Tuple[Decimal, Decimal]:
        """
        Calculate cost using Standard Cost method
        Returns: (total_cost, average_unit_cost)
        """
        if quantity <= 0:
            return Decimal('0.00'), Decimal('0.00')
        
        unit_cost = inventory.standard_cost or Decimal('0.00')
        total_cost = quantity * unit_cost
        
        return total_cost, unit_cost
    
    @classmethod
    def get_inventory_value(cls, inventory: Inventory, method: str = None) -> Dict[str, Decimal]:
        """
        Get inventory value using specified method or all methods
        """
        method = method or inventory.valuation_method
        quantity = inventory.quantity_on_hand
        
        if quantity <= 0:
            return {
                'fifo_value': Decimal('0.00'),
                'lifo_value': Decimal('0.00'),
                'average_value': Decimal('0.00'),
                'standard_value': Decimal('0.00'),
                'current_value': Decimal('0.00')
            }
        
        fifo_total, fifo_unit = cls.calculate_fifo_cost(inventory, quantity)
        lifo_total, lifo_unit = cls.calculate_lifo_cost(inventory, quantity)
        avg_total, avg_unit = cls.calculate_weighted_average_cost(inventory, quantity)
        std_total, std_unit = cls.calculate_standard_cost(inventory, quantity)
        
        # Determine current value based on valuation method
        current_value = {
            'FIFO': fifo_total,
            'LIFO': lifo_total,
            'AVERAGE': avg_total,
            'STANDARD': std_total
        }.get(method, avg_total)
        
        return {
            'fifo_value': fifo_total,
            'lifo_value': lifo_total,
            'average_value': avg_total,
            'standard_value': std_total,
            'current_value': current_value,
            'fifo_unit_cost': fifo_unit,
            'lifo_unit_cost': lifo_unit,
            'average_unit_cost': avg_unit,
            'standard_unit_cost': std_unit
        }


class CostLayerService:
    """
    Service for managing inventory cost layers
    """
    
    @staticmethod
    def create_cost_layer(inventory: Inventory, quantity: Decimal, unit_cost: Decimal,
                         reference_type: str, reference_id: int, batch_number: str = '') -> InventoryCostLayer:
        """
        Create a new cost layer for inventory receipt
        """
        return InventoryCostLayer.objects.create(
            inventory=inventory,
            layer_date=timezone.now(),
            reference_type=reference_type,
            reference_id=reference_id,
            original_quantity=quantity,
            remaining_quantity=quantity,
            unit_cost=unit_cost,
            batch_number=batch_number
        )
    
    @staticmethod
    def consume_cost_layers(inventory: Inventory, quantity: Decimal, method: str = 'FIFO') -> List[Dict]:
        """
        Consume cost layers based on valuation method
        Returns list of consumed layers with quantities and costs
        """
        if quantity <= 0:
            return []
        
        if method == 'FIFO':
            cost_layers = inventory.cost_layers.filter(
                remaining_quantity__gt=0
            ).order_by('layer_date', 'cost_layer_id')
        elif method == 'LIFO':
            cost_layers = inventory.cost_layers.filter(
                remaining_quantity__gt=0
            ).order_by('-layer_date', '-cost_layer_id')
        else:
            # For average costing, we don't consume specific layers
            return []
        
        consumed_layers = []
        remaining_qty = quantity
        
        for layer in cost_layers:
            if remaining_qty <= 0:
                break
            
            qty_consumed, cost = layer.consume_quantity(remaining_qty)
            if qty_consumed > 0:
                consumed_layers.append({
                    'layer_id': layer.cost_layer_id,
                    'quantity_consumed': qty_consumed,
                    'unit_cost': layer.unit_cost,
                    'total_cost': cost,
                    'batch_number': layer.batch_number
                })
                remaining_qty -= qty_consumed
        
        return consumed_layers
    
    @staticmethod
    def update_weighted_average_cost(inventory: Inventory, new_quantity: Decimal, new_unit_cost: Decimal):
        """
        Update weighted average cost when new stock is received
        """
        if new_quantity <= 0:
            return
        
        old_quantity = inventory.quantity_on_hand
        old_cost = inventory.average_cost or Decimal('0.00')
        
        if old_quantity > 0:
            total_cost = (old_quantity * old_cost) + (new_quantity * new_unit_cost)
            total_quantity = old_quantity + new_quantity
            inventory.average_cost = total_cost / total_quantity
        else:
            inventory.average_cost = new_unit_cost
        
        inventory.last_cost = new_unit_cost
        inventory.save()


class StockTransactionService:
    """
    Service for processing stock transactions
    """
    
    @staticmethod
    @transaction.atomic
    def process_stock_receipt(product, warehouse, quantity: Decimal, unit_cost: Decimal,
                            reference_type: str, reference_id: int, batch_number: str = '',
                            location: WarehouseLocation = None, user=None) -> StockTransaction:
        """
        Process stock receipt transaction
        """
        # Get or create inventory record
        inventory, created = Inventory.objects.get_or_create(
            product=product,
            warehouse=warehouse,
            location=location,
            defaults={
                'quantity_on_hand': Decimal('0.0000'),
                'average_cost': unit_cost,
                'last_cost': unit_cost
            }
        )
        
        # Create stock transaction
        stock_txn = StockTransaction.objects.create(
            product=product,
            warehouse=warehouse,
            transaction_type='RECEIPT',
            reference_type=reference_type,
            reference_id=reference_id,
            quantity=quantity,
            unit_cost=unit_cost,
            txn_date=timezone.now(),
            description=f"Stock receipt - {reference_type}",
            batch_number=batch_number,
            created_by=user
        )
        
        # Update inventory quantities
        inventory.quantity_on_hand += quantity
        inventory.last_transaction_date = timezone.now()
        
        # Create cost layer for FIFO/LIFO tracking
        CostLayerService.create_cost_layer(
            inventory, quantity, unit_cost, reference_type, reference_id, batch_number
        )
        
        # Update weighted average cost
        CostLayerService.update_weighted_average_cost(inventory, quantity, unit_cost)
        
        # Check for low stock alerts resolution
        LowStockAlertService.check_and_resolve_alerts(inventory)
        
        return stock_txn
    
    @staticmethod
    @transaction.atomic
    def process_stock_issue(product, warehouse, quantity: Decimal,
                          reference_type: str, reference_id: int, batch_number: str = '',
                          location: WarehouseLocation = None, user=None) -> StockTransaction:
        """
        Process stock issue transaction
        """
        # Get inventory record
        try:
            inventory = Inventory.objects.get(
                product=product,
                warehouse=warehouse,
                location=location
            )
        except Inventory.DoesNotExist:
            raise ValueError(f"No inventory found for {product.name} in {warehouse.name}")
        
        # Check available quantity
        if inventory.available_quantity < quantity:
            raise ValueError(f"Insufficient stock. Available: {inventory.available_quantity}, Required: {quantity}")
        
        # Calculate cost based on valuation method
        valuation_service = InventoryValuationService()
        total_cost, unit_cost = getattr(valuation_service, f'calculate_{inventory.valuation_method.lower()}_cost')(
            inventory, quantity
        )
        
        # Create stock transaction
        stock_txn = StockTransaction.objects.create(
            product=product,
            warehouse=warehouse,
            transaction_type='ISSUE',
            reference_type=reference_type,
            reference_id=reference_id,
            quantity=-quantity,  # Negative for outbound
            unit_cost=unit_cost,
            txn_date=timezone.now(),
            description=f"Stock issue - {reference_type}",
            batch_number=batch_number,
            created_by=user
        )
        
        # Update inventory quantities
        inventory.quantity_on_hand -= quantity
        inventory.last_transaction_date = timezone.now()
        inventory.save()
        
        # Consume cost layers for FIFO/LIFO
        if inventory.valuation_method in ['FIFO', 'LIFO']:
            CostLayerService.consume_cost_layers(inventory, quantity, inventory.valuation_method)
        
        # Check for low stock alerts
        LowStockAlertService.check_and_create_alerts(inventory)
        
        return stock_txn


class LowStockAlertService:
    """
    Service for managing low stock alerts
    """
    
    @staticmethod
    def check_and_create_alerts(inventory: Inventory):
        """
        Check inventory levels and create alerts if needed
        """
        # Check if already has active alert
        existing_alert = LowStockAlert.objects.filter(
            inventory=inventory,
            status='ACTIVE'
        ).first()
        
        if existing_alert:
            return existing_alert
        
        # Create alert if below reorder point
        if inventory.is_below_reorder_point:
            alert_type = 'OUT_OF_STOCK' if inventory.is_out_of_stock else 'LOW_STOCK'
            priority = 'CRITICAL' if inventory.is_out_of_stock else 'HIGH'
            
            alert = LowStockAlert.objects.create(
                inventory=inventory,
                alert_type=alert_type,
                threshold_quantity=inventory.reorder_point,
                current_quantity=inventory.quantity_on_hand,
                priority=priority,
                message=f"Stock level for {inventory.product.name} is {'out of stock' if inventory.is_out_of_stock else 'below reorder point'}",
                recommended_action=f"Reorder {inventory.reorder_quantity} units immediately"
            )
            return alert
        
        return None
    
    @staticmethod
    def check_and_resolve_alerts(inventory: Inventory):
        """
        Check if alerts can be resolved due to stock increase
        """
        active_alerts = LowStockAlert.objects.filter(
            inventory=inventory,
            status='ACTIVE'
        )
        
        for alert in active_alerts:
            if inventory.quantity_on_hand > inventory.reorder_point:
                alert.status = 'RESOLVED'
                alert.resolved_at = timezone.now()
                alert.save()


class StockAdjustmentService:
    """
    Service for processing stock adjustments
    """

    @staticmethod
    @transaction.atomic
    def create_adjustment(adjustment_type: str, reason_code: str, description: str, user) -> StockAdjustment:
        """
        Create a new stock adjustment
        """
        adjustment = StockAdjustment.objects.create(
            adjustment_type=adjustment_type,
            reason_code=reason_code,
            description=description,
            created_by=user
        )
        return adjustment

    @staticmethod
    def add_adjustment_item(adjustment: StockAdjustment, product, warehouse,
                          system_quantity: Decimal, physical_quantity: Decimal,
                          unit_cost: Decimal, location: WarehouseLocation = None,
                          batch_number: str = '', notes: str = '') -> StockAdjustmentItem:
        """
        Add an item to stock adjustment
        """
        item = StockAdjustmentItem.objects.create(
            adjustment=adjustment,
            product=product,
            warehouse=warehouse,
            location=location,
            system_quantity=system_quantity,
            physical_quantity=physical_quantity,
            unit_cost=unit_cost,
            batch_number=batch_number,
            notes=notes
        )

        # Recalculate adjustment totals
        adjustment.calculate_totals()
        return item

    @staticmethod
    @transaction.atomic
    def approve_adjustment(adjustment: StockAdjustment, user):
        """
        Approve a stock adjustment
        """
        adjustment.approve(user)

    @staticmethod
    @transaction.atomic
    def post_adjustment(adjustment: StockAdjustment, user):
        """
        Post adjustment to inventory
        """
        adjustment.post_to_inventory(user)


class StockValuationReportService:
    """
    Service for generating stock valuation reports
    """

    @staticmethod
    @transaction.atomic
    def create_valuation_report(valuation_date: date, valuation_type: str,
                              warehouse: Warehouse = None, product_category: str = '',
                              user=None) -> StockValuation:
        """
        Create a comprehensive stock valuation report
        """
        valuation = StockValuation.objects.create(
            valuation_date=valuation_date,
            valuation_type=valuation_type,
            warehouse=warehouse,
            product_category=product_category,
            created_by=user
        )

        # Get inventory items to value
        inventory_queryset = Inventory.objects.filter(quantity_on_hand__gt=0)

        if warehouse:
            inventory_queryset = inventory_queryset.filter(warehouse=warehouse)

        if product_category:
            inventory_queryset = inventory_queryset.filter(product__category__icontains=product_category)

        # Calculate valuations for each inventory item
        total_items = 0
        total_quantity = Decimal('0.0000')
        fifo_total = Decimal('0.00')
        lifo_total = Decimal('0.00')
        average_total = Decimal('0.00')
        standard_total = Decimal('0.00')

        for inventory in inventory_queryset:
            values = InventoryValuationService.get_inventory_value(inventory)

            StockValuationItem.objects.create(
                valuation=valuation,
                inventory=inventory,
                quantity_on_hand=inventory.quantity_on_hand,
                fifo_unit_cost=values['fifo_unit_cost'],
                fifo_total_value=values['fifo_value'],
                lifo_unit_cost=values['lifo_unit_cost'],
                lifo_total_value=values['lifo_value'],
                average_unit_cost=values['average_unit_cost'],
                average_total_value=values['average_value'],
                standard_unit_cost=values['standard_unit_cost'],
                standard_total_value=values['standard_value']
            )

            total_items += 1
            total_quantity += inventory.quantity_on_hand
            fifo_total += values['fifo_value']
            lifo_total += values['lifo_value']
            average_total += values['average_value']
            standard_total += values['standard_value']

        # Update valuation totals
        valuation.total_items = total_items
        valuation.total_quantity = total_quantity
        valuation.fifo_total_value = fifo_total
        valuation.lifo_total_value = lifo_total
        valuation.average_total_value = average_total
        valuation.standard_total_value = standard_total
        valuation.status = 'COMPLETED'
        valuation.completed_at = timezone.now()
        valuation.save()

        return valuation

    @staticmethod
    def get_inventory_aging_report(warehouse: Warehouse = None, days_threshold: int = 90) -> List[Dict]:
        """
        Generate inventory aging report
        """
        from django.db.models import F, Case, When, IntegerField
        from django.utils import timezone

        cutoff_date = timezone.now().date() - timezone.timedelta(days=days_threshold)

        inventory_queryset = Inventory.objects.filter(quantity_on_hand__gt=0)
        if warehouse:
            inventory_queryset = inventory_queryset.filter(warehouse=warehouse)

        aging_data = []
        for inventory in inventory_queryset:
            # Get oldest cost layer
            oldest_layer = inventory.cost_layers.filter(
                remaining_quantity__gt=0
            ).order_by('layer_date').first()

            if oldest_layer:
                days_old = (timezone.now().date() - oldest_layer.layer_date.date()).days
                aging_category = 'Current' if days_old <= 30 else \
                               '31-60 Days' if days_old <= 60 else \
                               '61-90 Days' if days_old <= 90 else \
                               'Over 90 Days'
            else:
                days_old = 0
                aging_category = 'Current'

            values = InventoryValuationService.get_inventory_value(inventory)

            aging_data.append({
                'product': inventory.product,
                'warehouse': inventory.warehouse,
                'quantity_on_hand': inventory.quantity_on_hand,
                'days_old': days_old,
                'aging_category': aging_category,
                'current_value': values['current_value'],
                'last_transaction_date': inventory.last_transaction_date
            })

        return aging_data

    @staticmethod
    def get_abc_analysis_report(warehouse: Warehouse = None) -> Dict[str, List]:
        """
        Generate ABC analysis report based on inventory value
        """
        inventory_queryset = Inventory.objects.filter(quantity_on_hand__gt=0)
        if warehouse:
            inventory_queryset = inventory_queryset.filter(warehouse=warehouse)

        # Calculate values and sort by total value
        inventory_values = []
        total_value = Decimal('0.00')

        for inventory in inventory_queryset:
            values = InventoryValuationService.get_inventory_value(inventory)
            item_value = values['current_value']
            inventory_values.append({
                'inventory': inventory,
                'value': item_value
            })
            total_value += item_value

        # Sort by value descending
        inventory_values.sort(key=lambda x: x['value'], reverse=True)

        # Calculate cumulative percentages and assign ABC categories
        cumulative_value = Decimal('0.00')
        a_items = []
        b_items = []
        c_items = []

        for item in inventory_values:
            cumulative_value += item['value']
            cumulative_percentage = (cumulative_value / total_value * 100) if total_value > 0 else 0

            if cumulative_percentage <= 80:
                category = 'A'
                a_items.append(item)
            elif cumulative_percentage <= 95:
                category = 'B'
                b_items.append(item)
            else:
                category = 'C'
                c_items.append(item)

            # Update inventory ABC classification
            item['inventory'].abc_classification = category
            item['inventory'].save()

        return {
            'A': a_items,
            'B': b_items,
            'C': c_items,
            'total_value': total_value
        }
