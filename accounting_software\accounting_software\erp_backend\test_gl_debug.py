#!/usr/bin/env python
import os
import sys
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'erp_backend.settings')
django.setup()

from purchase.models import VendorBill
from gl.models import JournalEntry

def test_gl_creation():
    """Test GL entry creation for existing vendor bills"""
    print("🔧 TESTING GL ENTRY CREATION")
    print("=" * 40)
    
    try:
        # Get the most recent vendor bill
        recent_bill = VendorBill.objects.filter(status='approved').order_by('-created_at').first()
        
        if not recent_bill:
            print("❌ No approved vendor bills found")
            return
        
        print(f"Testing with bill: {recent_bill.bill_number}")
        print(f"Status: {recent_bill.status}")
        print(f"Total Amount: ${recent_bill.total_amount}")
        print(f"Line Items: {recent_bill.line_items.count()}")
        
        # Check if GL entries already exist
        existing_entries = JournalEntry.objects.filter(
            reference_number=f"VB-{recent_bill.bill_number}"
        )
        print(f"Existing GL entries: {existing_entries.count()}")
        
        # Try to create GL entries manually
        print("\nTrying to create GL entries...")
        try:
            journal_entry = recent_bill.create_gl_entries()
            if journal_entry:
                print(f"✅ GL entry created: {journal_entry.reference_number}")
                print(f"Entry ID: {journal_entry.id}")
                print(f"Total Amount: ${journal_entry.total_amount}")
                
                # Show journal lines
                lines = journal_entry.journal_lines.all()
                print(f"Journal Lines: {lines.count()}")
                for line in lines:
                    if line.debit_amount > 0:
                        print(f"  DEBIT  ${line.debit_amount} - {line.account.account_name}")
                    if line.credit_amount > 0:
                        print(f"  CREDIT ${line.credit_amount} - {line.account.account_name}")
            else:
                print("❌ GL entry creation returned None")
        except Exception as e:
            print(f"❌ Error creating GL entries: {e}")
            import traceback
            traceback.print_exc()
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_gl_creation()
