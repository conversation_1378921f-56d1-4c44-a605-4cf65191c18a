#!/usr/bin/env python3
"""
Test vendor bill creation with authentication
"""
import requests
import json
from datetime import datetime, timedelta

# Configuration
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api"
TOKEN = "e15bc01f831c5111f413f534ef82288744cb7d41"

# Headers with authentication
HEADERS = {
    'Content-Type': 'application/json',
    'Authorization': f'Token {TOKEN}'
}

# Test data matching frontend structure
TEST_VENDOR_BILL = {
    "vendor": 50,  # Using existing vendor ID
    "bill_date": datetime.now().strftime('%Y-%m-%d'),
    "due_date": (datetime.now() + timedelta(days=30)).strftime('%Y-%m-%d'),
    "status": "draft",
    "payment_terms": "Net 30",
    "reference_number": "TEST-BILL-001",
    "notes": "Test vendor bill for debugging",
    "line_items": [
        {
            "item_description": "Test Product 1",
            "quantity": 10,
            "unit_price": 100.00,
            "tax_rate": 10.0,
            "account_code": "5010-COGS",
            "line_order": 1
        },
        {
            "item_description": "Test Service 1", 
            "quantity": 5,
            "unit_price": 200.00,
            "tax_rate": 10.0,
            "account_code": "5020-Services",
            "line_order": 2
        }
    ]
}

def test_create_vendor_bill():
    """Test creating a vendor bill"""
    print("Testing vendor bill creation...")
    print("Data being sent:")
    print(json.dumps(TEST_VENDOR_BILL, indent=2))
    
    try:
        url = f"{API_BASE}/purchase/vendor-bills/"
        response = requests.post(url, json=TEST_VENDOR_BILL, headers=HEADERS)
        
        print(f"\nStatus Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        if response.status_code == 201:
            print("✅ SUCCESS - Vendor bill created!")
            response_data = response.json()
            print("Response data:")
            print(json.dumps(response_data, indent=2))
            return response_data
        else:
            print("❌ FAILED")
            print("Response text:", response.text)
            try:
                error_data = response.json()
                print("Error details:")
                print(json.dumps(error_data, indent=2))
            except:
                pass
            return None
            
    except Exception as e:
        print(f"❌ EXCEPTION: {e}")
        return None

def test_list_vendors():
    """Test listing vendors to see if vendor ID 1 exists"""
    print("\nTesting vendor list...")
    try:
        url = f"{API_BASE}/contacts/vendors/"
        response = requests.get(url, headers=HEADERS)
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            vendors = response.json()
            print(f"Found {len(vendors.get('results', vendors))} vendors")
            if isinstance(vendors, dict) and 'results' in vendors:
                for vendor in vendors['results'][:3]:  # Show first 3
                    print(f"  - ID: {vendor.get('id')}, Name: {vendor.get('display_name', vendor.get('name'))}")
            elif isinstance(vendors, list):
                for vendor in vendors[:3]:  # Show first 3
                    print(f"  - ID: {vendor.get('id')}, Name: {vendor.get('display_name', vendor.get('name'))}")
        else:
            print("❌ Failed to get vendors")
            print("Response:", response.text[:200])
            
    except Exception as e:
        print(f"❌ Exception getting vendors: {e}")

if __name__ == "__main__":
    print("🚀 VENDOR BILL CREATION TEST WITH AUTH")
    print("=" * 50)
    
    # First check if vendors exist
    test_list_vendors()
    
    # Then test creating vendor bill
    result = test_create_vendor_bill()
    
    print("\n" + "=" * 50)
    if result:
        print("✅ Test completed successfully!")
    else:
        print("❌ Test failed - check error details above")
