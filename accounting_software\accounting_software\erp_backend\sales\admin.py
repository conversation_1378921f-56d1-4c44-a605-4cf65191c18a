from django.contrib import admin
from django.utils.html import format_html
from .models import ProductCategory, Product


@admin.register(ProductCategory)
class ProductCategoryAdmin(admin.ModelAdmin):
    list_display = ['name', 'parent_category', 'division_type', 'default_unit_of_measure', 'is_active', 'created_at']
    list_filter = ['division_type', 'default_unit_of_measure', 'is_active', 'track_inventory']
    search_fields = ['name', 'description']
    readonly_fields = ['category_id', 'created_at', 'updated_at']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('category_id', 'name', 'code', 'description', 'parent_category')
        }),
        ('Category Properties', {
            'fields': ('division_type', 'default_unit_of_measure')
        }),
        ('Settings', {
            'fields': ('track_inventory', 'requires_expiry_date', 'requires_batch_tracking', 'default_markup_percentage', 'default_tax_category', 'is_active')
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at', 'created_by'),
            'classes': ('collapse',)
        }),
    )


@admin.register(Product)
class ProductAdmin(admin.ModelAdmin):
    list_display = ['name', 'sku', 'product_type', 'category', 'unit_price', 'cost_price', 'margin_display', 'status', 'track_inventory']
    list_filter = ['product_type', 'category', 'status', 'track_inventory', 'taxable']
    search_fields = ['name', 'sku', 'description']
    readonly_fields = ['product_id', 'margin_amount', 'margin_percentage', 'markup_percentage', 'price_last_updated_at', 'price_last_updated_by', 'cost_last_updated_at', 'cost_last_updated_by', 'created_at', 'updated_at']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('product_id', 'name', 'sku', 'product_type', 'category', 'description')
        }),
        ('Pricing', {
            'fields': ('unit_price', 'cost_price', 'margin_amount', 'margin_percentage', 'markup_percentage', 'price_last_updated_at', 'price_last_updated_by', 'cost_last_updated_at', 'cost_last_updated_by')
        }),
        ('Inventory', {
            'fields': ('track_inventory', 'unit_of_measure', 'reorder_point', 'quantity_on_hand', 'quantity_on_purchase_order', 'quantity_on_sales_order'),
            'classes': ('collapse',)
        }),
        ('GL Accounts', {
            'fields': ('income_account_gl', 'expense_account_gl', 'inventory_asset_account_gl'),
            'classes': ('collapse',)
        }),
        ('Tax & Purchasing', {
            'fields': ('taxable', 'sales_tax_category', 'preferred_vendor'),
            'classes': ('collapse',)
        }),
        ('Settings', {
            'fields': ('status',)
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at', 'created_by'),
            'classes': ('collapse',)
        }),
    )
    
    def margin_display(self, obj):
        """Display margin with color coding"""
        margin = obj.margin_percentage
        if margin > 30:
            color = 'green'
        elif margin > 15:
            color = 'orange'
        else:
            color = 'red'
        return format_html(
            '<span style="color: {};">{:.2f}%</span>',
            color,
            margin
        )
    margin_display.short_description = 'Margin %'
    margin_display.admin_order_field = 'margin_percentage'
    
    def save_model(self, request, obj, form, change):
        """Track who updated prices"""
        if change:
            try:
                original = Product.objects.get(pk=obj.pk)
                if original.unit_price != obj.unit_price:
                    obj.price_last_updated_by = request.user
                if original.cost_price != obj.cost_price:
                    obj.cost_last_updated_by = request.user
            except Product.DoesNotExist:
                pass
        
        if not change:  # New product
            obj.created_by = request.user
            
        super().save_model(request, obj, form, change)
