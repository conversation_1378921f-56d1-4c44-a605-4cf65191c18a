from django.contrib import admin
from django.forms import ModelForm, Select
from django.utils.html import format_html
from django.utils import timezone
from .models import (
    ProductCategory, Product, Payment,
    Estimate, EstimateLineItem, PaymentTerm, SalesOrder, SalesOrderLineItem,
    DeliveryNote, DeliveryNoteItem, CustomerInvoice, CustomerInvoiceItem
)
from sales_tax.models import SalesTax

# Customer invoice admin configurations are now in this file


# Customer admin is now in contacts app - references moved to contacts.Contact

# Customer admin moved to contacts app - do not register here to avoid conflicts
# The CustomerAdmin class below is kept for reference only and is not registered
class CustomerAdminReference(admin.ModelAdmin):
    """
    DEPRECATED: Customer admin has been moved to contacts app.
    This is kept for reference but not registered to avoid conflicts.
    Use /admin/contacts/customer/ instead of /admin/sales/customer/
    
    Customer model is now accessed via contacts.Contact with contact_type='customer'
    """
    # This class is kept for documentation purposes only
    pass


@admin.register(ProductCategory)
class ProductCategoryAdmin(admin.ModelAdmin):
    list_display = ['name', 'parent_category', 'is_active', 'created_at']
    list_filter = ['is_active', 'parent_category', 'created_at']
    search_fields = ['name', 'description']


class ProductAdminForm(ModelForm):
    """Custom form for Product admin with enhanced fields"""
    
    class Meta:
        model = Product
        fields = '__all__'
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Add sales tax choices for tax rate field
        if 'sales_tax_category' in self.fields:
            output_taxes = SalesTax.objects.filter(tax_type='output').order_by('rate')
            choices = [('', 'No Tax')] + [(f"{tax.rate}%", f"{tax.description} ({tax.rate}%)") for tax in output_taxes]
            self.fields['sales_tax_category'].widget = Select(choices=choices)
            self.fields['sales_tax_category'].help_text = "Select output tax rate for sales"


@admin.register(Product)
class ProductAdmin(admin.ModelAdmin):
    form = ProductAdminForm
    list_display = [
        'name', 'sku', 'product_type', 'category', 
        'get_unit_price_display', 'get_cost_price_display', 'get_margin_display',
        'get_current_stock', 'get_income_account_display', 'status', 'created_at'
    ]
    list_filter = ['product_type', 'category', 'status', 'taxable', 'track_inventory', 'created_at']
    search_fields = [
        'name', 'sku', 'description', 
        'income_account_gl__account_name', 'expense_account_gl__account_name', 
        'inventory_asset_account_gl__account_name'
    ]
    readonly_fields = [
        'product_id', 'get_margin_display', 'get_markup_display', 
        'get_current_stock', 'price_last_updated_at', 'created_at', 'updated_at'
    ]
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('product_id', 'name', 'sku', 'product_type', 'category', 'description')
        }),
        ('💰 Sales Editable Fields', {
            'fields': ('unit_price', 'sales_tax_category'),
            'description': '🔹 Sales Department Access: You can only edit selling price and tax rates. Other product details are managed by the Purchase Department.',
        }),
        ('📊 Purchase Department Fields', {
            'fields': ('cost_price', 'minimum_selling_price', 'preferred_vendor'),
            'description': 'Managed by Purchase Department - Read Only for Sales',
        }),
        ('📈 Calculated Fields', {
            'fields': ('get_margin_display', 'get_markup_display'),
            'description': 'Auto-calculated based on cost and selling price',
        }),
        ('🏦 GL Account Integration', {
            'fields': ('income_account_gl', 'expense_account_gl', 'inventory_asset_account_gl'),
            'description': 'Link to Chart of Accounts for proper GL integration'
        }),
        ('📦 Inventory Information', {
            'fields': (
                'track_inventory', 'get_current_stock', 'reorder_point',
                'quantity_on_hand', 'quantity_on_purchase_order', 'quantity_on_sales_order'
            ),
            'description': 'Inventory tracking and stock levels'
        }),
        ('⚙️ Settings', {
            'fields': ('status', 'taxable')
        }),
        ('📅 Price History', {
            'fields': ('price_effective_date', 'price_last_updated_by', 'price_last_updated_at'),
        }),
        ('📝 Metadata', {
            'fields': ('created_at', 'updated_at', 'created_by'),
        }),
    )
    
    def get_unit_price_display(self, obj):
        """Display unit price with USD currency symbol"""
        if obj.unit_price is not None:
            return format_html('<span style="color: green; font-weight: bold;">$ {}</span>', f'{float(obj.unit_price):,.2f}')
        return format_html('<span style="color: orange;">Not Set</span>')
    get_unit_price_display.short_description = "💰 Selling Price"
    get_unit_price_display.admin_order_field = 'unit_price'
    
    def get_cost_price_display(self, obj):
        """Display cost price with USD currency symbol"""
        if obj.cost_price is not None:
            return format_html('<span style="color: blue;">$ {}</span>', f'{float(obj.cost_price):,.2f}')
        return format_html('<span style="color: red;">Not Set</span>')
    get_cost_price_display.short_description = "📊 Cost Price"
    get_cost_price_display.admin_order_field = 'cost_price'
    
    def get_margin_display(self, obj):
        """Display margin amount and percentage"""
        if obj.cost_price and obj.cost_price > 0 and obj.unit_price is not None:
            margin_amount = float(obj.unit_price) - float(obj.cost_price)
            margin_percent = ((float(obj.unit_price) - float(obj.cost_price)) / float(obj.cost_price)) * 100
            color = 'green' if margin_amount > 0 else 'red'
            return format_html(
                '<span style="color: {}; font-weight: bold;">$ {} ({}%)</span>',
                color, f'{margin_amount:,.2f}', f'{margin_percent:.1f}'
            )
        return format_html('<span style="color: orange;">Cost price needed</span>')
    get_margin_display.short_description = "📈 Margin (Amount & %)"
    
    def get_markup_display(self, obj):
        """Display markup percentage"""
        if obj.cost_price and obj.cost_price > 0:
            markup = ((obj.unit_price - obj.cost_price) / obj.cost_price) * 100
            return f"{markup:.1f}%"
        return "Cost price needed"
    get_markup_display.short_description = "📊 Markup %"
    
    def get_current_stock(self, obj):
        """Display current stock with status indicator"""
        if obj.track_inventory:
            stock = obj.quantity_on_hand
            if stock <= 0:
                color = 'red'
                status = '⚠️ Out of Stock'
            elif stock <= obj.reorder_point:
                color = 'orange'
                status = '⚠️ Low Stock'
            else:
                color = 'green'
                status = '✅ In Stock'
            return format_html(
                '<span style="color: {}; font-weight: bold;">{} units</span><br/><small>{}</small>',
                color, stock, status
            )
        return "Not tracked"
    get_current_stock.short_description = "📦 Current Stock"
    
    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        """Customize dropdown options for GL accounts"""
        if db_field.name == "income_account_gl":
            # Only show Revenue accounts
            kwargs["queryset"] = db_field.remote_field.model.objects.filter(
                account_type__type='REVENUE', 
                is_active=True,
                is_header_account=False
            ).order_by('account_number')
        elif db_field.name == "expense_account_gl":
            # Only show Expense accounts  
            kwargs["queryset"] = db_field.remote_field.model.objects.filter(
                account_type__type='EXPENSE',
                is_active=True,
                is_header_account=False
            ).order_by('account_number')
        elif db_field.name == "inventory_asset_account_gl":
            # Only show Asset accounts suitable for inventory
            kwargs["queryset"] = db_field.remote_field.model.objects.filter(
                account_type__type='ASSET',
                is_active=True,
                is_header_account=False
            ).order_by('account_number')
        return super().formfield_for_foreignkey(db_field, request, **kwargs)
    
    def get_income_account_display(self, obj):
        """Display income account with number and name"""
        if obj.income_account_gl:
            return f"{obj.income_account_gl.account_number} - {obj.income_account_gl.account_name}"
        return "Not Assigned"
    get_income_account_display.short_description = "Sales Account"
    
    def get_expense_account_display(self, obj):
        """Display expense account with number and name"""
        if obj.expense_account_gl:
            return f"{obj.expense_account_gl.account_number} - {obj.expense_account_gl.account_name}"
        return "Not Assigned"
    get_expense_account_display.short_description = "COGS Account"
    
    def get_inventory_account_display(self, obj):
        """Display inventory account with number and name"""
        if obj.inventory_asset_account_gl:
            return f"{obj.inventory_asset_account_gl.account_number} - {obj.inventory_asset_account_gl.account_name}"
        return "Not Assigned"
    get_inventory_account_display.short_description = "Inventory Account"
    
    def save_model(self, request, obj, form, change):
        """Override save to track price changes"""
        if change:
            # Check if unit_price changed
            try:
                original = Product.objects.get(pk=obj.pk)
                if original.unit_price != obj.unit_price:
                    obj.price_last_updated_by = request.user
                    obj.price_last_updated_at = timezone.now()
                    if not obj.price_effective_date:
                        obj.price_effective_date = timezone.now().date()
            except Product.DoesNotExist:
                pass
        
        super().save_model(request, obj, form, change)


# Customer Invoice Admin Classes

class CustomerInvoiceItemInline(admin.TabularInline):
    model = CustomerInvoiceItem
    extra = 1
    fields = ['product', 'item_description', 'quantity', 'unit_price', 'discount_percent', 'line_total', 'tax_rate', 'tax_amount', 'account_code']
    readonly_fields = ['line_total', 'tax_amount']


@admin.register(CustomerInvoice)
class CustomerInvoiceAdmin(admin.ModelAdmin):
    list_display = ['invoice_number', 'customer', 'invoice_date', 'due_date', 'total_amount', 'amount_paid', 'balance_due', 'status']
    list_filter = ['status', 'invoice_date', 'due_date', 'created_at']
    search_fields = ['invoice_number', 'customer__display_name', 'reference_number']
    readonly_fields = ['invoice_number', 'subtotal', 'tax_amount', 'total_amount', 'balance_due', 'created_at', 'updated_at']
    inlines = [CustomerInvoiceItemInline]

    fieldsets = (
        ('Basic Information', {
            'fields': ('invoice_number', 'customer', 'invoice_date', 'due_date', 'status')
        }),
        ('Document Links', {
            'fields': ('sales_order', 'delivery_note'),
            'classes': ('collapse',)
        }),
        ('Financial Information', {
            'fields': ('subtotal', 'discount_amount', 'shipping_amount', 'tax_amount', 'total_amount', 'amount_paid', 'balance_due')
        }),
        ('Additional Information', {
            'fields': ('payment_terms', 'reference_number', 'notes', 'terms_and_conditions'),
            'classes': ('collapse',)
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at', 'created_by'),
            'classes': ('collapse',)
        }),
    )


@admin.register(CustomerInvoiceItem)
class CustomerInvoiceItemAdmin(admin.ModelAdmin):
    list_display = ['invoice', 'product', 'item_description', 'quantity', 'unit_price', 'line_total', 'tax_amount']
    list_filter = ['invoice__status', 'product__product_type']
    search_fields = ['invoice__invoice_number', 'product__name', 'item_description']
    readonly_fields = ['line_total', 'tax_amount']


@admin.register(Payment)
class PaymentAdmin(admin.ModelAdmin):
    list_display = ['payment_id', 'customer', 'invoice', 'payment_date', 'amount', 'payment_method']
    list_filter = ['payment_method', 'payment_date', 'created_at']
    search_fields = ['customer__display_name', 'invoice__invoice_number', 'reference_number']
    readonly_fields = ['payment_id', 'created_at', 'updated_at']
    date_hierarchy = 'payment_date'
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('payment_id', 'customer', 'invoice', 'payment_date', 'amount')
        }),
        ('Payment Details', {
            'fields': ('payment_method', 'reference_number', 'notes')
        }),
        ('Deposit Information', {
            'fields': ('deposit_to_account',)
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at', 'created_by')
        }),
    )


class EstimateLineItemInline(admin.TabularInline):
    model = EstimateLineItem
    extra = 1
    fields = ['product', 'description', 'quantity', 'unit_price', 'discount_percent', 'line_total', 'taxable', 'tax_rate', 'tax_amount']
    readonly_fields = ['line_total', 'tax_amount']


@admin.register(Estimate)
class EstimateAdmin(admin.ModelAdmin):
    list_display = ['estimate_number', 'customer', 'estimate_date', 'expiration_date', 'total_amount', 'status']
    list_filter = ['status', 'estimate_date', 'expiration_date', 'created_at']
    search_fields = ['estimate_number', 'customer__display_name', 'po_number']
    readonly_fields = ['estimate_id', 'estimate_number', 'created_at', 'updated_at']
    inlines = [EstimateLineItemInline]
    date_hierarchy = 'estimate_date'
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('estimate_id', 'estimate_number', 'customer', 'estimate_date', 'expiration_date')
        }),
        ('Financial Information', {
            'fields': ('subtotal', 'discount_percent', 'discount_amount', 'tax_amount', 'total_amount')
        }),
        ('Status & Conversion', {
            'fields': ('status', 'converted_to_invoice')
        }),
        ('Additional Information', {
            'fields': ('po_number', 'memo', 'message_to_customer')
        }),
        ('Email Tracking', {
            'fields': ('email_sent', 'email_sent_date', 'viewed_date')
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at', 'created_by')
        }),
    )


@admin.register(PaymentTerm)
class PaymentTermAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'days', 'is_default', 'is_active', 'created_at']
    list_filter = ['is_default', 'is_active', 'created_at']
    search_fields = ['name', 'code', 'description']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'code', 'days', 'description')
        }),
        ('Settings', {
            'fields': ('is_default', 'is_active')
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at', 'created_by')
        }),
    )


class SalesOrderLineItemInline(admin.TabularInline):
    model = SalesOrderLineItem
    extra = 1
    fields = ['product', 'description', 'quantity', 'unit_price', 'discount_percent', 'line_total', 'taxable', 'tax_rate', 'tax_amount']
    readonly_fields = ['line_total', 'tax_amount', 'quantity_pending']


@admin.register(SalesOrder)
class SalesOrderAdmin(admin.ModelAdmin):
    list_display = ['so_number', 'customer', 'so_date', 'expected_delivery_date', 'total_amount', 'status', 'created_at']
    list_filter = ['status', 'so_date', 'expected_delivery_date', 'created_at']
    search_fields = ['so_number', 'customer__display_name', 'customer_po_number']
    readonly_fields = ['so_id', 'so_number', 'balance_due', 'created_at', 'updated_at']
    inlines = [SalesOrderLineItemInline]
    date_hierarchy = 'so_date'
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('so_id', 'so_number', 'customer', 'so_date', 'expected_delivery_date')
        }),
        ('Customer Reference', {
            'fields': ('customer_po_number', 'customer_reference')
        }),
        ('Financial Information', {
            'fields': ('subtotal', 'discount_percent', 'discount_amount', 'tax_amount', 'total_amount', 'balance_due')
        }),
        ('Delivery Information', {
            'fields': ('delivery_address', 'delivery_instructions', 'priority')
        }),
        ('Status & Terms', {
            'fields': ('status', 'payment_terms')
        }),
        ('Additional Information', {
            'fields': ('notes', 'internal_notes')
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at', 'created_by')
        }),
    )


class DeliveryNoteItemInline(admin.TabularInline):
    model = DeliveryNoteItem
    extra = 1
    fields = ['product', 'sales_order_line_item', 'quantity_delivered', 'unit_price', 'line_total', 'batch_number', 'serial_numbers']
    readonly_fields = ['line_total', 'tax_amount']


@admin.register(DeliveryNote)
class DeliveryNoteAdmin(admin.ModelAdmin):
    list_display = ['dn_number', 'customer', 'sales_order', 'delivery_date', 'status', 'posted', 'created_at']
    list_filter = ['status', 'posted', 'delivery_date', 'warehouse', 'created_at']
    search_fields = ['dn_number', 'customer__display_name', 'sales_order__so_number']
    readonly_fields = ['dn_id', 'dn_number', 'posted', 'posted_date', 'posted_by', 'created_at', 'updated_at']
    inlines = [DeliveryNoteItemInline]
    date_hierarchy = 'delivery_date'
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('dn_id', 'dn_number', 'sales_order', 'customer', 'delivery_date', 'expected_delivery_date')
        }),
        ('Delivery Details', {
            'fields': ('warehouse', 'delivery_address', 'delivery_method', 'tracking_number')
        }),
        ('Financial Information', {
            'fields': ('subtotal', 'discount_amount', 'tax_amount', 'total_amount')
        }),
        ('Status & Posting', {
            'fields': ('status', 'posted', 'posted_date', 'posted_by')
        }),
        ('Additional Information', {
            'fields': ('notes', 'internal_notes')
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at', 'created_by')
        }),
    )
