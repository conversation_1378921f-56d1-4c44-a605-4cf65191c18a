# Generated by Django 4.2.21 on 2025-07-05 14:10

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0006_add_enterprise_inventory_models'),
        ('contacts', '0006_remove_pricingrule_customer_and_more'),
        ('purchase', '0009_vendorbill_vendorbillitem'),
    ]

    operations = [
        migrations.RenameField(
            model_name='vendorbillitem',
            old_name='unit_cost',
            new_name='unit_price',
        ),
        migrations.RemoveField(
            model_name='vendorbill',
            name='tax_total',
        ),
        migrations.RemoveField(
            model_name='vendorbill',
            name='terms',
        ),
        migrations.RemoveField(
            model_name='vendorbillitem',
            name='description',
        ),
        migrations.AddField(
            model_name='vendorbill',
            name='goods_return_note',
            field=models.ForeignKey(blank=True, help_text='Linked goods return note for return-based bills', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='vendor_bills', to='inventory.goodsreturnnote'),
        ),
        migrations.AddField(
            model_name='vendorbill',
            name='grn',
            field=models.ForeignKey(blank=True, help_text='Linked GRN for goods receipt bills', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='vendor_bills', to='inventory.goodsreceiptnote'),
        ),
        migrations.AddField(
            model_name='vendorbill',
            name='payment_terms',
            field=models.CharField(blank=True, help_text="e.g., 'Net 30'", max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='vendorbill',
            name='purchase_order',
            field=models.ForeignKey(blank=True, help_text='Linked PO for service bills or PO-based bills', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='vendor_bills', to='purchase.purchaseorder'),
        ),
        migrations.AddField(
            model_name='vendorbill',
            name='tax_amount',
            field=models.DecimalField(decimal_places=2, default=0.0, help_text='Calculated tax amount', max_digits=15),
        ),
        migrations.AddField(
            model_name='vendorbillitem',
            name='account_code',
            field=models.CharField(blank=True, help_text="GL account (e.g., '5010-COGS') - CREDIT: Expense Account", max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='vendorbillitem',
            name='item_description',
            field=models.TextField(default='Default Item', help_text='Product/service name'),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='vendorbill',
            name='bill_date',
            field=models.DateField(help_text='Date of invoice issuance'),
        ),
        migrations.AlterField(
            model_name='vendorbill',
            name='bill_number',
            field=models.CharField(help_text="Unique identifier (e.g., 'VBILL-2023-001')", max_length=50, unique=True),
        ),
        migrations.AlterField(
            model_name='vendorbill',
            name='due_date',
            field=models.DateField(help_text='Payment due date'),
        ),
        migrations.AlterField(
            model_name='vendorbill',
            name='status',
            field=models.CharField(choices=[('draft', 'Draft'), ('approved', 'Approved'), ('paid', 'Paid'), ('rejected', 'Rejected')], default='draft', max_length=20),
        ),
        migrations.AlterField(
            model_name='vendorbill',
            name='subtotal',
            field=models.DecimalField(decimal_places=2, default=0.0, help_text='Before tax', max_digits=15),
        ),
        migrations.AlterField(
            model_name='vendorbill',
            name='total_amount',
            field=models.DecimalField(decimal_places=2, default=0.0, help_text='Final payable amount', max_digits=15),
        ),
        migrations.AlterField(
            model_name='vendorbill',
            name='vendor',
            field=models.ForeignKey(blank=True, help_text='Link to vendor master', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='vendor_bills', to='contacts.contact'),
        ),
        migrations.AlterField(
            model_name='vendorbillitem',
            name='tax_rate',
            field=models.DecimalField(decimal_places=2, default=0.0, help_text='e.g., 10%', max_digits=5),
        ),
        migrations.AlterField(
            model_name='vendorbillitem',
            name='vendor_bill',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='line_items', to='purchase.vendorbill'),
        ),
    ]
