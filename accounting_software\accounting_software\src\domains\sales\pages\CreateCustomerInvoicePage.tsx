import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Paper,
  Typography,
  Grid,
  Button,
  Alert,
  Snackbar,
  TextField,
  MenuItem,
  Card,
  CardContent,
  IconButton,
  Divider,
  FormControl,
  InputLabel,
  Select,
  CircularProgress,
  Backdrop,
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
  Save as SaveIcon,
  Receipt as ReceiptIcon,
} from '@mui/icons-material';
import dayjs from 'dayjs';

// Import services
import { customerInvoiceService, type CustomerInvoice, type CustomerInvoiceItem } from '../../../services/customer-invoice.service';
import { ContactsService } from '../../contacts/services/contacts.service';
import { type Contact } from '../../contacts/types/contacts.types';
import { formatCurrency } from '../../../shared/utils/formatters';

interface SnackbarState {
  open: boolean;
  message: string;
  severity: 'success' | 'error' | 'warning' | 'info';
}

interface InvoiceFormData {
  customer: number | '';
  invoice_date: string;
  due_date: string;
  reference_number: string;
  notes: string;
  terms_and_conditions: string;
  line_items: CustomerInvoiceItem[];
}

const CreateCustomerInvoicePage: React.FC = () => {
  const navigate = useNavigate();

  // State
  const [loading, setLoading] = useState(false);
  const [customers, setCustomers] = useState<Contact[]>([]);
  const [snackbar, setSnackbar] = useState<SnackbarState>({
    open: false,
    message: '',
    severity: 'success',
  });

  const [formData, setFormData] = useState<InvoiceFormData>({
    customer: '',
    invoice_date: dayjs().format('YYYY-MM-DD'),
    due_date: dayjs().add(30, 'day').format('YYYY-MM-DD'),
    reference_number: '',
    notes: '',
    terms_and_conditions: '',
    line_items: [
      {
        item_description: '',
        quantity: 1,
        unit_price: 0,
        line_total: 0,
        tax_rate: 0,
        tax_amount: 0,
        account_code: '4010-SALES',
      }
    ],
  });

  // Load customers
  useEffect(() => {
    const loadCustomers = async () => {
      try {
        const response = await ContactsService.getContacts({ contact_type: 'customer' });
        setCustomers(response.results);
      } catch (error) {
        console.error('Failed to load customers:', error);
        setSnackbar({
          open: true,
          message: 'Failed to load customers',
          severity: 'error',
        });
      }
    };

    loadCustomers();
  }, []);

  // Calculate line total
  const calculateLineTotal = (item: CustomerInvoiceItem): number => {
    const subtotal = item.quantity * item.unit_price;
    const taxAmount = subtotal * (item.tax_rate || 0) / 100;
    return subtotal + taxAmount;
  };

  // Update line item
  const updateLineItem = (index: number, field: keyof CustomerInvoiceItem, value: any) => {
    const updatedItems = [...formData.line_items];
    updatedItems[index] = { ...updatedItems[index], [field]: value };
    
    // Recalculate totals for this line
    if (field === 'quantity' || field === 'unit_price' || field === 'tax_rate') {
      const item = updatedItems[index];
      const subtotal = item.quantity * item.unit_price;
      const taxAmount = subtotal * (item.tax_rate || 0) / 100;
      updatedItems[index].tax_amount = taxAmount;
      updatedItems[index].line_total = subtotal + taxAmount;
    }

    setFormData(prev => ({ ...prev, line_items: updatedItems }));
  };

  // Add new line item
  const addLineItem = () => {
    setFormData(prev => ({
      ...prev,
      line_items: [
        ...prev.line_items,
        {
          item_description: '',
          quantity: 1,
          unit_price: 0,
          line_total: 0,
          tax_rate: 0,
          tax_amount: 0,
          account_code: '4010-SALES',
        }
      ]
    }));
  };

  // Remove line item
  const removeLineItem = (index: number) => {
    if (formData.line_items.length > 1) {
      setFormData(prev => ({
        ...prev,
        line_items: prev.line_items.filter((_, i) => i !== index)
      }));
    }
  };

  // Calculate totals
  const calculateTotals = () => {
    const subtotal = formData.line_items.reduce((sum, item) => sum + (item.quantity * item.unit_price), 0);
    const taxAmount = formData.line_items.reduce((sum, item) => sum + (item.tax_amount || 0), 0);
    const totalAmount = subtotal + taxAmount;

    return { subtotal, taxAmount, totalAmount };
  };

  // Handle form submission
  const handleSubmit = async (status: 'draft' | 'posted' = 'draft') => {
    if (!formData.customer) {
      setSnackbar({
        open: true,
        message: 'Please select a customer',
        severity: 'error',
      });
      return;
    }

    if (formData.line_items.length === 0 || formData.line_items.every(item => !item.item_description)) {
      setSnackbar({
        open: true,
        message: 'Please add at least one line item',
        severity: 'error',
      });
      return;
    }

    try {
      setLoading(true);
      
      const { subtotal, taxAmount, totalAmount } = calculateTotals();
      
      const invoiceData: Partial<CustomerInvoice> = {
        customer: formData.customer as number,
        invoice_date: formData.invoice_date,
        due_date: formData.due_date,
        reference_number: formData.reference_number,
        notes: formData.notes,
        terms_and_conditions: formData.terms_and_conditions,
        line_items: formData.line_items.filter(item => item.item_description.trim() !== ''),
        subtotal,
        tax_amount: taxAmount,
        total_amount: totalAmount,
        status: status,
      };

      const createdInvoice = await customerInvoiceService.createCustomerInvoice(invoiceData);

      setSnackbar({
        open: true,
        message: `Customer invoice ${createdInvoice.invoice_number} created successfully!`,
        severity: 'success',
      });

      // Navigate to the created invoice
      setTimeout(() => {
        navigate(`/dashboard/sales/customer-invoices/${createdInvoice.id}/edit`);
      }, 1500);

    } catch (error) {
      console.error('Failed to create customer invoice:', error);
      setSnackbar({
        open: true,
        message: `Failed to create customer invoice: ${error instanceof Error ? error.message : 'Unknown error'}`,
        severity: 'error',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCloseSnackbar = () => {
    setSnackbar(prev => ({ ...prev, open: false }));
  };

  const { subtotal, taxAmount, totalAmount } = calculateTotals();

  return (
    <Box sx={{ minHeight: '100vh', bgcolor: '#f5f5f5' }}>
      <Backdrop open={loading} sx={{ zIndex: 9999 }}>
        <CircularProgress color="primary" />
      </Backdrop>

      {/* Header */}
      <Box sx={{ bgcolor: 'white', borderBottom: 1, borderColor: 'divider', p: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box>
            <Typography variant="h4" component="h1" sx={{ fontWeight: 600, color: '#1a1a1a' }}>
              Create Customer Invoice
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
              Create a new customer invoice for services or products
            </Typography>
          </Box>
          <Button
            variant="outlined"
            startIcon={<ArrowBackIcon />}
            onClick={() => navigate('/dashboard/sales/customer-invoices')}
            sx={{ borderRadius: '8px' }}
          >
            Back to Customer Invoices
          </Button>
        </Box>
      </Box>

      {/* Content */}
      <Box sx={{ p: 3 }}>
        <Grid container spacing={3}>
          {/* Invoice Details */}
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
                  Invoice Details
                </Typography>
                
                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <FormControl fullWidth>
                      <InputLabel>Customer *</InputLabel>
                      <Select
                        value={formData.customer}
                        onChange={(e) => setFormData(prev => ({ ...prev, customer: e.target.value as number }))}
                        label="Customer *"
                      >
                        <MenuItem value="">
                          <em>Select Customer</em>
                        </MenuItem>
                        {customers.map((customer) => (
                          <MenuItem key={customer.id} value={customer.id}>
                            {customer.name}
                          </MenuItem>
                        ))}
                      </Select>
                    </FormControl>
                  </Grid>
                  
                  <Grid item xs={12} md={3}>
                    <TextField
                      fullWidth
                      label="Invoice Date"
                      type="date"
                      value={formData.invoice_date}
                      onChange={(e) => setFormData(prev => ({ ...prev, invoice_date: e.target.value }))}
                      InputLabelProps={{ shrink: true }}
                    />
                  </Grid>
                  
                  <Grid item xs={12} md={3}>
                    <TextField
                      fullWidth
                      label="Due Date"
                      type="date"
                      value={formData.due_date}
                      onChange={(e) => setFormData(prev => ({ ...prev, due_date: e.target.value }))}
                      InputLabelProps={{ shrink: true }}
                    />
                  </Grid>
                  
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Reference Number"
                      value={formData.reference_number}
                      onChange={(e) => setFormData(prev => ({ ...prev, reference_number: e.target.value }))}
                      placeholder="Customer PO number or reference"
                    />
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          {/* Line Items */}
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>
                    Line Items
                  </Typography>
                  <Button
                    variant="outlined"
                    startIcon={<AddIcon />}
                    onClick={addLineItem}
                    size="small"
                    sx={{ borderRadius: '6px' }}
                  >
                    Add Line
                  </Button>
                </Box>

                {formData.line_items.map((item, index) => (
                  <Box key={index} sx={{ mb: 3, p: 2, border: 1, borderColor: 'divider', borderRadius: 1 }}>
                    <Grid container spacing={2} alignItems="center">
                      <Grid item xs={12} md={4}>
                        <TextField
                          fullWidth
                          label="Description *"
                          value={item.item_description}
                          onChange={(e) => updateLineItem(index, 'item_description', e.target.value)}
                          placeholder="Product or service description"
                          multiline
                          rows={2}
                        />
                      </Grid>

                      <Grid item xs={6} md={1.5}>
                        <TextField
                          fullWidth
                          label="Quantity"
                          type="number"
                          value={item.quantity}
                          onChange={(e) => updateLineItem(index, 'quantity', parseFloat(e.target.value) || 0)}
                          inputProps={{ min: 0, step: 0.01 }}
                        />
                      </Grid>

                      <Grid item xs={6} md={1.5}>
                        <TextField
                          fullWidth
                          label="Unit Price"
                          type="number"
                          value={item.unit_price}
                          onChange={(e) => updateLineItem(index, 'unit_price', parseFloat(e.target.value) || 0)}
                          inputProps={{ min: 0, step: 0.01 }}
                        />
                      </Grid>

                      <Grid item xs={6} md={1.5}>
                        <TextField
                          fullWidth
                          label="Tax Rate (%)"
                          type="number"
                          value={item.tax_rate || 0}
                          onChange={(e) => updateLineItem(index, 'tax_rate', parseFloat(e.target.value) || 0)}
                          inputProps={{ min: 0, max: 100, step: 0.01 }}
                        />
                      </Grid>

                      <Grid item xs={6} md={2}>
                        <TextField
                          fullWidth
                          label="Account Code"
                          value={item.account_code || '4010-SALES'}
                          onChange={(e) => updateLineItem(index, 'account_code', e.target.value)}
                          placeholder="4010-SALES"
                        />
                      </Grid>

                      <Grid item xs={8} md={1}>
                        <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'right' }}>
                          {formatCurrency(item.line_total, '$')}
                        </Typography>
                      </Grid>

                      <Grid item xs={4} md={0.5}>
                        <IconButton
                          onClick={() => removeLineItem(index)}
                          disabled={formData.line_items.length === 1}
                          color="error"
                          size="small"
                        >
                          <DeleteIcon />
                        </IconButton>
                      </Grid>
                    </Grid>
                  </Box>
                ))}
              </CardContent>
            </Card>
          </Grid>

          {/* Additional Information and Totals */}
          <Grid item xs={12} md={8}>
            <Card>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
                  Additional Information
                </Typography>

                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Notes"
                      value={formData.notes}
                      onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                      multiline
                      rows={3}
                      placeholder="Internal notes for this invoice"
                    />
                  </Grid>

                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      label="Terms and Conditions"
                      value={formData.terms_and_conditions}
                      onChange={(e) => setFormData(prev => ({ ...prev, terms_and_conditions: e.target.value }))}
                      multiline
                      rows={3}
                      placeholder="Terms and conditions for the customer"
                    />
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
                  Invoice Summary
                </Typography>

                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">Subtotal:</Typography>
                  <Typography variant="body2">{formatCurrency(subtotal, '$')}</Typography>
                </Box>

                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">Tax Amount:</Typography>
                  <Typography variant="body2">{formatCurrency(taxAmount, '$')}</Typography>
                </Box>

                <Divider sx={{ my: 1 }} />

                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3 }}>
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>Total:</Typography>
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>{formatCurrency(totalAmount, '$')}</Typography>
                </Box>

                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                  <Button
                    variant="outlined"
                    startIcon={<SaveIcon />}
                    onClick={() => handleSubmit('draft')}
                    disabled={loading}
                    fullWidth
                    sx={{ borderRadius: '8px' }}
                  >
                    Save as Draft
                  </Button>

                  <Button
                    variant="contained"
                    startIcon={<ReceiptIcon />}
                    onClick={() => handleSubmit('posted')}
                    disabled={loading}
                    fullWidth
                    sx={{
                      bgcolor: 'success.main',
                      '&:hover': { bgcolor: 'success.dark' },
                      borderRadius: '8px',
                    }}
                  >
                    Create & Post Invoice
                  </Button>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Box>

      {/* Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity} sx={{ width: '100%' }}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default CreateCustomerInvoicePage;
