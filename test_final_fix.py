#!/usr/bin/env python3
"""
Final test to verify the vendor creation fix
"""
import requests
import json
import time

def get_auth_token():
    """Get authentication token"""
    url = "http://localhost:8000/api-token-auth/"
    data = {"username": "admin", "password": "admin123"}
    
    try:
        response = requests.post(url, json=data)
        if response.status_code == 200:
            return response.json().get('token')
        else:
            print(f"Auth failed: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"Auth error: {e}")
        return None

def test_vendor_creation():
    """Test vendor creation with the exact data format from frontend"""
    print("=== FINAL VENDOR CREATION TEST ===")
    
    # Get auth token
    token = get_auth_token()
    if not token:
        print("❌ Failed to get auth token")
        return False
    
    print(f"✅ Got auth token")
    
    # Test data exactly as sent by frontend
    test_data = {
        "displayName": "Final Test Company",
        "firstName": "<PERSON>",
        "lastName": "Doe",
        "companyName": "Final Test Company Ltd",
        "email": "<EMAIL>",
        "phone": "************",
        "mobile": "************",
        "billingAddress": {
            "street": "123 Final St",
            "city": "Test City",
            "state": "Test State",
            "postalCode": "12345",
            "country": "India"
        },
        "shippingAddress": {
            "sameAsBilling": True,
            "street": "",
            "city": "",
            "state": "",
            "postalCode": "",
            "country": "India"
        },
        "paymentTerms": "1",
        "creditLimit": 15000,
        "vendorCategory": "Technology",
        "leadTimeDays": 7,
        "minimumOrderAmount": 1000,
        "preferredVendor": True,
        "notes": "Final test vendor",
        "is_active": True
    }
    
    url = "http://localhost:8000/api/contacts/vendors/"
    headers = {
        "Authorization": f"Token {token}",
        "Content-Type": "application/json"
    }
    
    print(f"📤 Sending request to: {url}")
    print(f"📤 Data: {json.dumps(test_data, indent=2)}")
    
    try:
        response = requests.post(url, json=test_data, headers=headers)
        
        print(f"\n📥 Response Status: {response.status_code}")
        print(f"📥 Response Body: {response.text}")
        
        if response.status_code == 201:
            print("\n🎉 SUCCESS! Vendor created successfully!")
            created_vendor = response.json()
            print(f"✅ Created vendor ID: {created_vendor.get('id', 'N/A')}")
            print(f"✅ Company name: {created_vendor.get('company_name', 'N/A')}")
            return True
        elif response.status_code == 400:
            print("\n❌ STILL GETTING 400 ERROR")
            try:
                error_data = response.json()
                print(f"❌ Error details: {json.dumps(error_data, indent=2)}")
                
                if 'companyName' in error_data:
                    print(f"❌ companyName error: {error_data['companyName']}")
                else:
                    print("✅ No companyName error - different issue")
                    
            except Exception as e:
                print(f"Could not parse error: {e}")
            return False
        else:
            print(f"\n❌ Unexpected status: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Request failed: {e}")
        return False

def test_empty_company_name():
    """Test with empty company name"""
    print("\n=== TESTING EMPTY COMPANY NAME ===")
    
    token = get_auth_token()
    if not token:
        return False
    
    test_data = {
        "displayName": "Individual Vendor",
        "firstName": "Jane",
        "lastName": "Smith",
        "companyName": "",  # Empty company name
        "email": "<EMAIL>",
        "phone": "************",
        "billingAddress": {
            "street": "456 Individual St",
            "city": "Test City",
            "state": "Test State",
            "postalCode": "12345",
            "country": "India"
        },
        "shippingAddress": {
            "sameAsBilling": True,
            "street": "",
            "city": "",
            "state": "",
            "postalCode": "",
            "country": "India"
        },
        "paymentTerms": "1",
        "creditLimit": 5000,
        "vendorCategory": "Service",
        "leadTimeDays": 3,
        "minimumOrderAmount": 100,
        "preferredVendor": False,
        "notes": "Individual vendor test",
        "is_active": True
    }
    
    url = "http://localhost:8000/api/contacts/vendors/"
    headers = {
        "Authorization": f"Token {token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.post(url, json=test_data, headers=headers)
        print(f"Status: {response.status_code}")
        
        if response.status_code == 201:
            print("✅ Empty company name works!")
            return True
        elif response.status_code == 400:
            error_data = response.json()
            if 'companyName' in error_data:
                print(f"❌ Empty companyName error: {error_data['companyName']}")
            else:
                print("✅ No companyName error with empty value")
            return False
        else:
            print(f"❌ Unexpected status: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("🔧 TESTING FINAL VENDOR API FIX")
    print("=" * 50)
    
    # Test 1: Normal vendor creation
    test1_result = test_vendor_creation()
    
    # Test 2: Empty company name
    test2_result = test_empty_company_name()
    
    print(f"\n{'=' * 50}")
    print(f"📊 FINAL RESULTS:")
    print(f"Vendor Creation: {'✅ PASS' if test1_result else '❌ FAIL'}")
    print(f"Empty Company Name: {'✅ PASS' if test2_result else '❌ FAIL'}")
    
    if test1_result and test2_result:
        print(f"\n🎉 ALL TESTS PASSED!")
        print(f"The vendor API fix is working correctly.")
        print(f"Frontend should now be able to create vendors without errors.")
    else:
        print(f"\n❌ SOME TESTS FAILED")
        print(f"There are still issues that need to be resolved.")
