#!/usr/bin/env python3
"""
Basic server connectivity test
"""
import requests

def test_basic_connectivity():
    """Test basic server connectivity"""
    try:
        print("Testing basic server connectivity...")
        
        # Test if server is running
        response = requests.get("http://localhost:8000", timeout=5)
        print(f"Server root status: {response.status_code}")
        
        # Test API root
        response = requests.get("http://localhost:8000/api/", timeout=5)
        print(f"API root status: {response.status_code}")
        
        print("✅ Server is responding")
        
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to server")
    except requests.exceptions.Timeout:
        print("❌ Server timeout")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_basic_connectivity()
