#!/usr/bin/env python3
import requests

BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api"
TOKEN = "e15bc01f831c5111f413f534ef82288744cb7d41"

HEADERS = {
    'Content-Type': 'application/json',
    'Authorization': f'Token {TOKEN}'
}

# Quick test
try:
    vendors_response = requests.get(f"{API_BASE}/contacts/vendors/", headers=HEADERS, timeout=3)
    vendor_id = vendors_response.json()['results'][0]['contact']
    
    bill_data = {
        "vendor": vendor_id,
        "bill_date": "2025-07-06",
        "due_date": "2025-08-05",
        "status": "draft",
        "payment_terms": "Net 30",
        "reference_number": "QUICK-TEST",
        "notes": "Quick test",
        "line_items": [
            {
                "item_description": "Test item",
                "quantity": 1,
                "unit_price": 100.00,
                "tax_rate": 10.0,
                "account_code": "5010-COGS",
                "line_order": 1
            }
        ]
    }
    
    response = requests.post(f"{API_BASE}/purchase/vendor-bills/", json=bill_data, headers=HEADERS, timeout=5)
    print(f"Status: {response.status_code}")
    if response.status_code == 201:
        print("✅ SUCCESS - Regular bills working!")
    else:
        print(f"❌ ERROR: {response.text}")
        
except Exception as e:
    print(f"❌ Exception: {e}")
