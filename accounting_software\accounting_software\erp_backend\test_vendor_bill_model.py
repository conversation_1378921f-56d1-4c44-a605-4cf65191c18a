#!/usr/bin/env python
import os
import sys
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'erp_backend.settings')
django.setup()

from purchase.models import VendorBill, VendorBillItem
from contacts.models import Contact

def test_vendor_bill_model():
    """Test if VendorBill model can be queried"""
    try:
        print("Testing VendorBill model...")
        
        # Test basic query
        bills = VendorBill.objects.all()
        print(f"✅ Basic query works - found {bills.count()} bills")
        
        # Test the problematic query from the ViewSet
        try:
            bills_with_relations = VendorBill.objects.select_related('vendor', 'purchase_order', 'grn', 'goods_return_note').prefetch_related('line_items')
            count = bills_with_relations.count()
            print(f"✅ Complex query works - found {count} bills with relations")
        except Exception as e:
            print(f"❌ Complex query failed: {e}")
            
            # Test each relation individually
            try:
                bills_vendor = VendorBill.objects.select_related('vendor')
                print(f"✅ vendor relation works - {bills_vendor.count()} bills")
            except Exception as e2:
                print(f"❌ vendor relation failed: {e2}")
                
            try:
                bills_po = VendorBill.objects.select_related('purchase_order')
                print(f"✅ purchase_order relation works - {bills_po.count()} bills")
            except Exception as e2:
                print(f"❌ purchase_order relation failed: {e2}")
                
            try:
                bills_grn = VendorBill.objects.select_related('grn')
                print(f"✅ grn relation works - {bills_grn.count()} bills")
            except Exception as e2:
                print(f"❌ grn relation failed: {e2}")
                
            try:
                bills_return = VendorBill.objects.select_related('goods_return_note')
                print(f"✅ goods_return_note relation works - {bills_return.count()} bills")
            except Exception as e2:
                print(f"❌ goods_return_note relation failed: {e2}")
        
        # Test creating a simple vendor bill
        try:
            # Get a vendor
            vendor = Contact.objects.filter(contact_type='vendor').first()
            if vendor:
                print(f"Found vendor: {vendor.name}")

                # Try to create a bill (but don't save it)
                bill = VendorBill(
                    vendor=vendor,
                    bill_date='2025-07-06',
                    due_date='2025-08-05',
                    status='draft'
                )
                print("✅ VendorBill object creation works")

                # Test serializer
                from purchase.serializers import VendorBillSerializer
                serializer = VendorBillSerializer(bill)
                print("✅ VendorBillSerializer works")
                print(f"Serialized data keys: {list(serializer.data.keys())}")

            else:
                print("❌ No vendor found to test with")

        except Exception as e:
            print(f"❌ VendorBill creation/serialization failed: {e}")
            import traceback
            traceback.print_exc()
        
    except Exception as e:
        print(f"❌ VendorBill model test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_vendor_bill_model()
