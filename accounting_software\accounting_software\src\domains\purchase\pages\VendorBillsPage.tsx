import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Grid,
  TextField,
  MenuItem,
  Chip,
  IconButton,
  Tooltip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Avatar,
  Divider,
  Alert,
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  Email as EmailIcon,
  FileCopy as DuplicateIcon,
  Receipt as ReceiptIcon,
  Description as DescriptionIcon,
  Assignment as AssignmentIcon,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { StandardDatePicker } from '../../../shared/components';
import { formatCurrency } from '../../../shared/utils/formatters';
import StatCard from '../../../shared/components/StatCard';
import dayjs from 'dayjs';
import { vendorBillService, type VendorBill, type VendorBillStats, type VendorBillFilters } from '../../../services/vendor-bill.service';

const VendorBillsPage: React.FC = () => {
  const navigate = useNavigate();
  
  // State
  const [bills, setBills] = useState<VendorBill[]>([]);
  const [stats, setStats] = useState<VendorBillStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<VendorBillFilters>({});

  // Load vendor bills from API
  useEffect(() => {
    loadVendorBills();
  }, [filters]);

  const loadVendorBills = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('Loading vendor bills with filters:', filters);

      // Load bills and stats in parallel
      const [billsResponse, statsResponse] = await Promise.all([
        vendorBillService.getVendorBills(filters),
        vendorBillService.getVendorBillStats().catch(err => {
          console.warn('Failed to load stats:', err);
          return null;
        })
      ]);

      console.log('Vendor bills loaded:', billsResponse);
      console.log('Stats loaded:', statsResponse);

      setBills(billsResponse.results || []);
      setStats(statsResponse);

    } catch (err) {
      console.error('Failed to load vendor bills:', err);
      setError(err instanceof Error ? err.message : 'Failed to load vendor bills');

      // Set empty data on error
      setBills([]);
      setStats(null);
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (field: keyof VendorBillFilters, value: any) => {
    setFilters(prev => ({
      ...prev,
      [field]: value || undefined
    }));
  };

  const handleDelete = async (id: number) => {
    if (window.confirm('Are you sure you want to delete this vendor bill?')) {
      // TODO: Implement delete functionality
      console.log('Delete bill:', id);
    }
  };

  const handleDuplicate = async (id: number) => {
    // TODO: Implement duplicate functionality
    console.log('Duplicate bill:', id);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid': return 'success';
      case 'approved': return 'info';
      case 'draft': return 'default';
      case 'rejected': return 'error';
      default: return 'default';
    }
  };

  const getSourceIcon = (sourceType?: string) => {
    switch (sourceType) {
      case 'grn': return <ReceiptIcon fontSize="small" />;
      case 'po': return <DescriptionIcon fontSize="small" />;
      case 'return_note': return <AssignmentIcon fontSize="small" />;
      default: return <EditIcon fontSize="small" />;
    }
  };

  const getSourceLabel = (sourceType?: string) => {
    switch (sourceType) {
      case 'grn': return 'From GRN';
      case 'po': return 'From PO';
      case 'return_note': return 'From Return';
      default: return 'Manual';
    }
  };

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Box>
          <Typography variant="h4" component="h1" sx={{ fontWeight: 600, color: '#1a1a1a' }}>
            Vendor Bills
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
            Manage accounts payable and vendor bills
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button
            variant="outlined"
            startIcon={<ReceiptIcon />}
            onClick={() => navigate('/dashboard/purchases/vendor-bills/create-from-grn')}
            sx={{ borderRadius: '8px' }}
          >
            From GRN
          </Button>
          <Button
            variant="outlined"
            startIcon={<DescriptionIcon />}
            onClick={() => navigate('/dashboard/purchases/vendor-bills/create-from-po')}
            sx={{ borderRadius: '8px' }}
          >
            From PO
          </Button>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => navigate('/dashboard/purchases/vendor-bills/create')}
            sx={{ 
              borderRadius: '8px',
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              '&:hover': {
                background: 'linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%)',
              }
            }}
          >
            Create Bill
          </Button>
        </Box>
      </Box>

      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
          <Button
            size="small"
            onClick={loadVendorBills}
            sx={{ ml: 2 }}
          >
            Retry
          </Button>
        </Alert>
      )}

      {/* Statistics Cards */}
      {stats && (
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={2}>
            <Card sx={{ 
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', 
              color: 'white',
              borderRadius: '12px',
            }}>
              <CardContent sx={{ textAlign: 'center', py: 2 }}>
                <Avatar sx={{ bgcolor: 'rgba(255,255,255,0.2)', mx: 'auto', mb: 1 }}>
                  <ReceiptIcon />
                </Avatar>
                <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 0.5 }}>
                  {stats.total_bills}
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.9 }}>
                  Total Bills
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} sm={6} md={2}>
            <Card sx={{ 
              background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)', 
              color: 'white',
              borderRadius: '12px',
            }}>
              <CardContent sx={{ textAlign: 'center', py: 2 }}>
                <Typography variant="h5" sx={{ fontWeight: 'bold', mb: 0.5 }}>
                  {formatCurrency(stats.total_payables)}
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.9 }}>
                  Total Payables
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} sm={6} md={2}>
            <Card sx={{ 
              background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)', 
              color: 'white',
              borderRadius: '12px',
            }}>
              <CardContent sx={{ textAlign: 'center', py: 2 }}>
                <Typography variant="h5" sx={{ fontWeight: 'bold', mb: 0.5 }}>
                  {formatCurrency(stats.outstanding_amount)}
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.9 }}>
                  Outstanding
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} sm={6} md={2}>
            <Card sx={{ 
              background: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)', 
              color: 'white',
              borderRadius: '12px',
            }}>
              <CardContent sx={{ textAlign: 'center', py: 2 }}>
                <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 0.5 }}>
                  {stats.overdue_count}
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.9 }}>
                  Overdue
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} sm={6} md={2}>
            <Card sx={{ 
              background: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)', 
              color: '#333',
              borderRadius: '12px',
            }}>
              <CardContent sx={{ textAlign: 'center', py: 2 }}>
                <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 0.5 }}>
                  {stats.paid_count}
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.8 }}>
                  Paid
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          
          <Grid item xs={12} sm={6} md={2}>
            <Card sx={{ 
              background: 'linear-gradient(135deg, #d299c2 0%, #fef9d7 100%)', 
              color: '#333',
              borderRadius: '12px',
            }}>
              <CardContent sx={{ textAlign: 'center', py: 2 }}>
                <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 0.5 }}>
                  {stats.draft_count}
                </Typography>
                <Typography variant="body2" sx={{ opacity: 0.8 }}>
                  Draft
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Filters */}
      <Card sx={{ mb: 3, borderRadius: '12px', boxShadow: '0 2px 12px rgba(0,0,0,0.08)' }}>
        <CardContent>
          <Typography variant="h6" gutterBottom sx={{ mb: 2, fontWeight: 600 }}>
            Filters
          </Typography>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={2}>
              <TextField
                fullWidth
                size="small"
                label="Search"
                value={filters.search || ''}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                placeholder="Search bills..."
                sx={{ borderRadius: '8px' }}
              />
            </Grid>
            <Grid item xs={12} md={2}>
              <TextField
                fullWidth
                size="small"
                select
                label="Status"
                value={filters.status || ''}
                onChange={(e) => handleFilterChange('status', e.target.value)}
              >
                <MenuItem value="">All Status</MenuItem>
                <MenuItem value="draft">Draft</MenuItem>
                <MenuItem value="approved">Approved</MenuItem>
                <MenuItem value="paid">Paid</MenuItem>
                <MenuItem value="rejected">Rejected</MenuItem>
              </TextField>
            </Grid>
            <Grid item xs={12} md={2}>
              <TextField
                fullWidth
                size="small"
                select
                label="Source"
                value={filters.source_type || ''}
                onChange={(e) => handleFilterChange('source_type', e.target.value)}
              >
                <MenuItem value="">All Sources</MenuItem>
                <MenuItem value="manual">Manual</MenuItem>
                <MenuItem value="grn">From GRN</MenuItem>
                <MenuItem value="po">From PO</MenuItem>
                <MenuItem value="return_note">From Return</MenuItem>
              </TextField>
            </Grid>
            <Grid item xs={12} md={2}>
              <StandardDatePicker
                label="From Date"
                value={filters.date_from ? dayjs(filters.date_from) : null}
                onChange={(date) => handleFilterChange('date_from', date?.format('YYYY-MM-DD'))}
                businessContext="general"
                dateFormat="DD/MM/YYYY"
                size="small"
              />
            </Grid>
            <Grid item xs={12} md={2}>
              <StandardDatePicker
                label="To Date"
                value={filters.date_to ? dayjs(filters.date_to) : null}
                onChange={(date) => handleFilterChange('date_to', date?.format('YYYY-MM-DD'))}
                businessContext="general"
                dateFormat="DD/MM/YYYY"
                size="small"
              />
            </Grid>
            <Grid item xs={12} md={2}>
              <Button
                variant="outlined"
                onClick={() => setFilters({})}
                fullWidth
                sx={{ borderRadius: '8px' }}
              >
                Clear Filters
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Bills Table */}
      <Card sx={{ borderRadius: '12px', boxShadow: '0 2px 12px rgba(0,0,0,0.08)' }}>
        <CardContent sx={{ p: 0 }}>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow sx={{ backgroundColor: '#f8f9fa' }}>
                  <TableCell sx={{ fontWeight: 600, color: '#495057' }}>Bill Number</TableCell>
                  <TableCell sx={{ fontWeight: 600, color: '#495057' }}>Vendor</TableCell>
                  <TableCell sx={{ fontWeight: 600, color: '#495057' }}>Date</TableCell>
                  <TableCell sx={{ fontWeight: 600, color: '#495057' }}>Due Date</TableCell>
                  <TableCell sx={{ fontWeight: 600, color: '#495057' }}>Status</TableCell>
                  <TableCell sx={{ fontWeight: 600, color: '#495057' }}>Source</TableCell>
                  <TableCell align="right" sx={{ fontWeight: 600, color: '#495057' }}>Amount</TableCell>
                  <TableCell align="center" sx={{ fontWeight: 600, color: '#495057' }}>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {bills.map((bill) => (
                  <TableRow key={bill.id} hover>
                    <TableCell>
                      <Typography variant="body2" fontWeight="medium" color="primary">
                        {bill.bill_number}
                      </Typography>
                      {bill.reference_number && (
                        <Typography variant="caption" color="text.secondary">
                          Ref: {bill.reference_number}
                        </Typography>
                      )}
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {bill.vendor_name}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {dayjs(bill.bill_date).format('DD/MM/YYYY')}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {dayjs(bill.due_date).format('DD/MM/YYYY')}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={bill.status.toUpperCase()}
                        color={getStatusColor(bill.status) as any}
                        size="small"
                        sx={{ borderRadius: '6px' }}
                      />
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        {getSourceIcon(bill.source_type)}
                        <Typography variant="caption">
                          {getSourceLabel(bill.source_type)}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell align="right">
                      <Typography variant="body2" fontWeight="medium">
                        {formatCurrency(bill.total_amount)}
                      </Typography>
                      {bill.balance_due > 0 && (
                        <Typography variant="caption" color="error">
                          Due: {formatCurrency(bill.balance_due)}
                        </Typography>
                      )}
                    </TableCell>
                    <TableCell align="center">
                      <Box sx={{ display: 'flex', gap: 0.5 }}>
                        <Tooltip title="View">
                          <IconButton
                            size="small"
                            onClick={() => navigate(`/dashboard/purchases/vendor-bills/${bill.id}`)}
                          >
                            <ViewIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Edit">
                          <IconButton
                            size="small"
                            onClick={() => navigate(`/dashboard/purchases/vendor-bills/${bill.id}/edit`)}
                          >
                            <EditIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Duplicate">
                          <IconButton
                            size="small"
                            onClick={() => handleDuplicate(bill.id)}
                          >
                            <DuplicateIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Delete">
                          <IconButton
                            size="small"
                            onClick={() => handleDelete(bill.id)}
                            color="error"
                          >
                            <DeleteIcon fontSize="small" />
                          </IconButton>
                        </Tooltip>
                      </Box>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>
    </Box>
  );
};

export default VendorBillsPage;
