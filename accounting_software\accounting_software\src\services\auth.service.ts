import api from './api';

interface LoginCredentials {
  username: string;
  password: string;
}

interface DjangoTokenResponse {
  token: string;
}

interface AuthResponse {
  token: string;
  user: {
    id: number;
    username: string;
    email: string;
    firstName: string;
    lastName: string;
    role: string;
  };
}

export const authService = {
  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    // First get the token from Django
    const tokenResponse = await fetch('http://localhost:8000/api-token-auth/', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(credentials),
    });

    if (!tokenResponse.ok) {
      const errorData = await tokenResponse.json();
      throw new Error(errorData.non_field_errors?.[0] || 'Login failed');
    }

    const tokenData: DjangoTokenResponse = await tokenResponse.json();

    // Get user details using the token
    const userResponse = await fetch('http://localhost:8000/api/hr/employees/me/', {
      headers: {
        'Authorization': `Token ${tokenData.token}`,
        'Content-Type': 'application/json',
      },
    });

    let user = {
      id: 1,
      username: credentials.username,
      email: '',
      firstName: '',
      lastName: '',
      role: 'admin',
    };

    if (userResponse.ok) {
      const userData = await userResponse.json();
      user = {
        id: userData.id || 1,
        username: userData.username || credentials.username,
        email: userData.email || '',
        firstName: userData.first_name || '',
        lastName: userData.last_name || '',
        role: userData.role || 'admin',
      };
    }

    const authResponse: AuthResponse = {
      token: tokenData.token,
      user,
    };

    localStorage.setItem('token', authResponse.token);
    localStorage.setItem('user', JSON.stringify(authResponse.user));
    return authResponse;
  },

  logout() {
    localStorage.removeItem('token');
    localStorage.removeItem('user');
  },

  getCurrentUser() {
    const userStr = localStorage.getItem('user');
    return userStr ? JSON.parse(userStr) : null;
  },

  isAuthenticated() {
    return !!localStorage.getItem('token');
  }
};