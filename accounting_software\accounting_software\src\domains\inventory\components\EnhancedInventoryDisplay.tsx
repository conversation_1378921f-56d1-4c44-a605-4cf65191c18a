import React, { useState, useEffect } from 'react';
import {
  Box,
  Typo<PERSON>,
  Card,
  CardContent,
  Button,
  Alert,
  Grid,
  Chip,
  CircularProgress,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Divider,
  LinearProgress,
} from '@mui/material';
import {
  Inventory as InventoryIcon,
  Search as SearchIcon,
  Refresh as RefreshIcon,
  Visibility as ViewIcon,
  Warning as WarningIcon,
  TrendingDown as LowStockIcon,
  Assessment as ValuationIcon,
  Layers as LayersIcon,
  NotificationAdd as AlertIcon,
} from '@mui/icons-material';
import { inventoryService, InventoryItem, Warehouse, WarehouseLocation } from '../services/inventory.service';
import { formatCurrency, formatDate } from '../../../shared/utils/formatters';
import { useCurrencyInfo } from '../../gl/hooks/useCurrencyInfo';

interface EnhancedInventoryDisplayProps {
  selectedWarehouse?: Warehouse;
  selectedLocation?: WarehouseLocation;
  onInventorySelect?: (inventory: InventoryItem) => void;
}

const EnhancedInventoryDisplay: React.FC<EnhancedInventoryDisplayProps> = ({
  selectedWarehouse,
  selectedLocation,
  onInventorySelect
}) => {
  const [inventory, setInventory] = useState<InventoryItem[]>([]);
  const [warehouses, setWarehouses] = useState<Warehouse[]>([]);
  const [locations, setLocations] = useState<WarehouseLocation[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [warehouseFilter, setWarehouseFilter] = useState(selectedWarehouse?.warehouse_id || '');
  const [locationFilter, setLocationFilter] = useState(selectedLocation?.location_id || '');
  const [valuationMethodFilter, setValuationMethodFilter] = useState('');
  const [abcFilter, setAbcFilter] = useState('');
  const [stockFilter, setStockFilter] = useState('');
  
  // Dialog states
  const [openValuationDialog, setOpenValuationDialog] = useState(false);
  const [openCostLayersDialog, setOpenCostLayersDialog] = useState(false);
  const [selectedInventory, setSelectedInventory] = useState<InventoryItem | null>(null);
  const [valuationData, setValuationData] = useState<any>(null);
  const [costLayers, setCostLayers] = useState<any[]>([]);

  const { currencyInfo } = useCurrencyInfo();

  useEffect(() => {
    loadWarehouses();
    loadInventory();
  }, []);

  useEffect(() => {
    if (warehouseFilter) {
      loadLocations();
    }
  }, [warehouseFilter]);

  useEffect(() => {
    loadInventory();
  }, [searchTerm, warehouseFilter, locationFilter, valuationMethodFilter, abcFilter, stockFilter]);

  const loadWarehouses = async () => {
    try {
      const response = await inventoryService.getAllWarehouses();
      setWarehouses(response);
    } catch (err) {
      console.error('Failed to load warehouses:', err);
    }
  };

  const loadLocations = async () => {
    try {
      const response = await inventoryService.getAllWarehouseLocations({
        warehouse: warehouseFilter ? parseInt(warehouseFilter.toString()) : undefined,
        is_active: true,
      });
      setLocations(response.results || response);
    } catch (err) {
      console.error('Failed to load locations:', err);
    }
  };

  const loadInventory = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await inventoryService.getEnhancedInventory({
        search: searchTerm || undefined,
        warehouse: warehouseFilter ? parseInt(warehouseFilter.toString()) : undefined,
        location: locationFilter ? parseInt(locationFilter.toString()) : undefined,
        valuation_method: valuationMethodFilter || undefined,
        abc_classification: abcFilter || undefined,
        low_stock: stockFilter === 'low_stock' ? true : undefined,
        out_of_stock: stockFilter === 'out_of_stock' ? true : undefined,
        negative_stock: stockFilter === 'negative_stock' ? true : undefined,
      });
      
      setInventory(response.results || response);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load inventory');
    } finally {
      setLoading(false);
    }
  };

  const handleViewValuation = async (item: InventoryItem) => {
    try {
      setSelectedInventory(item);
      setLoading(true);
      
      const valuation = await inventoryService.getInventoryValuation(item.inventory_id);
      setValuationData(valuation);
      setOpenValuationDialog(true);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load valuation data');
    } finally {
      setLoading(false);
    }
  };

  const handleViewCostLayers = async (item: InventoryItem) => {
    try {
      setSelectedInventory(item);
      setLoading(true);
      
      const layers = await inventoryService.getInventoryCostLayers(item.inventory_id);
      setCostLayers(layers);
      setOpenCostLayersDialog(true);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load cost layers');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateAlert = async (item: InventoryItem) => {
    try {
      setLoading(true);
      await inventoryService.createInventoryAlert(item.inventory_id);
      // Refresh inventory to show updated alert status
      loadInventory();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create alert');
    } finally {
      setLoading(false);
    }
  };

  const getStockStatusColor = (item: InventoryItem) => {
    if (item.is_out_of_stock) return 'error';
    if (item.is_below_reorder_point) return 'warning';
    return 'success';
  };

  const getStockStatusIcon = (item: InventoryItem) => {
    if (item.is_out_of_stock) return <WarningIcon />;
    if (item.is_below_reorder_point) return <LowStockIcon />;
    return null;
  };

  const getAbcColor = (classification?: string) => {
    switch (classification) {
      case 'A': return 'error';
      case 'B': return 'warning';
      case 'C': return 'info';
      default: return 'default';
    }
  };

  const getValuationMethodColor = (method: string) => {
    switch (method) {
      case 'FIFO': return 'primary';
      case 'LIFO': return 'secondary';
      case 'AVERAGE': return 'info';
      case 'STANDARD': return 'success';
      default: return 'default';
    }
  };

  return (
    <Box>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h5" component="h1">
          <InventoryIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
          Enhanced Inventory
          {selectedWarehouse && (
            <Typography variant="subtitle1" color="textSecondary" component="span" sx={{ ml: 2 }}>
              - {selectedWarehouse.name}
            </Typography>
          )}
        </Typography>
        <Button
          variant="outlined"
          startIcon={<RefreshIcon />}
          onClick={loadInventory}
          disabled={loading}
        >
          Refresh
        </Button>
      </Box>

      {/* Filters */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={3}>
              <TextField
                fullWidth
                label="Search products"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                size="small"
                InputProps={{
                  startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />,
                }}
              />
            </Grid>
            
            {!selectedWarehouse && (
              <Grid item xs={12} md={2}>
                <FormControl fullWidth size="small">
                  <InputLabel>Warehouse</InputLabel>
                  <Select
                    value={warehouseFilter}
                    onChange={(e) => {
                      setWarehouseFilter(e.target.value);
                      setLocationFilter(''); // Reset location when warehouse changes
                    }}
                    label="Warehouse"
                  >
                    <MenuItem value="">All Warehouses</MenuItem>
                    {warehouses.map((warehouse) => (
                      <MenuItem key={warehouse.warehouse_id} value={warehouse.warehouse_id}>
                        {warehouse.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
            )}
            
            <Grid item xs={12} md={2}>
              <FormControl fullWidth size="small">
                <InputLabel>Location</InputLabel>
                <Select
                  value={locationFilter}
                  onChange={(e) => setLocationFilter(e.target.value)}
                  label="Location"
                  disabled={!warehouseFilter}
                >
                  <MenuItem value="">All Locations</MenuItem>
                  {locations.map((location) => (
                    <MenuItem key={location.location_id} value={location.location_id}>
                      {location.location_code}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} md={2}>
              <FormControl fullWidth size="small">
                <InputLabel>Valuation Method</InputLabel>
                <Select
                  value={valuationMethodFilter}
                  onChange={(e) => setValuationMethodFilter(e.target.value)}
                  label="Valuation Method"
                >
                  <MenuItem value="">All Methods</MenuItem>
                  <MenuItem value="FIFO">FIFO</MenuItem>
                  <MenuItem value="LIFO">LIFO</MenuItem>
                  <MenuItem value="AVERAGE">Average</MenuItem>
                  <MenuItem value="STANDARD">Standard</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} md={1}>
              <FormControl fullWidth size="small">
                <InputLabel>ABC</InputLabel>
                <Select
                  value={abcFilter}
                  onChange={(e) => setAbcFilter(e.target.value)}
                  label="ABC"
                >
                  <MenuItem value="">All</MenuItem>
                  <MenuItem value="A">A</MenuItem>
                  <MenuItem value="B">B</MenuItem>
                  <MenuItem value="C">C</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} md={2}>
              <FormControl fullWidth size="small">
                <InputLabel>Stock Status</InputLabel>
                <Select
                  value={stockFilter}
                  onChange={(e) => setStockFilter(e.target.value)}
                  label="Stock Status"
                >
                  <MenuItem value="">All Stock</MenuItem>
                  <MenuItem value="low_stock">Low Stock</MenuItem>
                  <MenuItem value="out_of_stock">Out of Stock</MenuItem>
                  <MenuItem value="negative_stock">Negative Stock</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Inventory Table */}
      <Card>
        <CardContent>
          {loading ? (
            <Box display="flex" justifyContent="center" p={3}>
              <CircularProgress />
            </Box>
          ) : (
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Product</TableCell>
                    <TableCell>Location</TableCell>
                    <TableCell align="right">On Hand</TableCell>
                    <TableCell align="right">Available</TableCell>
                    <TableCell align="right">Reorder Point</TableCell>
                    <TableCell>Valuation</TableCell>
                    <TableCell align="right">Value</TableCell>
                    <TableCell>ABC</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell align="right">Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {inventory.map((item) => (
                    <TableRow 
                      key={item.inventory_id} 
                      hover
                      onClick={() => onInventorySelect?.(item)}
                      sx={{ cursor: onInventorySelect ? 'pointer' : 'default' }}
                    >
                      <TableCell>
                        <Box>
                          <Typography variant="subtitle2">
                            {item.product_name}
                          </Typography>
                          <Typography variant="caption" color="textSecondary" fontFamily="monospace">
                            {item.product_sku}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Box>
                          <Typography variant="body2">
                            {item.warehouse_name}
                          </Typography>
                          {item.location_code && (
                            <Typography variant="caption" color="textSecondary" fontFamily="monospace">
                              {item.location_code}
                            </Typography>
                          )}
                        </Box>
                      </TableCell>
                      <TableCell align="right">
                        <Typography variant="body2" fontWeight="bold">
                          {item.quantity_on_hand.toLocaleString()}
                        </Typography>
                      </TableCell>
                      <TableCell align="right">
                        <Typography 
                          variant="body2" 
                          color={item.quantity_available < 0 ? 'error.main' : 'text.primary'}
                        >
                          {item.quantity_available.toLocaleString()}
                        </Typography>
                      </TableCell>
                      <TableCell align="right">
                        <Typography variant="body2">
                          {item.reorder_point.toLocaleString()}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={item.valuation_method}
                          color={getValuationMethodColor(item.valuation_method) as any}
                          size="small"
                        />
                      </TableCell>
                      <TableCell align="right">
                        <Typography variant="body2" fontWeight="bold">
                          {formatCurrency(item.total_value_average, currencyInfo)}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        {item.abc_classification && (
                          <Chip
                            label={item.abc_classification}
                            color={getAbcColor(item.abc_classification) as any}
                            size="small"
                          />
                        )}
                      </TableCell>
                      <TableCell>
                        <Box display="flex" alignItems="center" gap={1}>
                          <Chip
                            icon={getStockStatusIcon(item)}
                            label={
                              item.is_out_of_stock ? 'Out of Stock' :
                              item.is_below_reorder_point ? 'Low Stock' : 'In Stock'
                            }
                            color={getStockStatusColor(item) as any}
                            size="small"
                          />
                        </Box>
                      </TableCell>
                      <TableCell align="right">
                        <Tooltip title="View Valuation">
                          <IconButton
                            size="small"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleViewValuation(item);
                            }}
                          >
                            <ValuationIcon />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="View Cost Layers">
                          <IconButton
                            size="small"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleViewCostLayers(item);
                            }}
                          >
                            <LayersIcon />
                          </IconButton>
                        </Tooltip>
                        {item.is_below_reorder_point && (
                          <Tooltip title="Create Alert">
                            <IconButton
                              size="small"
                              color="warning"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleCreateAlert(item);
                              }}
                            >
                              <AlertIcon />
                            </IconButton>
                          </Tooltip>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                  {inventory.length === 0 && !loading && (
                    <TableRow>
                      <TableCell colSpan={10} align="center">
                        <Typography color="textSecondary">
                          No inventory items found
                        </Typography>
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </CardContent>
      </Card>

      {/* Valuation Dialog */}
      <Dialog
        open={openValuationDialog}
        onClose={() => setOpenValuationDialog(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          Inventory Valuation - {selectedInventory?.product_name}
        </DialogTitle>
        <DialogContent>
          {valuationData && (
            <Grid container spacing={3}>
              <Grid item xs={6}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="h6" color="primary">
                      FIFO Method
                    </Typography>
                    <Typography variant="h4">
                      {formatCurrency(valuationData.fifo_value, currencyInfo)}
                    </Typography>
                    <Typography variant="caption">
                      Unit Cost: {formatCurrency(valuationData.fifo_unit_cost, currencyInfo)}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={6}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="h6" color="secondary">
                      LIFO Method
                    </Typography>
                    <Typography variant="h4">
                      {formatCurrency(valuationData.lifo_value, currencyInfo)}
                    </Typography>
                    <Typography variant="caption">
                      Unit Cost: {formatCurrency(valuationData.lifo_unit_cost, currencyInfo)}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={6}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="h6" color="info.main">
                      Average Method
                    </Typography>
                    <Typography variant="h4">
                      {formatCurrency(valuationData.average_value, currencyInfo)}
                    </Typography>
                    <Typography variant="caption">
                      Unit Cost: {formatCurrency(valuationData.average_unit_cost, currencyInfo)}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={6}>
                <Card variant="outlined">
                  <CardContent>
                    <Typography variant="h6" color="success.main">
                      Standard Method
                    </Typography>
                    <Typography variant="h4">
                      {formatCurrency(valuationData.standard_value, currencyInfo)}
                    </Typography>
                    <Typography variant="caption">
                      Unit Cost: {formatCurrency(valuationData.standard_unit_cost, currencyInfo)}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenValuationDialog(false)}>Close</Button>
        </DialogActions>
      </Dialog>

      {/* Cost Layers Dialog */}
      <Dialog
        open={openCostLayersDialog}
        onClose={() => setOpenCostLayersDialog(false)}
        maxWidth="lg"
        fullWidth
      >
        <DialogTitle>
          Cost Layers - {selectedInventory?.product_name}
        </DialogTitle>
        <DialogContent>
          <TableContainer>
            <Table size="small">
              <TableHead>
                <TableRow>
                  <TableCell>Layer Date</TableCell>
                  <TableCell>Reference</TableCell>
                  <TableCell align="right">Original Qty</TableCell>
                  <TableCell align="right">Remaining Qty</TableCell>
                  <TableCell align="right">Unit Cost</TableCell>
                  <TableCell align="right">Total Value</TableCell>
                  <TableCell>Batch</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {costLayers.map((layer, index) => (
                  <TableRow key={index}>
                    <TableCell>{formatDate(layer.layer_date)}</TableCell>
                    <TableCell>{layer.reference_type}</TableCell>
                    <TableCell align="right">{layer.original_quantity}</TableCell>
                    <TableCell align="right">{layer.remaining_quantity}</TableCell>
                    <TableCell align="right">
                      {formatCurrency(layer.unit_cost, currencyInfo)}
                    </TableCell>
                    <TableCell align="right">
                      {formatCurrency(layer.total_value, currencyInfo)}
                    </TableCell>
                    <TableCell>{layer.batch_number || '-'}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenCostLayersDialog(false)}>Close</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default EnhancedInventoryDisplay;
