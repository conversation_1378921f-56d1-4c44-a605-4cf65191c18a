#!/usr/bin/env python3
"""
Test creating vendor bills from Purchase Orders
"""
import requests
import json
from datetime import datetime, timedelta

# Configuration
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api"
TOKEN = "e15bc01f831c5111f413f534ef82288744cb7d41"

HEADERS = {
    'Content-Type': 'application/json',
    'Authorization': f'Token {TOKEN}'
}

def test_po_to_vendor_bill():
    print("📋➡️💰 TESTING PO TO VENDOR BILL CREATION")
    print("=" * 55)
    
    try:
        # 1. Get available vendors and products
        print("1. Getting vendors and products...")
        
        vendors_response = requests.get(f"{API_BASE}/contacts/vendors/", headers=HEADERS, timeout=5)
        vendor_id = vendors_response.json()['results'][0]['contact']
        vendor_name = vendors_response.json()['results'][0]['display_name']
        
        products_response = requests.get(f"{API_BASE}/sales/products/", headers=HEADERS, timeout=5)
        products = products_response.json()['results']
        
        print(f"✅ Vendor: {vendor_name} (ID: {vendor_id})")
        print(f"✅ Products available: {len(products)}")
        
        # 2. Create a Purchase Order first
        print(f"\n2. Creating Purchase Order...")
        
        po_data = {
            "vendor": vendor_id,
            "po_date": "2025-07-06",
            "expected_date": "2025-07-20",
            "status": "received",  # Set to received so we can create bill
            "payment_terms": "Net 30",
            "reference_number": "PO-TEST-001",
            "memo": "Test PO for vendor bill creation",
            "buyer_name": "Test Buyer",
            "buyer_email": "<EMAIL>",
            "line_items": [
                {
                    "product": products[0]['id'] if products else None,
                    "description": "Test Product for PO to Bill",
                    "quantity": 5,
                    "unit_price": 100.00,
                    "discount_percent": 0,
                    "taxable": True,
                    "tax_rate": 10.0,
                    "unit_of_measure": "pcs",
                    "line_order": 1
                },
                {
                    "description": "Service Item for PO to Bill",
                    "quantity": 2,
                    "unit_price": 200.00,
                    "discount_percent": 5.0,
                    "taxable": True,
                    "tax_rate": 10.0,
                    "unit_of_measure": "hrs",
                    "line_order": 2
                }
            ]
        }
        
        po_response = requests.post(f"{API_BASE}/purchase/purchase-orders/", json=po_data, headers=HEADERS, timeout=10)
        
        if po_response.status_code == 201:
            created_po = po_response.json()
            po_id = created_po['id']
            po_number = created_po['po_number']
            
            print(f"✅ PO created: {po_number} (ID: {po_id})")
            print(f"PO Status: {created_po['status']}")
            print(f"PO Total: ${created_po['total_amount']}")
            print(f"Line Items: {len(created_po['line_items'])}")
            
            # 3. Test creating vendor bill from PO
            print(f"\n3. Creating vendor bill from PO...")
            
            bill_from_po_response = requests.post(
                f"{API_BASE}/purchase/vendor-bills/create_from_po/", 
                json={"po_id": po_id}, 
                headers=HEADERS, 
                timeout=10
            )
            
            if bill_from_po_response.status_code == 201:
                created_bill = bill_from_po_response.json()
                bill_id = created_bill['id']
                bill_number = created_bill['bill_number']
                
                print(f"✅ Vendor bill created: {bill_number} (ID: {bill_id})")
                print(f"Bill Status: {created_bill['status']}")
                print(f"Bill Total: ${created_bill['total_amount']}")
                print(f"Bill Line Items: {len(created_bill['line_items'])}")
                
                # 4. Verify bill data matches PO data
                print(f"\n4. Verifying bill data matches PO...")
                
                # Check basic fields
                verification_results = []
                
                if created_bill['vendor'] == vendor_id:
                    verification_results.append("✅ Vendor ID matches")
                else:
                    verification_results.append(f"❌ Vendor ID mismatch: PO={vendor_id}, Bill={created_bill['vendor']}")
                
                if created_bill['reference_number'] == f"PO-{po_number}":
                    verification_results.append("✅ Reference number matches PO")
                else:
                    verification_results.append(f"❌ Reference mismatch: Expected=PO-{po_number}, Got={created_bill['reference_number']}")
                
                if abs(float(created_bill['total_amount']) - float(created_po['total_amount'])) < 0.01:
                    verification_results.append("✅ Total amount matches")
                else:
                    verification_results.append(f"❌ Total amount mismatch: PO=${created_po['total_amount']}, Bill=${created_bill['total_amount']}")
                
                if len(created_bill['line_items']) == len(created_po['line_items']):
                    verification_results.append("✅ Line item count matches")
                else:
                    verification_results.append(f"❌ Line item count mismatch: PO={len(created_po['line_items'])}, Bill={len(created_bill['line_items'])}")
                
                # Check line items details
                po_items = created_po['line_items']
                bill_items = created_bill['line_items']
                
                for i, (po_item, bill_item) in enumerate(zip(po_items, bill_items)):
                    if po_item['description'] == bill_item['item_description']:
                        verification_results.append(f"✅ Item {i+1} description matches")
                    else:
                        verification_results.append(f"❌ Item {i+1} description mismatch")
                    
                    if abs(float(po_item['quantity']) - float(bill_item['quantity'])) < 0.01:
                        verification_results.append(f"✅ Item {i+1} quantity matches")
                    else:
                        verification_results.append(f"❌ Item {i+1} quantity mismatch")
                    
                    if abs(float(po_item['unit_price']) - float(bill_item['unit_price'])) < 0.01:
                        verification_results.append(f"✅ Item {i+1} unit price matches")
                    else:
                        verification_results.append(f"❌ Item {i+1} unit price mismatch")
                
                print("\n📋 Verification Results:")
                for result in verification_results:
                    print(f"  {result}")
                
                # 5. Test retrieving the created bill
                print(f"\n5. Testing bill retrieval...")
                
                retrieve_response = requests.get(f"{API_BASE}/purchase/vendor-bills/{bill_id}/", headers=HEADERS, timeout=5)
                
                if retrieve_response.status_code == 200:
                    retrieved_bill = retrieve_response.json()
                    print("✅ Bill retrieved successfully")
                    print(f"Retrieved Bill Number: {retrieved_bill['bill_number']}")
                    print(f"Retrieved Total: ${retrieved_bill['total_amount']}")
                    print(f"Retrieved Status: {retrieved_bill['status']}")
                    
                    # Check if notes contain PO reference
                    if po_number in retrieved_bill.get('notes', ''):
                        print("✅ Bill notes contain PO reference")
                    else:
                        print("⚠️ Bill notes don't contain PO reference")
                    
                    return len([r for r in verification_results if r.startswith('❌')]) == 0
                else:
                    print(f"❌ Failed to retrieve bill: {retrieve_response.status_code}")
                    return False
            else:
                print(f"❌ Failed to create bill from PO: {bill_from_po_response.status_code}")
                print(bill_from_po_response.text)
                return False
        else:
            print(f"❌ Failed to create PO: {po_response.status_code}")
            print(po_response.text)
            return False
            
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_po_to_vendor_bill()
    
    print("\n" + "=" * 55)
    print("📋 PO TO VENDOR BILL TEST SUMMARY")
    print("=" * 55)
    
    if success:
        print("🎉 PO TO VENDOR BILL CREATION IS WORKING!")
        print("✅ Purchase Order created successfully")
        print("✅ Vendor bill created from PO")
        print("✅ All data mapping verified")
        print("✅ Line items transferred correctly")
        print("✅ Financial amounts match")
        print("✅ Bill retrieval working")
        print("\n💡 Frontend integration ready!")
        print("🚀 Users can create bills from POs seamlessly!")
    else:
        print("❌ PO to vendor bill creation has issues")
        print("🔧 Check the error details above")
