#!/usr/bin/env python3
"""
Test the exact scenario from frontend with validation errors
"""
import requests
import json

# Configuration
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api"
TOKEN = "e15bc01f831c5111f413f534ef82288744cb7d41"

HEADERS = {
    'Content-Type': 'application/json',
    'Authorization': f'Token {TOKEN}'
}

def test_frontend_scenario():
    print("🚀 TESTING FRONTEND SCENARIO WITH VALIDATION ERRORS")
    print("=" * 60)
    
    # Get vendor
    vendors_response = requests.get(f"{API_BASE}/contacts/vendors/", headers=HEADERS)
    vendor_id = vendors_response.json()['results'][0]['contact']
    print(f"Using vendor ID: {vendor_id}")
    
    # Test data that mimics the frontend error scenario
    bill_data = {
        "vendor": vendor_id,
        "bill_date": "2025-07-06",
        "due_date": "2025-08-05",
        "status": "draft",
        "payment_terms": "Net 30",
        "reference_number": "FRONTEND-ERROR-TEST",
        "notes": "Testing frontend validation errors",
        "line_items": [
            {
                "product": 2,  # This product doesn't exist
                "item_description": "Product that doesn't exist",
                "quantity": 1,
                "unit_price": 100.00,
                "tax_rate": 10.0,
                "account_code": "5010-COGS",
                "line_order": 1
            },
            {},  # Empty line item
            {
                "item_description": "",  # Blank description
                "quantity": 1,
                "unit_price": 50.00,
                "tax_rate": 0.0,
                "account_code": "5020-Services",
                "line_order": 3
            },
            {
                "item_description": "",  # Another blank description
                "quantity": 2,
                "unit_price": 25.00,
                "tax_rate": 5.0,
                "account_code": "5030-Supplies",
                "line_order": 4
            }
        ]
    }
    
    print("Sending problematic data:")
    print(json.dumps(bill_data, indent=2))
    
    # Create vendor bill
    response = requests.post(f"{API_BASE}/purchase/vendor-bills/", json=bill_data, headers=HEADERS)
    
    print(f"\nStatus Code: {response.status_code}")
    
    if response.status_code == 201:
        print("✅ SUCCESS! Vendor bill created successfully!")
        data = response.json()
        print(f"Bill ID: {data['id']}")
        print(f"Bill Number: {data['bill_number']}")
        print(f"Total Amount: {data['total_amount']}")
        print(f"Line Items Created: {len(data['line_items'])}")
        
        # Show line items
        for i, item in enumerate(data['line_items']):
            print(f"  Item {i+1}: {item['item_description']} - ${item['line_total']}")
        
        return True
    else:
        print(f"❌ FAILED! Status: {response.status_code}")
        try:
            error_data = response.json()
            print("Error details:")
            print(json.dumps(error_data, indent=2))
        except:
            print(f"Raw error: {response.text}")
        return False

def test_clean_data():
    print("\n" + "=" * 60)
    print("🧹 TESTING WITH CLEAN DATA")
    print("=" * 60)
    
    # Get vendor
    vendors_response = requests.get(f"{API_BASE}/contacts/vendors/", headers=HEADERS)
    vendor_id = vendors_response.json()['results'][0]['contact']
    
    # Clean test data
    clean_bill_data = {
        "vendor": vendor_id,
        "bill_date": "2025-07-06",
        "due_date": "2025-08-05",
        "status": "draft",
        "payment_terms": "Net 30",
        "reference_number": "CLEAN-TEST",
        "notes": "Testing with clean data",
        "line_items": [
            {
                "item_description": "Valid Product 1",
                "quantity": 2,
                "unit_price": 100.00,
                "tax_rate": 10.0,
                "account_code": "5010-COGS",
                "line_order": 1
            },
            {
                "item_description": "Valid Service 1",
                "quantity": 1,
                "unit_price": 200.00,
                "tax_rate": 10.0,
                "account_code": "5020-Services",
                "line_order": 2
            }
        ]
    }
    
    response = requests.post(f"{API_BASE}/purchase/vendor-bills/", json=clean_bill_data, headers=HEADERS)
    
    if response.status_code == 201:
        print("✅ SUCCESS! Clean data works perfectly!")
        data = response.json()
        print(f"Bill ID: {data['id']}")
        print(f"Total Amount: {data['total_amount']}")
        return True
    else:
        print(f"❌ FAILED! Status: {response.status_code}")
        print(f"Error: {response.text}")
        return False

if __name__ == "__main__":
    # Test problematic scenario first
    success1 = test_frontend_scenario()
    
    # Test clean data
    success2 = test_clean_data()
    
    print("\n" + "=" * 60)
    print("📋 SUMMARY")
    print("=" * 60)
    if success1:
        print("✅ Frontend validation errors are now handled gracefully!")
    if success2:
        print("✅ Clean data works perfectly!")
    
    if success1 and success2:
        print("\n🎉 VENDOR BILL CREATION IS FULLY WORKING!")
        print("💡 Frontend should now work without validation errors!")
    else:
        print("\n⚠️  Some issues remain...")
