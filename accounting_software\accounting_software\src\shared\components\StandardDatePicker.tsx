import React from 'react';
import { DatePicker, DatePickerProps } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { TextField, Box, Typography, Chip } from '@mui/material';
import { CalendarToday as CalendarIcon } from '@mui/icons-material';
import { Dayjs } from 'dayjs';
import dayjs from 'dayjs';

export interface StandardDatePickerProps extends Omit<DatePickerProps<Dayjs>, 'renderInput'> {
  fullWidth?: boolean;
  required?: boolean;
  size?: 'small' | 'medium';
  variant?: 'outlined' | 'filled' | 'standard';
  helperText?: string;
  error?: boolean;
  showFormat?: boolean;
  dateFormat?: 'DD/MM/YYYY' | 'MM/DD/YYYY' | 'YYYY-MM-DD' | 'DD-MM-YYYY';
  businessContext?: 'invoice' | 'payment' | 'receipt' | 'general';
  showQuickActions?: boolean;
}

const StandardDatePicker: React.FC<StandardDatePickerProps> = ({
  fullWidth = true,
  required = false,
  size = 'medium',
  variant = 'outlined',
  helperText,
  error = false,
  label,
  showFormat = true,
  dateFormat = 'DD/MM/YYYY',
  businessContext = 'general',
  showQuickActions = false,
  value,
  onChange,
  ...props
}) => {
  // Format mapping for display
  const formatMap = {
    'DD/MM/YYYY': 'DD/MM/YYYY',
    'MM/DD/YYYY': 'MM/DD/YYYY', 
    'YYYY-MM-DD': 'YYYY-MM-DD',
    'DD-MM-YYYY': 'DD-MM-YYYY'
  };

  // Quick action dates based on business context
  const getQuickActions = () => {
    const today = dayjs();
    
    switch (businessContext) {
      case 'invoice':
        return [
          { label: 'Today', value: today },
          { label: 'Tomorrow', value: today.add(1, 'day') },
          { label: 'End of Month', value: today.endOf('month') },
          { label: '+30 Days', value: today.add(30, 'day') }
        ];
      case 'payment':
        return [
          { label: 'Today', value: today },
          { label: 'Yesterday', value: today.subtract(1, 'day') },
          { label: 'Week Ago', value: today.subtract(1, 'week') },
          { label: 'Month Ago', value: today.subtract(1, 'month') }
        ];
      case 'receipt':
        return [
          { label: 'Today', value: today },
          { label: 'Yesterday', value: today.subtract(1, 'day') },
          { label: 'Week Ago', value: today.subtract(1, 'week') }
        ];
      default:
        return [
          { label: 'Today', value: today },
          { label: 'Yesterday', value: today.subtract(1, 'day') },
          { label: 'Tomorrow', value: today.add(1, 'day') }
        ];
    }
  };

  const quickActions = getQuickActions();

  const handleQuickActionClick = (actionValue: Dayjs) => {
    if (onChange) {
      onChange(actionValue, 'finish');
    }
  };

  // Format the date according to the specified format
  const formatDisplayValue = (date: Dayjs | null) => {
    if (!date) return '';
    
    switch (dateFormat) {
      case 'DD/MM/YYYY':
        return date.format('DD/MM/YYYY');
      case 'MM/DD/YYYY':
        return date.format('MM/DD/YYYY');
      case 'YYYY-MM-DD':
        return date.format('YYYY-MM-DD');
      case 'DD-MM-YYYY':
        return date.format('DD-MM-YYYY');
      default:
        return date.format('DD/MM/YYYY');
    }
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <Box>
        <DatePicker
          label={label}
          value={value}
          onChange={onChange}
          format={formatMap[dateFormat]}
          slotProps={{
            textField: {
              fullWidth,
              required,
              size,
              variant,
              error,
              helperText: showFormat ? 
                `${helperText ? helperText + ' • ' : ''}Format: ${dateFormat}` : 
                helperText,
              InputProps: {
                startAdornment: <CalendarIcon sx={{ mr: 1, color: 'action.active' }} />,
              }
            },
            actionBar: {
              actions: ['clear', 'today', 'cancel', 'accept']
            }
          }}
          {...props}
        />
        
        {/* Quick Actions */}
        {showQuickActions && (
          <Box sx={{ mt: 1, display: 'flex', gap: 0.5, flexWrap: 'wrap' }}>
            <Typography variant="caption" sx={{ mr: 1, alignSelf: 'center', color: 'text.secondary' }}>
              Quick:
            </Typography>
            {quickActions.map((action) => (
              <Chip
                key={action.label}
                label={action.label}
                size="small"
                variant="outlined"
                onClick={() => handleQuickActionClick(action.value)}
                sx={{ 
                  fontSize: '0.75rem',
                  height: 24,
                  cursor: 'pointer',
                  '&:hover': {
                    backgroundColor: 'primary.light',
                    color: 'primary.contrastText'
                  }
                }}
              />
            ))}
          </Box>
        )}

        {/* Current Value Display */}
        {value && showFormat && (
          <Typography variant="caption" sx={{ mt: 0.5, display: 'block', color: 'text.secondary' }}>
            Selected: {formatDisplayValue(value)}
          </Typography>
        )}
      </Box>
    </LocalizationProvider>
  );
};

export default StandardDatePicker;
