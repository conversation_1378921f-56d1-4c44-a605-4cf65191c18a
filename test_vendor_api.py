#!/usr/bin/env python3
"""
Test script to verify vendor API functionality
"""
import requests
import json

# Configuration
BASE_URL = "http://localhost:8000"
API_URL = f"{BASE_URL}/api/contacts/vendors/"

# Test data
test_vendor_data = {
    "displayName": "Test Vendor Company",
    "firstName": "John",
    "lastName": "Doe", 
    "companyName": "Test Vendor Company Ltd",
    "email": "<EMAIL>",
    "phone": "************",
    "mobile": "************",
    "billingAddress": {
        "street": "123 Test Street",
        "city": "Test City",
        "state": "Test State",
        "postalCode": "12345",
        "country": "India"
    },
    "shippingAddress": {
        "sameAsBilling": True,
        "street": "",
        "city": "",
        "state": "",
        "postalCode": "",
        "country": "India"
    },
    "paymentTerms": "1",
    "creditLimit": 10000,
    "vendorCategory": "Technology",
    "leadTimeDays": 7,
    "minimumOrderAmount": 500,
    "preferredVendor": True,
    "notes": "Test vendor for API validation",
    "is_active": True
}

def get_auth_token():
    """Get authentication token"""
    login_url = f"{BASE_URL}/api-token-auth/"
    login_data = {
        "username": "admin",
        "password": "admin123"
    }

    try:
        response = requests.post(login_url, json=login_data)
        if response.status_code == 200:
            return response.json().get('token')
        else:
            print(f"Login failed: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"Login error: {e}")
        return None

def test_vendor_creation():
    """Test vendor creation API"""
    print("Testing Vendor Creation API...")
    
    # Get auth token
    token = get_auth_token()
    if not token:
        print("Failed to get authentication token")
        return False
    
    headers = {
        "Authorization": f"Token {token}",
        "Content-Type": "application/json"
    }
    
    try:
        print(f"Sending POST request to: {API_URL}")
        print(f"Data: {json.dumps(test_vendor_data, indent=2)}")
        
        response = requests.post(API_URL, json=test_vendor_data, headers=headers)
        
        print(f"Response Status: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        print(f"Response Body: {response.text}")
        
        if response.status_code == 201:
            print("✅ Vendor created successfully!")
            return True
        else:
            print(f"❌ Vendor creation failed: {response.status_code}")
            try:
                error_data = response.json()
                print(f"Error details: {json.dumps(error_data, indent=2)}")
            except:
                print(f"Raw error response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Request error: {e}")
        return False

def test_vendor_list():
    """Test vendor list API"""
    print("\nTesting Vendor List API...")
    
    # Get auth token
    token = get_auth_token()
    if not token:
        print("Failed to get authentication token")
        return False
    
    headers = {
        "Authorization": f"Token {token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(API_URL, headers=headers)
        
        print(f"Response Status: {response.status_code}")
        
        if response.status_code == 200:
            vendors = response.json()
            print(f"✅ Found {len(vendors)} vendors")
            if vendors:
                print("First vendor:")
                print(json.dumps(vendors[0], indent=2))
            return True
        else:
            print(f"❌ Vendor list failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Request error: {e}")
        return False

if __name__ == "__main__":
    print("=== Vendor API Test ===")
    
    # Test vendor list first
    list_success = test_vendor_list()
    
    # Test vendor creation
    create_success = test_vendor_creation()
    
    print(f"\n=== Test Results ===")
    print(f"Vendor List: {'✅ PASS' if list_success else '❌ FAIL'}")
    print(f"Vendor Creation: {'✅ PASS' if create_success else '❌ FAIL'}")
