#!/usr/bin/env python3
import requests
import json

# Test the vendor bills endpoint
base_url = "http://localhost:8000"

# Test endpoints
endpoints = [
    "/api/purchase/vendors/",
    "/api/purchase/vendor-bills/",
    "/api/purchase/vendor-bills/stats/",
]

for endpoint in endpoints:
    try:
        print(f"\nTesting: {base_url}{endpoint}")
        response = requests.get(f"{base_url}{endpoint}")
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"Response: {json.dumps(data, indent=2)[:200]}...")
        else:
            print(f"Error: {response.text[:200]}...")
    except Exception as e:
        print(f"Exception: {e}")
