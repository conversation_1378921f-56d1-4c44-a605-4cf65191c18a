import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Card,
  CardContent,
  Button,
  Alert,
  Grid,
  Chip,
  CircularProgress,
  TextField,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  IconButton,
  Tooltip,
  Menu,
  ListItemIcon,
  ListItemText,
  Pagination,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material';
import {
  Assessment as ValuationIcon,
  Add as AddIcon,
  Search as SearchIcon,
  Refresh as RefreshIcon,
  Visibility as ViewIcon,
  Delete as DeleteIcon,
  MoreVert as MoreVertIcon,
  Dashboard as DashboardIcon,
  GetApp as ExportIcon,
  FilterList as FilterIcon,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useSnackbar } from 'notistack';
import { PageContainer, PageHeader } from '../../../layouts/components/PageComponents';
import { formatCurrency, formatDate } from '../../../shared/utils/formatters';
import { inventoryService, StockValuation } from '../services/inventory.service';
import { useCurrencyInfo } from '../../gl/hooks/useCurrencyInfo';

const StockValuationPage: React.FC = () => {
  const [valuations, setValuations] = useState<StockValuation[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [typeFilter, setTypeFilter] = useState('');
  const [warehouseFilter, setWarehouseFilter] = useState('');
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  
  // Menu and dialog states
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedValuation, setSelectedValuation] = useState<StockValuation | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);

  const navigate = useNavigate();
  const { enqueueSnackbar } = useSnackbar();
  const { currencyInfo } = useCurrencyInfo();

  useEffect(() => {
    loadValuations();
  }, [page, searchTerm, statusFilter, typeFilter, warehouseFilter]);

  const loadValuations = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await inventoryService.getStockValuations({
        page,
        search: searchTerm || undefined,
        status: statusFilter || undefined,
        valuation_type: typeFilter || undefined,
        warehouse: warehouseFilter ? parseInt(warehouseFilter) : undefined,
        page_size: 10,
      });
      
      setValuations(response.results);
      setTotalCount(response.count);
      setTotalPages(Math.ceil(response.count / 10));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load valuations');
    } finally {
      setLoading(false);
    }
  };

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleDeleteClick = (valuation: StockValuation) => {
    setSelectedValuation(valuation);
    setDeleteDialogOpen(true);
    handleMenuClose();
  };

  const handleDeleteConfirm = async () => {
    if (!selectedValuation) return;
    
    try {
      setLoading(true);
      await inventoryService.deleteStockValuation(selectedValuation.valuation_id);
      enqueueSnackbar('Valuation deleted successfully', { variant: 'success' });
      loadValuations();
    } catch (err) {
      enqueueSnackbar(err instanceof Error ? err.message : 'Failed to delete valuation', { variant: 'error' });
    } finally {
      setLoading(false);
      setDeleteDialogOpen(false);
      setSelectedValuation(null);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'COMPLETED': return 'success';
      case 'CALCULATING': return 'warning';
      case 'ERROR': return 'error';
      default: return 'default';
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'MONTHLY': return 'Monthly';
      case 'QUARTERLY': return 'Quarterly';
      case 'ANNUAL': return 'Annual';
      case 'AD_HOC': return 'Ad-hoc';
      default: return type;
    }
  };

  return (
    <PageContainer>
      <PageHeader>
        <Box>
          <Typography variant="h5" fontWeight="bold">
            Stock Valuations
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Manage and view inventory valuation reports
          </Typography>
        </Box>
        <Box display="flex" gap={1}>
          <Button
            variant="outlined"
            startIcon={<DashboardIcon />}
            onClick={() => navigate('/dashboard/inventory/valuations/dashboard')}
          >
            Dashboard
          </Button>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => navigate('/dashboard/inventory/valuations/new')}
          >
            Generate Report
          </Button>
          <IconButton onClick={handleMenuClick}>
            <MoreVertIcon />
          </IconButton>
          <Menu
            anchorEl={anchorEl}
            open={Boolean(anchorEl)}
            onClose={handleMenuClose}
          >
            <MenuItem onClick={() => { loadValuations(); handleMenuClose(); }}>
              <ListItemIcon>
                <RefreshIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText>Refresh</ListItemText>
            </MenuItem>
            <MenuItem onClick={handleMenuClose}>
              <ListItemIcon>
                <ExportIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText>Export</ListItemText>
            </MenuItem>
          </Menu>
        </Box>
      </PageHeader>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Summary Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h6" color="primary">
                Total Reports
              </Typography>
              <Typography variant="h4">
                {totalCount}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h6" color="success.main">
                Completed
              </Typography>
              <Typography variant="h4">
                {valuations.filter(v => v.status === 'COMPLETED').length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h6" color="warning.main">
                Calculating
              </Typography>
              <Typography variant="h4">
                {valuations.filter(v => v.status === 'CALCULATING').length}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h6" color="info.main">
                Latest Value
              </Typography>
              <Typography variant="h4">
                {valuations.length > 0 ? 
                  formatCurrency(valuations[0]?.average_total_value || 0, currencyInfo) : 
                  formatCurrency(0, currencyInfo)
                }
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Filters */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} sm={6} md={3}>
              <TextField
                fullWidth
                placeholder="Search valuations..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <FormControl fullWidth>
                <InputLabel>Status</InputLabel>
                <Select
                  value={statusFilter}
                  label="Status"
                  onChange={(e) => setStatusFilter(e.target.value)}
                >
                  <MenuItem value="">All</MenuItem>
                  <MenuItem value="CALCULATING">Calculating</MenuItem>
                  <MenuItem value="COMPLETED">Completed</MenuItem>
                  <MenuItem value="ERROR">Error</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <FormControl fullWidth>
                <InputLabel>Type</InputLabel>
                <Select
                  value={typeFilter}
                  label="Type"
                  onChange={(e) => setTypeFilter(e.target.value)}
                >
                  <MenuItem value="">All</MenuItem>
                  <MenuItem value="MONTHLY">Monthly</MenuItem>
                  <MenuItem value="QUARTERLY">Quarterly</MenuItem>
                  <MenuItem value="ANNUAL">Annual</MenuItem>
                  <MenuItem value="AD_HOC">Ad-hoc</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<RefreshIcon />}
                onClick={loadValuations}
                disabled={loading}
              >
                Refresh
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Valuations Table */}
      <Card>
        <CardContent>
          {loading ? (
            <Box display="flex" justifyContent="center" p={3}>
              <CircularProgress />
            </Box>
          ) : (
            <>
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Date</TableCell>
                      <TableCell>Type</TableCell>
                      <TableCell>Warehouse</TableCell>
                      <TableCell align="right">Items</TableCell>
                      <TableCell align="right">Total Value</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Created By</TableCell>
                      <TableCell align="right">Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {valuations.map((valuation) => (
                      <TableRow
                        key={valuation.valuation_id}
                        hover
                        onClick={() => navigate(`/dashboard/inventory/valuations/${valuation.valuation_id}`)}
                        sx={{ cursor: 'pointer' }}
                      >
                        <TableCell>
                          {formatDate(valuation.valuation_date)}
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={getTypeLabel(valuation.valuation_type)}
                            size="small"
                            variant="outlined"
                          />
                        </TableCell>
                        <TableCell>
                          {valuation.warehouse_name || 'All Warehouses'}
                        </TableCell>
                        <TableCell align="right">
                          {valuation.total_items.toLocaleString()}
                        </TableCell>
                        <TableCell align="right">
                          {formatCurrency(valuation.average_total_value, currencyInfo)}
                        </TableCell>
                        <TableCell>
                          <Chip
                            label={valuation.status}
                            color={getStatusColor(valuation.status) as any}
                            size="small"
                          />
                        </TableCell>
                        <TableCell>
                          {valuation.created_by_name}
                        </TableCell>
                        <TableCell align="right">
                          <Tooltip title="View Details">
                            <IconButton
                              size="small"
                              onClick={(e) => {
                                e.stopPropagation();
                                navigate(`/dashboard/inventory/valuations/${valuation.valuation_id}`);
                              }}
                            >
                              <ViewIcon />
                            </IconButton>
                          </Tooltip>
                          {valuation.status === 'ERROR' && (
                            <Tooltip title="Delete">
                              <IconButton
                                size="small"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleDeleteClick(valuation);
                                }}
                              >
                                <DeleteIcon />
                              </IconButton>
                            </Tooltip>
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>

              {/* Pagination */}
              {totalPages > 1 && (
                <Box display="flex" justifyContent="center" mt={3}>
                  <Pagination
                    count={totalPages}
                    page={page}
                    onChange={(_, newPage) => setPage(newPage)}
                    color="primary"
                  />
                </Box>
              )}

              {valuations.length === 0 && !loading && (
                <Box textAlign="center" py={4}>
                  <ValuationIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
                  <Typography variant="h6" color="text.secondary">
                    No valuations found
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    Generate your first valuation report to get started
                  </Typography>
                  <Button
                    variant="contained"
                    startIcon={<AddIcon />}
                    onClick={() => navigate('/dashboard/inventory/valuations/new')}
                  >
                    Generate Report
                  </Button>
                </Box>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Delete Valuation Report</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete this valuation report? This action cannot be undone.
          </Typography>
          {selectedValuation && (
            <Box mt={2}>
              <Typography variant="body2" color="text.secondary">
                <strong>Date:</strong> {formatDate(selectedValuation.valuation_date)}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                <strong>Type:</strong> {getTypeLabel(selectedValuation.valuation_type)}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                <strong>Warehouse:</strong> {selectedValuation.warehouse_name || 'All Warehouses'}
              </Typography>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={handleDeleteConfirm}
            color="error"
            variant="contained"
            disabled={loading}
          >
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </PageContainer>
  );
};

export default StockValuationPage;
