import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  Button,
  Alert,
  Snackbar,
  Chip,
  CircularProgress,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  CheckCircle as CheckCircleIcon,
  Receipt as ReceiptIcon,
} from '@mui/icons-material';
import dayjs from 'dayjs';

import { vendorBillService } from '../../../services/vendor-bill.service';
import { formatCurrency } from '../../../shared/utils/formatters';

interface GRNItem {
  grn_item_id: number;
  product: number | null;
  product_name: string;
  quantity_received: number;
  unit_cost: number;
  total_cost: number;
  condition: string;
}

interface GRN {
  grn_id: number;
  grn_number: string;
  purchase_order: number;
  purchase_order_number: string;
  vendor_name: string;
  receipt_date: string;
  status: string;
  total_value: number;
  warehouse_name: string;
  items: GRNItem[];
}

interface SnackbarState {
  open: boolean;
  message: string;
  severity: 'success' | 'error' | 'warning' | 'info';
}

const CreateVendorBillFromGRNPage: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const grnId = searchParams.get('grn_id');

  const [availableGRNs, setAvailableGRNs] = useState<GRN[]>([]);
  const [selectedGRN, setSelectedGRN] = useState<GRN | null>(null);
  const [loading, setLoading] = useState(false);
  const [snackbar, setSnackbar] = useState<SnackbarState>({
    open: false,
    message: '',
    severity: 'info',
  });

  // Load available GRNs for selection
  const loadAvailableGRNs = async () => {
    try {
      // Get goods GRNs that are billable (no existing bills)
      const goodsGRNs = await vendorBillService.getBillableGRNs();
      setAvailableGRNs(goodsGRNs);
    } catch (error) {
      console.error('Failed to load billable goods GRNs:', error);
      setSnackbar({
        open: true,
        message: 'Failed to load billable goods GRNs',
        severity: 'error',
      });
    }
  };

  // Handle GRN selection
  const handleSelectGRN = (grn: GRN) => {
    setSelectedGRN(grn);
    // Update URL to include selected GRN ID
    navigate(`/dashboard/purchases/vendor-bills/create-from-grn?grn_id=${grn.grn_id}`, { replace: true });
  };

  // Handle creating vendor bill from selected GRN
  const handleCreateFromGRN = async () => {
    if (!selectedGRN) return;

    setLoading(true);
    try {
      const billData = {
        bill_date: dayjs().format('YYYY-MM-DD'),
        due_date: dayjs().add(30, 'days').format('YYYY-MM-DD'),
        notes: `Bill created from GRN ${selectedGRN.grn_number}`,
      };

      await vendorBillService.createVendorBillFromGRN(selectedGRN.grn_id);

      setSnackbar({
        open: true,
        message: `Vendor bill created successfully from GRN ${selectedGRN.grn_number}`,
        severity: 'success',
      });

      // Navigate to vendor bills page after a short delay
      setTimeout(() => {
        navigate('/dashboard/purchases/vendor-bills');
      }, 2000);

    } catch (error) {
      console.error('Failed to create vendor bill from GRN:', error);
      setSnackbar({
        open: true,
        message: error instanceof Error ? error.message : 'Failed to create vendor bill from GRN',
        severity: 'error',
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle back navigation
  const handleBack = () => {
    if (selectedGRN) {
      setSelectedGRN(null);
      navigate('/dashboard/purchases/vendor-bills/create-from-grn', { replace: true });
    } else {
      navigate('/dashboard/purchases/vendor-bills');
    }
  };

  // Handle snackbar close
  const handleCloseSnackbar = () => {
    setSnackbar(prev => ({ ...prev, open: false }));
  };

  // Load data on component mount
  useEffect(() => {
    loadAvailableGRNs();
  }, []);

  // Auto-select GRN if ID is provided in URL
  useEffect(() => {
    if (grnId && availableGRNs.length > 0) {
      const grn = availableGRNs.find(g => g.grn_id === parseInt(grnId));
      if (grn) {
        setSelectedGRN(grn);
      }
    }
  }, [grnId, availableGRNs]);

  // Show GRN selection view
  if (!selectedGRN) {
    return (
      <Box sx={{ p: 3 }}>
        {/* Header */}
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <IconButton onClick={handleBack} sx={{ mr: 2 }}>
            <ArrowBackIcon />
          </IconButton>
          <Box>
            <Typography variant="h4" component="h1" sx={{ fontWeight: 600, color: '#1a1a1a' }}>
              Select Goods Receipt Note
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
              Choose a goods GRN to create vendor bill from. Only unbilled goods GRNs are shown.
            </Typography>
          </Box>
        </Box>

        {/* GRN Selection Grid */}
        <Box sx={{ flexGrow: 1 }}>
          <Grid container spacing={3}>
            {availableGRNs.length === 0 ? (
              <Grid item xs={12}>
                <Box sx={{ textAlign: 'center', py: 8 }}>
                  <ReceiptIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
                  <Typography variant="h6" color="text.secondary" sx={{ mb: 2 }}>
                    No Goods Receipt Notes Available
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                    There are no goods GRNs available for bill creation. Only goods GRNs that haven't been billed yet will appear here.
                  </Typography>
                  <Button
                    variant="contained"
                    onClick={() => navigate('/dashboard/inventory/grns')}
                    sx={{ borderRadius: '8px' }}
                  >
                    View Goods Receipt Notes
                  </Button>
                </Box>
              </Grid>
            ) : (
              availableGRNs.map((grn) => (
                <Grid item xs={12} sm={6} md={4} key={grn.grn_id}>
                  <Card 
                    sx={{ 
                      cursor: 'pointer',
                      transition: 'all 0.2s',
                      '&:hover': {
                        transform: 'translateY(-2px)',
                        boxShadow: 3,
                      },
                    }}
                    onClick={() => handleSelectGRN(grn)}
                  >
                    <CardContent>
                      <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                        {grn.grn_number}
                      </Typography>
                      
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                        <strong>PO:</strong> {grn.purchase_order_number}
                      </Typography>
                      
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                        <strong>Vendor:</strong> {grn.vendor_name}
                      </Typography>
                      
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                        <strong>Date:</strong> {dayjs(grn.receipt_date).format('DD/MM/YYYY')}
                      </Typography>
                      
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                        <strong>Total:</strong> {formatCurrency(grn.total_value, '$')}
                      </Typography>
                      
                      <Button
                        variant="contained"
                        fullWidth
                        size="small"
                        sx={{ borderRadius: '6px' }}
                      >
                        Create Bill from this GRN
                      </Button>
                    </CardContent>
                  </Card>
                </Grid>
              ))
            )}
          </Grid>
        </Box>

        {/* Snackbar */}
        <Snackbar
          open={snackbar.open}
          autoHideDuration={6000}
          onClose={handleCloseSnackbar}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        >
          <Alert onClose={handleCloseSnackbar} severity={snackbar.severity} sx={{ width: '100%' }}>
            {snackbar.message}
          </Alert>
        </Snackbar>
      </Box>
    );
  }

  // Show selected GRN and bill creation view
  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <IconButton onClick={handleBack} sx={{ mr: 2 }}>
          <ArrowBackIcon />
        </IconButton>
        <Box>
          <Typography variant="h4" component="h1" sx={{ fontWeight: 600, color: '#1a1a1a' }}>
            Create Vendor Bill from Goods Receipt Note
          </Typography>
        </Box>
      </Box>

      <Grid container spacing={3}>
        {/* Selected GRN Details */}
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
                Selected GRN Details
              </Typography>
              
              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <Typography variant="body2" color="text.secondary">
                    <strong>GRN Number:</strong> {selectedGRN.grn_number}
                  </Typography>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="body2" color="text.secondary">
                    <strong>PO Number:</strong> {selectedGRN.purchase_order_number}
                  </Typography>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="body2" color="text.secondary">
                    <strong>Vendor:</strong> {selectedGRN.vendor_name}
                  </Typography>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="body2" color="text.secondary">
                    <strong>Date:</strong> {dayjs(selectedGRN.receipt_date).format('DD/MM/YYYY')}
                  </Typography>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Typography variant="body2" color="text.secondary">
                    <strong>Total:</strong> {formatCurrency(selectedGRN.total_value, '$')}
                  </Typography>
                </Grid>
                <Grid item xs={12} md={6}>
                  <Chip
                    label={selectedGRN.status}
                    size="small"
                    color={selectedGRN.status === 'POSTED' ? 'success' : 'primary'}
                    sx={{ textTransform: 'capitalize' }}
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Action Card */}
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
                Create Vendor Bill
              </Typography>
              
              <Alert severity="info" sx={{ mb: 3 }}>
                <Typography variant="body2">
                  Click "Create Bill from GRN" to automatically create a vendor bill with all GRN details including received quantities and costs.
                </Typography>
              </Alert>

              <Button
                variant="contained"
                startIcon={loading ? <CircularProgress size={20} color="inherit" /> : <CheckCircleIcon />}
                onClick={handleCreateFromGRN}
                disabled={loading}
                fullWidth
                size="large"
                sx={{
                  bgcolor: 'success.main',
                  '&:hover': { bgcolor: 'success.dark' },
                  borderRadius: '8px',
                }}
              >
                {loading ? 'Creating Bill...' : 'Create Bill from GRN'}
              </Button>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity} sx={{ width: '100%' }}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default CreateVendorBillFromGRNPage;
