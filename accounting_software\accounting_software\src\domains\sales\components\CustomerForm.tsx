import React, { useState } from 'react';
import {
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Grid,
  Typography,
  Box,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Checkbox,
  CircularProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Divider,
  Autocomplete,
  IconButton,
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Person as PersonIcon,
  Business as BusinessIcon,
  LocationOn as LocationIcon,
  Payment as PaymentIcon,
  Settings as SettingsIcon,
  Close as CloseIcon,
} from '@mui/icons-material';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import ContactsService from '../../contacts/services/contacts.service';
import { CustomerFormData } from '../../contacts/types/contacts.types';
import { usePaymentTerms } from '../../../contexts/PaymentTermsContext';
import ConfirmationDialog from '../../../shared/components/ConfirmationDialog';
import { useConfirmation } from '../../../shared/hooks/useConfirmation';

interface CustomerFormProps {
  onClose: () => void;
  onSave: () => void;
  initialValues?: any;
}

const validationSchema = Yup.object({
  displayName: Yup.string().required('Display name is required'),
  email: Yup.string().email('Invalid email format'),
  phone: Yup.string(),
  billingAddress: Yup.object({
    country: Yup.string().required('Country is required'),
  }),
  paymentTerms: Yup.string().required('Payment terms are required'),
  creditLimit: Yup.number().min(0, 'Credit limit must be positive'),
});

const CustomerForm: React.FC<CustomerFormProps> = ({ onClose, onSave, initialValues }) => {
  const [loading, setLoading] = useState(false);
  const [expanded, setExpanded] = useState<string>('panel1');
  const { paymentTerms } = usePaymentTerms();
  const { confirmation, showSuccess, showError, hideConfirmation } = useConfirmation();

  const isEditMode = Boolean(initialValues?.id);

  const formik = useFormik<CustomerFormData>({
    initialValues: {
      firstName: initialValues?.firstName || '',
      lastName: initialValues?.lastName || '',
      displayName: initialValues?.displayName || '',
      companyName: initialValues?.companyName || '',
      email: initialValues?.email || '',
      phone: initialValues?.phone || '',
      mobile: initialValues?.mobile || '',
      billingAddress: {
        street: initialValues?.billingAddress?.street || '',
        city: initialValues?.billingAddress?.city || '',
        state: initialValues?.billingAddress?.state || '',
        postalCode: initialValues?.billingAddress?.postalCode || '',
        country: initialValues?.billingAddress?.country || 'India',
      },
      shippingAddress: {
        sameAsBilling: initialValues?.shippingAddress?.sameAsBilling ?? true,
        street: initialValues?.shippingAddress?.street || '',
        city: initialValues?.shippingAddress?.city || '',
        state: initialValues?.shippingAddress?.state || '',
        postalCode: initialValues?.shippingAddress?.postalCode || '',
        country: initialValues?.shippingAddress?.country || 'India',
      },
      paymentTerms: initialValues?.paymentTerms || '1',
      creditLimit: initialValues?.creditLimit || 0,
      customerCategory: initialValues?.customerCategory || '',
      discountPercentage: initialValues?.discountPercentage || 0,
      taxExempt: initialValues?.taxExempt || false,
      notes: initialValues?.notes || '',
      is_active: initialValues?.is_active ?? true,
    },
    validationSchema,
    onSubmit: async (values) => {
      try {
        setLoading(true);

        const customerData = {
          ...values,
          is_active: true
        };
        
        if (isEditMode) {
          await ContactsService.updateCustomer(initialValues.id, customerData);
          showSuccess(
            'Customer Updated!',
            `Customer "${customerData.displayName}" has been updated successfully.`
          );
        } else {
          await ContactsService.createCustomer(customerData);
          showSuccess(
            'Customer Created!',
            `Customer "${customerData.displayName}" has been created successfully.`
          );
        }

        // Close after a short delay to show the success message
        setTimeout(() => {
          onSave();
          onClose();
        }, 1500);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to save customer';
        showError(
          'Save Failed',
          errorMessage
        );
      } finally {
        setLoading(false);
      }
    },
  });

  const handleAccordionChange = (panel: string) => (
    event: React.SyntheticEvent,
    isExpanded: boolean
  ) => {
    setExpanded(isExpanded ? panel : '');
  };

  const handleSameAsBillingChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const sameAsBilling = event.target.checked;
    formik.setFieldValue('shippingAddress.sameAsBilling', sameAsBilling);
    
    if (sameAsBilling) {
      formik.setFieldValue('shippingAddress.street', formik.values.billingAddress.street);
      formik.setFieldValue('shippingAddress.city', formik.values.billingAddress.city);
      formik.setFieldValue('shippingAddress.state', formik.values.billingAddress.state);
      formik.setFieldValue('shippingAddress.postalCode', formik.values.billingAddress.postalCode);
      formik.setFieldValue('shippingAddress.country', formik.values.billingAddress.country);
    }
  };

  return (
    <>
      <DialogTitle sx={{ borderBottom: '1px solid rgba(0, 0, 0, 0.12)', pb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        {isEditMode ? 'Edit Customer' : 'New Customer'}
        <IconButton onClick={onClose} disabled={loading}>
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <form onSubmit={formik.handleSubmit}>
        <DialogContent dividers sx={{ p: 0 }}>

          {/* Basic Information */}
          <Accordion
            expanded={expanded === 'panel1'}
            onChange={handleAccordionChange('panel1')}
          >
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Box display="flex" alignItems="center" gap={1}>
                <PersonIcon />
                <Typography variant="subtitle1" fontWeight="bold">
                  Basic Information
                </Typography>
              </Box>
            </AccordionSummary>
            <AccordionDetails>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    id="displayName"
                    name="displayName"
                    label="Display Name *"
                    value={formik.values.displayName}
                    onChange={formik.handleChange}
                    error={formik.touched.displayName && Boolean(formik.errors.displayName)}
                    helperText={formik.touched.displayName && formik.errors.displayName}
                  />
                </Grid>
                
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="firstName"
                    name="firstName"
                    label="First Name"
                    value={formik.values.firstName}
                    onChange={formik.handleChange}
                  />
                </Grid>
                
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="lastName"
                    name="lastName"
                    label="Last Name"
                    value={formik.values.lastName}
                    onChange={formik.handleChange}
                  />
                </Grid>
                
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    id="companyName"
                    name="companyName"
                    label="Company Name"
                    value={formik.values.companyName}
                    onChange={formik.handleChange}
                  />
                </Grid>
                
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="email"
                    name="email"
                    label="Email"
                    type="email"
                    value={formik.values.email}
                    onChange={formik.handleChange}
                    error={formik.touched.email && Boolean(formik.errors.email)}
                    helperText={formik.touched.email && formik.errors.email}
                  />
                </Grid>
                
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="phone"
                    name="phone"
                    label="Phone"
                    value={formik.values.phone}
                    onChange={formik.handleChange}
                  />
                </Grid>
                
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="mobile"
                    name="mobile"
                    label="Mobile"
                    value={formik.values.mobile}
                    onChange={formik.handleChange}
                  />
                </Grid>
                
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="customerCategory"
                    name="customerCategory"
                    label="Customer Category"
                    value={formik.values.customerCategory}
                    onChange={formik.handleChange}
                    placeholder="e.g., Retail, Wholesale, VIP"
                  />
                </Grid>
              </Grid>
            </AccordionDetails>
          </Accordion>

          {/* Billing Address */}
          <Accordion
            expanded={expanded === 'panel2'}
            onChange={handleAccordionChange('panel2')}
          >
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Box display="flex" alignItems="center" gap={1}>
                <LocationIcon />
                <Typography variant="subtitle1" fontWeight="bold">
                  Billing Address
                </Typography>
              </Box>
            </AccordionSummary>
            <AccordionDetails>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    id="billingAddress.street"
                    name="billingAddress.street"
                    label="Street Address"
                    value={formik.values.billingAddress.street}
                    onChange={formik.handleChange}
                  />
                </Grid>
                
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="billingAddress.city"
                    name="billingAddress.city"
                    label="City"
                    value={formik.values.billingAddress.city}
                    onChange={formik.handleChange}
                  />
                </Grid>
                
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="billingAddress.state"
                    name="billingAddress.state"
                    label="State/Province"
                    value={formik.values.billingAddress.state}
                    onChange={formik.handleChange}
                  />
                </Grid>
                
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="billingAddress.postalCode"
                    name="billingAddress.postalCode"
                    label="Postal Code"
                    value={formik.values.billingAddress.postalCode}
                    onChange={formik.handleChange}
                  />
                </Grid>
                
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth>
                    <InputLabel>Country *</InputLabel>
                    <Select
                      id="billingAddress.country"
                      name="billingAddress.country"
                      value={formik.values.billingAddress.country}
                      onChange={formik.handleChange}
                      label="Country *"
                    >
                      <MenuItem value="India">India</MenuItem>
                      <MenuItem value="United States">United States</MenuItem>
                      <MenuItem value="United Kingdom">United Kingdom</MenuItem>
                      <MenuItem value="Canada">Canada</MenuItem>
                      <MenuItem value="Australia">Australia</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
              </Grid>
            </AccordionDetails>
          </Accordion>

          {/* Shipping Address */}
          <Accordion
            expanded={expanded === 'panel3'}
            onChange={handleAccordionChange('panel3')}
          >
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Box display="flex" alignItems="center" gap={1}>
                <LocationIcon />
                <Typography variant="subtitle1" fontWeight="bold">
                  Shipping Address
                </Typography>
              </Box>
            </AccordionSummary>
            <AccordionDetails>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Checkbox
                        checked={formik.values.shippingAddress.sameAsBilling}
                        onChange={handleSameAsBillingChange}
                      />
                    }
                    label="Same as billing address"
                  />
                </Grid>
                
                {!formik.values.shippingAddress.sameAsBilling && (
                  <>
                    <Grid item xs={12}>
                      <TextField
                        fullWidth
                        id="shippingAddress.street"
                        name="shippingAddress.street"
                        label="Street Address"
                        value={formik.values.shippingAddress.street}
                        onChange={formik.handleChange}
                      />
                    </Grid>
                    
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        id="shippingAddress.city"
                        name="shippingAddress.city"
                        label="City"
                        value={formik.values.shippingAddress.city}
                        onChange={formik.handleChange}
                      />
                    </Grid>
                    
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        id="shippingAddress.state"
                        name="shippingAddress.state"
                        label="State/Province"
                        value={formik.values.shippingAddress.state}
                        onChange={formik.handleChange}
                      />
                    </Grid>
                    
                    <Grid item xs={12} sm={6}>
                      <TextField
                        fullWidth
                        id="shippingAddress.postalCode"
                        name="shippingAddress.postalCode"
                        label="Postal Code"
                        value={formik.values.shippingAddress.postalCode}
                        onChange={formik.handleChange}
                      />
                    </Grid>
                    
                    <Grid item xs={12} sm={6}>
                      <FormControl fullWidth>
                        <InputLabel>Country</InputLabel>
                        <Select
                          id="shippingAddress.country"
                          name="shippingAddress.country"
                          value={formik.values.shippingAddress.country}
                          onChange={formik.handleChange}
                          label="Country"
                        >
                          <MenuItem value="India">India</MenuItem>
                          <MenuItem value="United States">United States</MenuItem>
                          <MenuItem value="United Kingdom">United Kingdom</MenuItem>
                          <MenuItem value="Canada">Canada</MenuItem>
                          <MenuItem value="Australia">Australia</MenuItem>
                        </Select>
                      </FormControl>
                    </Grid>
                  </>
                )}
              </Grid>
            </AccordionDetails>
          </Accordion>

          {/* Financial Information */}
          <Accordion
            expanded={expanded === 'panel4'}
            onChange={handleAccordionChange('panel4')}
          >
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Box display="flex" alignItems="center" gap={1}>
                <PaymentIcon />
                <Typography variant="subtitle1" fontWeight="bold">
                  Financial Information
                </Typography>
              </Box>
            </AccordionSummary>
            <AccordionDetails>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <Autocomplete
                    options={paymentTerms}
                    getOptionLabel={(option) => option.name}
                    value={paymentTerms.find(term => term.id === parseInt(formik.values.paymentTerms || '1')) || null}
                    onChange={(event, newValue) => {
                      formik.setFieldValue('paymentTerms', newValue ? newValue.id.toString() : '1');
                    }}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        label="Payment Terms"
                        error={formik.touched.paymentTerms && Boolean(formik.errors.paymentTerms)}
                        helperText={formik.touched.paymentTerms && formik.errors.paymentTerms}
                      />
                    )}
                  />
                </Grid>
                
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="creditLimit"
                    name="creditLimit"
                    label="Credit Limit"
                    type="number"
                    value={formik.values.creditLimit}
                    onChange={formik.handleChange}
                    InputProps={{ inputProps: { min: 0 } }}
                  />
                </Grid>
                
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="discountPercentage"
                    name="discountPercentage"
                    label="Discount Percentage"
                    type="number"
                    value={formik.values.discountPercentage}
                    onChange={formik.handleChange}
                    InputProps={{ inputProps: { min: 0, max: 100 } }}
                  />
                </Grid>
                
                <Grid item xs={12} sm={6}>
                  <FormControlLabel
                    control={
                      <Checkbox
                        id="taxExempt"
                        name="taxExempt"
                        checked={formik.values.taxExempt}
                        onChange={formik.handleChange}
                      />
                    }
                    label="Tax Exempt"
                  />
                </Grid>
              </Grid>
            </AccordionDetails>
          </Accordion>

          {/* Additional Information */}
          <Accordion
            expanded={expanded === 'panel5'}
            onChange={handleAccordionChange('panel5')}
          >
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Box display="flex" alignItems="center" gap={1}>
                <SettingsIcon />
                <Typography variant="subtitle1" fontWeight="bold">
                  Additional Information
                </Typography>
              </Box>
            </AccordionSummary>
            <AccordionDetails>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    id="notes"
                    name="notes"
                    label="Notes"
                    multiline
                    rows={3}
                    value={formik.values.notes}
                    onChange={formik.handleChange}
                    placeholder="Additional notes about this customer..."
                  />
                </Grid>
              </Grid>
            </AccordionDetails>
          </Accordion>
        </DialogContent>

        <DialogActions sx={{ px: 3, py: 2 }}>
          <Button onClick={onClose} disabled={loading}>
            Cancel
          </Button>
          <Button
            type="submit"
            variant="contained"
            disabled={loading}
            startIcon={loading ? <CircularProgress size={20} /> : null}
          >
            {loading ? 'Saving...' : isEditMode ? 'Update Customer' : 'Create Customer'}
          </Button>
        </DialogActions>
      </form>

      {/* Confirmation Dialog */}
      <ConfirmationDialog
        open={confirmation.open}
        onClose={hideConfirmation}
        onConfirm={confirmation.onConfirm}
        title={confirmation.title}
        message={confirmation.message}
        type={confirmation.type}
        confirmText={confirmation.confirmText}
        cancelText={confirmation.cancelText}
        showCancel={confirmation.showCancel}
        confirmColor={confirmation.confirmColor}
        autoClose={confirmation.autoClose}
        autoCloseDelay={confirmation.autoCloseDelay}
      />
    </>
  );
};

export default CustomerForm; 