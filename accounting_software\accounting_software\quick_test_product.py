#!/usr/bin/env python3
import requests

BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api"
TOKEN = "e15bc01f831c5111f413f534ef82288744cb7d41"

HEADERS = {
    'Content-Type': 'application/json',
    'Authorization': f'Token {TOKEN}'
}

# Test with product
try:
    vendors_response = requests.get(f"{API_BASE}/contacts/vendors/", headers=HEADERS, timeout=3)
    vendor_id = vendors_response.json()['results'][0]['contact']
    
    products_response = requests.get(f"{API_BASE}/sales/products/", headers=HEADERS, timeout=3)
    products = products_response.json()['results']
    product_id = products[0]['id'] if products else None
    
    bill_data = {
        "vendor": vendor_id,
        "bill_date": "2025-07-06",
        "due_date": "2025-08-05",
        "status": "draft",
        "payment_terms": "Net 30",
        "reference_number": "PRODUCT-TEST",
        "notes": "Product test",
        "line_items": [
            {
                "product": product_id,
                "item_description": "Test product item",
                "quantity": 1,
                "unit_price": 100.00,
                "tax_rate": 10.0,
                "account_code": "5010-COGS",
                "line_order": 1
            }
        ]
    }
    
    response = requests.post(f"{API_BASE}/purchase/vendor-bills/", json=bill_data, headers=HEADERS, timeout=5)
    print(f"Status: {response.status_code}")
    if response.status_code == 201:
        print("✅ SUCCESS - Bills with products working!")
        bill = response.json()
        print(f"Product in response: {bill['line_items'][0].get('product', 'None')}")
    else:
        print(f"❌ ERROR: {response.text}")
        
except Exception as e:
    print(f"❌ Exception: {e}")
