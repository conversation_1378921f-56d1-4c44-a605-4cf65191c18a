#!/usr/bin/env python3
"""
Debug script for PO to Vendor Bill creation issue
"""

import requests
import json

# Configuration
API_BASE = "http://localhost:8000/api"
HEADERS = {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
}

def test_simple_po_to_bill():
    """Test creating a vendor bill from PO with minimal data"""
    
    print("🔍 DEBUGGING PO TO VENDOR BILL CREATION")
    print("=" * 50)
    
    try:
        # 1. Get the latest PO
        print("1. Getting latest PO...")
        po_response = requests.get(f"{API_BASE}/purchase/purchase-orders/", headers=HEADERS, timeout=10)
        
        if po_response.status_code == 200:
            pos = po_response.json()
            if pos and len(pos) > 0:
                latest_po = pos[0]  # Get the first PO
                po_id = latest_po['id']
                po_number = latest_po['po_number']
                
                print(f"✅ Found PO: {po_number} (ID: {po_id})")
                print(f"PO Status: {latest_po['status']}")
                print(f"PO Vendor: {latest_po['vendor_name']}")
                print(f"PO Total: ${latest_po['total_amount']}")
                
                # 2. Try to create vendor bill from this PO
                print(f"\n2. Creating vendor bill from PO {po_number}...")
                
                bill_data = {"po_id": po_id}
                print(f"Request data: {bill_data}")
                
                bill_response = requests.post(
                    f"{API_BASE}/purchase/vendor-bills/create_from_po/", 
                    json=bill_data, 
                    headers=HEADERS, 
                    timeout=10
                )
                
                print(f"Response status: {bill_response.status_code}")
                print(f"Response headers: {dict(bill_response.headers)}")
                
                if bill_response.status_code == 201:
                    created_bill = bill_response.json()
                    print(f"✅ Vendor bill created successfully!")
                    print(f"Bill Number: {created_bill.get('bill_number', 'N/A')}")
                    print(f"Bill ID: {created_bill.get('id', 'N/A')}")
                    print(f"Bill Total: ${created_bill.get('total_amount', 'N/A')}")
                else:
                    print(f"❌ Failed to create vendor bill")
                    print(f"Response text: {bill_response.text}")
                    
                    try:
                        error_data = bill_response.json()
                        print(f"Error details: {json.dumps(error_data, indent=2)}")
                    except:
                        print("Could not parse error response as JSON")
                        
            else:
                print("❌ No POs found")
        else:
            print(f"❌ Failed to get POs: {po_response.status_code}")
            print(f"Response: {po_response.text}")
            
    except Exception as e:
        print(f"❌ Exception occurred: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_simple_po_to_bill()
