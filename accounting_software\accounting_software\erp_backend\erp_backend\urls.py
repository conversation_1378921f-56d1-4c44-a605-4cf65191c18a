"""
URL configuration for erp_backend project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from rest_framework.authtoken.views import obtain_auth_token
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator

# Create CSRF-exempt authentication view
csrf_exempt_obtain_auth_token = csrf_exempt(obtain_auth_token)

urlpatterns = [
    path('admin/', admin.site.urls),
    path('api/contacts/', include('contacts.urls')),  # Centralized contacts app URLs
    path('api/hr/', include('hr.urls')),  # HR app URLs
    path('api/account/', include('account.urls')), # Account app URLs
    path('api/gl/', include('gl.urls')), # General Ledger app URLs
    path('api/cms/', include('cms.urls')), # Cash Management System app URLs
    path('api/sales/', include('sales.urls')), # Sales app URLs
    path('api/purchase/', include('purchase.urls')),
    path('api/inventory/', include('inventory.urls')), # Inventory app URLs
    path('api/pricing/', include('Pricing.urls')), # Pricing app URLs
    # path('api/audit/', include('audit.urls')), # Audit trail app URLs - temporarily disabled
    path('api-auth/', include('rest_framework.urls')),  # REST framework browsable API auth
    path('api-token-auth/', csrf_exempt_obtain_auth_token, name='api_token_auth'),  # Token authentication
    path('api/', include('sales_tax.urls')),
    path('api/', include('tds.urls')),  # TDS app URLs
]
