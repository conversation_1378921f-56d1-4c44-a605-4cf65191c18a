#!/usr/bin/env python3
"""
Test script to verify the vendor service fix by testing with proper authentication
"""
import requests
import json

# Test data that matches the frontend form structure
test_vendor_data = {
    "displayName": "Test Vendor Company",
    "firstName": "<PERSON>",
    "lastName": "Doe", 
    "companyName": "Test Vendor Company Ltd",
    "email": "<EMAIL>",
    "phone": "************",
    "mobile": "************",
    "billingAddress": {
        "street": "123 Test Street",
        "city": "Test City",
        "state": "Test State",
        "postalCode": "12345",
        "country": "India"
    },
    "shippingAddress": {
        "sameAsBilling": True,
        "street": "",
        "city": "",
        "state": "",
        "postalCode": "",
        "country": "India"
    },
    "paymentTerms": "1",
    "creditLimit": 10000,
    "vendorCategory": "Technology",
    "leadTimeDays": 7,
    "minimumOrderAmount": 500,
    "preferredVendor": True,
    "notes": "Test vendor for API validation",
    "is_active": True
}

def get_auth_token():
    """Get authentication token using Django's token auth"""
    url = "http://localhost:8000/api-token-auth/"
    data = {
        "username": "admin",
        "password": "admin123"
    }
    
    try:
        response = requests.post(url, json=data)
        if response.status_code == 200:
            return response.json().get('token')
        else:
            print(f"Auth failed: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"Auth error: {e}")
        return None

def test_vendor_creation_with_auth():
    """Test vendor creation with proper authentication and correct data format"""
    print("=== Testing Vendor Creation with Authentication ===")
    
    # Get auth token
    token = get_auth_token()
    if not token:
        print("❌ Failed to get authentication token")
        return False
    
    print(f"✅ Got auth token: {token[:10]}...")
    
    url = "http://localhost:8000/api/contacts/vendors/"
    headers = {
        "Authorization": f"Token {token}",
        "Content-Type": "application/json"
    }
    
    try:
        print(f"Sending POST request to: {url}")
        print(f"Data structure: {json.dumps(test_vendor_data, indent=2)}")
        
        response = requests.post(url, json=test_vendor_data, headers=headers)
        
        print(f"Response Status: {response.status_code}")
        print(f"Response Body: {response.text}")
        
        if response.status_code == 201:
            print("✅ Vendor created successfully!")
            created_vendor = response.json()
            print(f"Created vendor ID: {created_vendor.get('id', 'N/A')}")
            return True
        elif response.status_code == 400:
            print("❌ Still getting 400 Bad Request")
            try:
                error_data = response.json()
                print(f"Error details: {json.dumps(error_data, indent=2)}")
                
                # Check if it's still a companyName error
                if 'companyName' in str(error_data):
                    print("❌ companyName validation error still exists")
                else:
                    print("✅ No companyName error - different validation issue")
            except:
                print(f"Raw error response: {response.text}")
            return False
        else:
            print(f"❌ Unexpected status code: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Request error: {e}")
        return False

def test_vendor_list_with_auth():
    """Test vendor list with authentication"""
    print("\n=== Testing Vendor List with Authentication ===")
    
    # Get auth token
    token = get_auth_token()
    if not token:
        print("❌ Failed to get authentication token")
        return False
    
    url = "http://localhost:8000/api/contacts/vendors/"
    headers = {
        "Authorization": f"Token {token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(url, headers=headers)
        
        print(f"Response Status: {response.status_code}")
        
        if response.status_code == 200:
            vendors = response.json()
            if isinstance(vendors, list):
                print(f"✅ Found {len(vendors)} vendors")
            elif isinstance(vendors, dict) and 'results' in vendors:
                print(f"✅ Found {len(vendors['results'])} vendors (paginated)")
            else:
                print("✅ Got vendor list response")
            return True
        else:
            print(f"❌ Vendor list failed: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Request error: {e}")
        return False

def main():
    print("=== Vendor Service Fix Test ===")
    print("Testing if the frontend service fix resolves the 400 Bad Request error...")
    
    # Test vendor list first
    list_success = test_vendor_list_with_auth()
    
    # Test vendor creation
    create_success = test_vendor_creation_with_auth()
    
    print(f"\n=== Test Results ===")
    print(f"Vendor List: {'✅ PASS' if list_success else '❌ FAIL'}")
    print(f"Vendor Creation: {'✅ PASS' if create_success else '❌ FAIL'}")
    
    if list_success and create_success:
        print("\n🎉 The vendor service fix is working!")
        print("Frontend should now be able to create vendors without 400 errors.")
    else:
        print("\n❌ There are still issues that need to be resolved.")

if __name__ == "__main__":
    main()
