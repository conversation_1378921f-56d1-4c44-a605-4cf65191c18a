#!/usr/bin/env python
import os
import sys
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'erp_backend.settings')
django.setup()

from django.contrib.auth.models import User
from rest_framework.authtoken.models import Token

try:
    admin = User.objects.get(username='admin')
    token, created = Token.objects.get_or_create(user=admin)
    print(f'Admin token: {token.key}')
except User.DoesNotExist:
    print('Admin user not found')
