import React, { useState } from 'react';
import {
  Box,
  Typography,
  FormControlLabel,
  Switch,
  Grid,
  TextField,
  MenuItem,
  Autocomplete,
  FormHelperText,
  Collapse,
  Paper,
  Divider,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  Tooltip,
  IconButton,
} from '@mui/material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { Info as InfoIcon } from '@mui/icons-material';
import dayjs from 'dayjs';
import { StandardDatePicker } from '../../../shared/components';
import { RecurringInvoiceSettings, RecurringFrequency, RECURRING_FREQUENCIES } from '../../../shared/types/invoice.types';

interface RecurringInvoiceFormProps {
  value: RecurringInvoiceSettings | undefined;
  onChange: (settings: RecurringInvoiceSettings | undefined) => void;
}

const defaultSettings: RecurringInvoiceSettings = {
  isRecurring: false,
  frequency: 'monthly',
  interval: 1,
  startDate: dayjs().format('YYYY-MM-DD'),
  sendAutomatically: false,
};

const RecurringInvoiceForm: React.FC<RecurringInvoiceFormProps> = ({ value, onChange }) => {
  const [settings, setSettings] = useState<RecurringInvoiceSettings>(value || defaultSettings);

  const handleChange = (field: keyof RecurringInvoiceSettings, newValue: any) => {
    const updatedSettings = { ...settings, [field]: newValue };
    setSettings(updatedSettings);
    onChange(updatedSettings.isRecurring ? updatedSettings : undefined);
  };

  const getDaysOfWeek = () => [
    { value: 0, label: 'Sunday' },
    { value: 1, label: 'Monday' },
    { value: 2, label: 'Tuesday' },
    { value: 3, label: 'Wednesday' },
    { value: 4, label: 'Thursday' },
    { value: 5, label: 'Friday' },
    { value: 6, label: 'Saturday' },
  ];

  const getDaysOfMonth = () => {
    const days = [];
    for (let i = 1; i <= 31; i++) {
      days.push({ value: i, label: i.toString() });
    }
    return days;
  };

  return (
    <Paper variant="outlined" sx={{ p: 3, mt: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">Recurring Invoice</Typography>
        <FormControlLabel
          control={
            <Switch
              checked={settings.isRecurring}
              onChange={(e) => handleChange('isRecurring', e.target.checked)}
              color="primary"
            />
          }
          label="Make this a recurring invoice"
        />
      </Box>
      
      <Collapse in={settings.isRecurring}>
        <Divider sx={{ mb: 3 }} />
        
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <FormControl fullWidth>
              <InputLabel id="frequency-label">Frequency</InputLabel>
              <Select
                labelId="frequency-label"
                value={settings.frequency}
                label="Frequency"
                onChange={(e) => handleChange('frequency', e.target.value)}
              >
                {RECURRING_FREQUENCIES.map((option) => (
                  <MenuItem key={option.value} value={option.value}>
                    {option.label}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Repeat every"
              type="number"
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    {settings.frequency === 'weekly' ? 'week(s)' : 
                     settings.frequency === 'monthly' ? 'month(s)' : 
                     settings.frequency === 'quarterly' ? 'quarter(s)' : 
                     settings.frequency === 'yearly' ? 'year(s)' : 'interval(s)'}
                  </InputAdornment>
                ),
                inputProps: { min: 1 }
              }}
              value={settings.interval}
              onChange={(e) => handleChange('interval', parseInt(e.target.value) || 1)}
            />
          </Grid>
          
          {settings.frequency === 'weekly' && (
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel id="day-of-week-label">Day of Week</InputLabel>
                <Select
                  labelId="day-of-week-label"
                  value={settings.dayOfWeek || 1}
                  label="Day of Week"
                  onChange={(e) => handleChange('dayOfWeek', e.target.value)}
                >
                  {getDaysOfWeek().map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
          )}
          
          {(settings.frequency === 'monthly' || settings.frequency === 'quarterly' || settings.frequency === 'yearly') && (
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel id="day-of-month-label">Day of Month</InputLabel>
                <Select
                  labelId="day-of-month-label"
                  value={settings.dayOfMonth || 1}
                  label="Day of Month"
                  onChange={(e) => handleChange('dayOfMonth', e.target.value)}
                >
                  {getDaysOfMonth().map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
          )}
          
          <Grid item xs={12} md={6}>
            <StandardDatePicker
              label="Start Date"
              value={dayjs(settings.startDate)}
              onChange={(date) => {
                if (date) {
                  handleChange('startDate', date.format('YYYY-MM-DD'));
                }
              }}
              required
              businessContext="invoice"
              showQuickActions
              dateFormat="DD/MM/YYYY"
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <StandardDatePicker
              label="End Date (Optional)"
              value={settings.endDate ? dayjs(settings.endDate) : null}
              onChange={(date) => {
                handleChange('endDate', date ? date.format('YYYY-MM-DD') : undefined);
              }}
              businessContext="invoice"
              showQuickActions
              dateFormat="DD/MM/YYYY"
              helperText="Leave blank for no end date"
            />
          </Grid>
          
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Maximum Occurrences (Optional)"
              type="number"
              InputProps={{
                inputProps: { min: 1 }
              }}
              value={settings.maxOccurrences || ''}
              onChange={(e) => {
                const value = e.target.value ? parseInt(e.target.value) : undefined;
                handleChange('maxOccurrences', value);
              }}
              placeholder="No limit"
              helperText="Leave blank for no limit"
            />
          </Grid>
          
          <Grid item xs={12}>
            <FormControlLabel
              control={
                <Switch
                  checked={settings.sendAutomatically}
                  onChange={(e) => handleChange('sendAutomatically', e.target.checked)}
                  color="primary"
                />
              }
              label={
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  Send automatically
                  <Tooltip title="When enabled, invoices will be automatically sent to the customer on the scheduled date">
                    <IconButton size="small" sx={{ ml: 0.5 }}>
                      <InfoIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                </Box>
              }
            />
          </Grid>
        </Grid>
      </Collapse>
    </Paper>
  );
};

export default RecurringInvoiceForm; 