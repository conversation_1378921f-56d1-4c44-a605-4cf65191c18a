"""
Customer Invoice Views - Sales Module
Replica of Vendor Bill views but for Accounts Receivable
"""

from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import filters
from django.db.models import Sum
from django.utils import timezone
from datetime import timed<PERSON><PERSON>

from .models import CustomerInvoice, CustomerInvoiceItem, SalesOrder, DeliveryNote
from .customer_invoice_serializers import CustomerInvoiceSerializer


class CustomerInvoiceViewSet(viewsets.ModelViewSet):
    """ViewSet for managing customer invoices - comprehensive ERP implementation"""
    queryset = CustomerInvoice.objects.all()
    serializer_class = CustomerInvoiceSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['status', 'customer', 'invoice_date', 'due_date']
    search_fields = ['invoice_number', 'customer__display_name', 'reference_number', 'notes']
    ordering_fields = ['invoice_date', 'due_date', 'total_amount', 'created_at']
    ordering = ['-invoice_date', '-created_at']

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)

    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Get customer invoice statistics"""
        queryset = self.get_queryset()
        
        total_invoices = queryset.count()
        draft_invoices = queryset.filter(status='draft').count()
        posted_invoices = queryset.filter(status='posted').count()
        paid_invoices = queryset.filter(status='paid').count()
        overdue_invoices = queryset.filter(
            status__in=['posted', 'sent', 'partial'],
            due_date__lt=timezone.now().date()
        ).count()
        
        # Calculate totals
        total_receivables = queryset.filter(
            status__in=['posted', 'sent', 'partial']
        ).aggregate(
            total=Sum('balance_due')
        )['total'] or 0
        
        return Response({
            'total_invoices': total_invoices,
            'draft_invoices': draft_invoices,
            'posted_invoices': posted_invoices,
            'paid_invoices': paid_invoices,
            'overdue_invoices': overdue_invoices,
            'total_receivables': float(total_receivables),
        })

    @action(detail=True, methods=['post'])
    def post_invoice(self, request, pk=None):
        """Post a draft invoice"""
        invoice = self.get_object()
        
        if invoice.status != 'draft':
            return Response(
                {'error': 'Only draft invoices can be posted'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Update status
        invoice.status = 'posted'
        invoice.save()
        
        return Response({'message': 'Invoice posted successfully'})

    @action(detail=True, methods=['post'])
    def void_invoice(self, request, pk=None):
        """Void an invoice"""
        invoice = self.get_object()
        
        if invoice.status in ['paid', 'void']:
            return Response(
                {'error': 'Cannot void paid or already voided invoices'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Update status
        invoice.status = 'void'
        invoice.save()
        
        return Response({'message': 'Invoice voided successfully'})

    @action(detail=False, methods=['get'])
    def billable_sales_orders(self, request):
        """Get sales orders that can be invoiced (service SOs)"""
        try:
            # Get service sales orders that are in valid status for invoice creation
            billable_sos = SalesOrder.objects.filter(
                status__in=['confirmed', 'acknowledged', 'partial']
            ).select_related('customer').prefetch_related('line_items__product')
            
            # Filter for service SOs only (exclude those already invoiced)
            service_sos = []
            for so in billable_sos:
                # Check if SO already has an invoice
                existing_invoices = CustomerInvoice.objects.filter(sales_order=so)
                if existing_invoices.exists():
                    continue
                
                has_services = False
                has_goods = False
                
                for line_item in so.line_items.all():
                    if line_item.product:
                        if line_item.product.product_type == 'service':
                            has_services = True
                        elif line_item.product.product_type == 'product':
                            has_goods = True
                    else:
                        # If no product linked, assume it's a service line item
                        has_services = True
                
                # Only include service SOs (exclude pure goods SOs)
                if has_services and not has_goods:
                    # Include pure service SOs
                    so_data = {
                        'so_id': so.id,
                        'so_number': so.so_number,
                        'customer_name': so.customer.display_name if so.customer else 'No Customer',
                        'so_date': so.so_date,
                        'status': so.status,
                        'total_amount': float(so.total_amount),
                        'line_items_count': so.line_items.count(),
                        'items': []
                    }
                    
                    # Add line items
                    for line_item in so.line_items.all():
                        item_data = {
                            'product_name': line_item.product.name if line_item.product else 'Service Item',
                            'quantity': float(line_item.quantity),
                            'unit_price': float(line_item.unit_price),
                            'total_price': float(line_item.line_total),
                            'tax_rate': float(line_item.tax_rate)
                        }
                        so_data['items'].append(item_data)
                    
                    service_sos.append(so_data)
            
            return Response(service_sos)
            
        except Exception as e:
            return Response(
                {'error': f'Failed to fetch billable service sales orders: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['get'])
    def billable_delivery_notes(self, request):
        """Get delivery notes that can be invoiced (goods DNs)"""
        try:
            # Get goods delivery notes that are in valid status for invoice creation
            billable_dns = DeliveryNote.objects.filter(
                status__in=['delivered', 'posted']
            ).select_related('customer').prefetch_related('line_items__product')
            
            # Filter for goods DNs only (exclude those already invoiced)
            goods_dns = []
            for dn in billable_dns:
                # Check if DN already has an invoice
                existing_invoices = CustomerInvoice.objects.filter(delivery_note=dn)
                if existing_invoices.exists():
                    continue
                
                # Only include DNs containing goods (not services)
                has_goods = False
                for line_item in dn.line_items.all():
                    if line_item.product and line_item.product.product_type == 'product':
                        has_goods = True
                        break
                
                if has_goods:
                    dn_data = {
                        'dn_id': dn.id,
                        'dn_number': dn.dn_number,
                        'customer_name': dn.customer.display_name if dn.customer else 'No Customer',
                        'delivery_date': dn.delivery_date,
                        'status': dn.status,
                        'total_value': float(dn.total_value) if hasattr(dn, 'total_value') else 0,
                        'warehouse_name': dn.warehouse.name if dn.warehouse else 'No Warehouse',
                        'items': []
                    }
                    
                    # Add line items
                    for line_item in dn.line_items.all():
                        if line_item.product and line_item.product.product_type == 'product':
                            item_data = {
                                'product_name': line_item.product.name,
                                'quantity_delivered': float(line_item.quantity_delivered),
                                'unit_price': float(line_item.unit_price),
                                'total_price': float(line_item.quantity_delivered * line_item.unit_price)
                            }
                            dn_data['items'].append(item_data)
                    
                    goods_dns.append(dn_data)
            
            return Response(goods_dns)
            
        except Exception as e:
            return Response(
                {'error': f'Failed to fetch billable goods delivery notes: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['post'])
    def create_from_sales_order(self, request):
        """Create customer invoice from sales order (for services)"""
        sales_order_id = request.data.get('sales_order_id')

        if not sales_order_id:
            return Response(
                {'error': 'Sales order ID is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            sales_order = SalesOrder.objects.get(id=sales_order_id)
        except SalesOrder.DoesNotExist:
            return Response(
                {'error': 'Sales order not found'},
                status=status.HTTP_404_NOT_FOUND
            )

        # Check if sales order already has an invoice
        existing_invoices = CustomerInvoice.objects.filter(sales_order=sales_order)
        if existing_invoices.exists():
            return Response(
                {'error': 'This sales order already has an invoice created'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Validate sales order exists
        if not sales_order.customer:
            return Response(
                {'error': 'Sales order has no customer assigned'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Prepare invoice data
        invoice_data = {
            'customer': sales_order.customer.id,
            'invoice_date': request.data.get('invoice_date', timezone.now().date()),
            'due_date': request.data.get('due_date', timezone.now().date() + timedelta(days=30)),
            'reference_number': f"SO-{sales_order.so_number}",
            'notes': request.data.get('notes', f"Invoice created from Sales Order {sales_order.so_number}"),
            'line_items': []
        }

        # Copy line items from sales order
        for so_item in sales_order.line_items.all():
            # Round values to 2 decimal places to match CustomerInvoiceItem model constraints
            quantity = round(float(so_item.quantity), 2)
            unit_price = round(float(so_item.unit_price), 2)

            line_item_data = {
                'product': so_item.product.id if so_item.product else None,
                'item_description': so_item.product.name if so_item.product else f"Service from SO {sales_order.so_number}",
                'quantity': quantity,
                'unit_price': unit_price,
                'tax_rate': so_item.tax_rate,
                'account_code': getattr(so_item.product, 'income_account_code', '4010-SALES') if so_item.product else '4010-SALES'
            }
            invoice_data['line_items'].append(line_item_data)

        if not invoice_data['line_items']:
            return Response(
                {'error': 'No billable items found in this sales order'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Create the customer invoice using the existing serializer
        serializer = CustomerInvoiceSerializer(data=invoice_data, context={'request': request})
        if serializer.is_valid():
            customer_invoice = serializer.save()

            # Link the sales order to the customer invoice
            customer_invoice.sales_order = sales_order
            customer_invoice.save()

            return Response({
                'message': 'Customer invoice created successfully from sales order',
                'invoice_id': customer_invoice.id,
                'invoice_number': customer_invoice.invoice_number,
                'so_number': sales_order.so_number
            }, status=status.HTTP_201_CREATED)
        else:
            return Response(
                {'error': 'Failed to create customer invoice', 'details': serializer.errors},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=False, methods=['post'])
    def create_from_delivery_note(self, request):
        """Create customer invoice from delivery note (for goods)"""
        delivery_note_id = request.data.get('delivery_note_id')

        if not delivery_note_id:
            return Response(
                {'error': 'Delivery note ID is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            delivery_note = DeliveryNote.objects.get(id=delivery_note_id)
        except DeliveryNote.DoesNotExist:
            return Response(
                {'error': 'Delivery note not found'},
                status=status.HTTP_404_NOT_FOUND
            )

        # Check if delivery note already has an invoice
        existing_invoices = CustomerInvoice.objects.filter(delivery_note=delivery_note)
        if existing_invoices.exists():
            return Response(
                {'error': 'This delivery note already has an invoice created'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Validate delivery note has customer
        if not delivery_note.customer:
            return Response(
                {'error': 'Delivery note has no customer assigned'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Prepare invoice data
        invoice_data = {
            'customer': delivery_note.customer.id,
            'invoice_date': request.data.get('invoice_date', timezone.now().date()),
            'due_date': request.data.get('due_date', timezone.now().date() + timedelta(days=30)),
            'reference_number': f"DN-{delivery_note.dn_number}",
            'notes': request.data.get('notes', f"Invoice created from Delivery Note {delivery_note.dn_number}"),
            'line_items': []
        }

        # Copy line items from delivery note
        for dn_item in delivery_note.line_items.all():
            # Only include goods items that were delivered
            if dn_item.product and dn_item.product.product_type == 'product' and dn_item.quantity_delivered > 0:
                # Round values to 2 decimal places to match CustomerInvoiceItem model constraints
                quantity = round(float(dn_item.quantity_delivered), 2)
                unit_price = round(float(dn_item.unit_price), 2)

                line_item_data = {
                    'product': dn_item.product.id,
                    'item_description': dn_item.product.name,
                    'quantity': quantity,
                    'unit_price': unit_price,
                    'tax_rate': 0,  # Delivery notes typically don't have tax info, will be added in invoice
                    'account_code': getattr(dn_item.product, 'income_account_code', '4010-SALES')
                }
                invoice_data['line_items'].append(line_item_data)

        if not invoice_data['line_items']:
            return Response(
                {'error': 'No billable goods found in this delivery note'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Create the customer invoice using the existing serializer
        serializer = CustomerInvoiceSerializer(data=invoice_data, context={'request': request})
        if serializer.is_valid():
            customer_invoice = serializer.save()

            # Link the delivery note to the customer invoice
            customer_invoice.delivery_note = delivery_note
            customer_invoice.save()

            return Response({
                'message': 'Customer invoice created successfully from delivery note',
                'invoice_id': customer_invoice.id,
                'invoice_number': customer_invoice.invoice_number,
                'dn_number': delivery_note.dn_number
            }, status=status.HTTP_201_CREATED)
        else:
            return Response(
                {'error': 'Failed to create customer invoice', 'details': serializer.errors},
                status=status.HTTP_400_BAD_REQUEST
            )
