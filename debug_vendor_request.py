#!/usr/bin/env python3
"""
Debug script to capture the exact request being made and the exact error response
"""
import requests
import json
import sys

def get_auth_token():
    """Get authentication token"""
    url = "http://localhost:8000/api-token-auth/"
    data = {"username": "admin", "password": "admin123"}
    
    try:
        print(f"Getting auth token from {url}")
        response = requests.post(url, json=data)
        print(f"Auth response: {response.status_code}")
        if response.status_code == 200:
            token = response.json().get('token')
            print(f"Got token: {token[:10] if token else 'None'}...")
            return token
        else:
            print(f"Auth failed: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"Auth error: {e}")
        return None

def debug_vendor_creation():
    """Debug vendor creation with detailed logging"""
    print("=== DEBUGGING VENDOR CREATION ===")
    
    # Get auth token
    token = get_auth_token()
    if not token:
        print("❌ Failed to get auth token")
        return
    
    print(f"✅ Got auth token: {token[:10]}...")
    
    # Test data that matches frontend form exactly
    test_data = {
        "displayName": "Test Company",
        "firstName": "John",
        "lastName": "Doe",
        "companyName": "Test Company Ltd",
        "email": "<EMAIL>",
        "phone": "************",
        "mobile": "************",
        "billingAddress": {
            "street": "123 Main St",
            "city": "Test City",
            "state": "Test State",
            "postalCode": "12345",
            "country": "India"
        },
        "shippingAddress": {
            "sameAsBilling": True,
            "street": "",
            "city": "",
            "state": "",
            "postalCode": "",
            "country": "India"
        },
        "paymentTerms": "1",
        "creditLimit": 10000,
        "vendorCategory": "Technology",
        "leadTimeDays": 7,
        "minimumOrderAmount": 500,
        "preferredVendor": True,
        "notes": "Test vendor",
        "is_active": True
    }
    
    url = "http://localhost:8000/api/contacts/vendors/"
    headers = {
        "Authorization": f"Token {token}",
        "Content-Type": "application/json"
    }
    
    print(f"\n📤 REQUEST DETAILS:")
    print(f"URL: {url}")
    print(f"Method: POST")
    print(f"Headers: {headers}")
    print(f"Data: {json.dumps(test_data, indent=2)}")
    
    try:
        response = requests.post(url, json=test_data, headers=headers)
        
        print(f"\n📥 RESPONSE DETAILS:")
        print(f"Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        print(f"Response Body: {response.text}")
        
        if response.status_code == 400:
            print(f"\n🔍 ANALYZING 400 ERROR:")
            try:
                error_data = response.json()
                print(f"Error JSON: {json.dumps(error_data, indent=2)}")
                
                # Check specific field errors
                for field, errors in error_data.items():
                    print(f"Field '{field}': {errors}")
                    if field == 'companyName':
                        print(f"❌ FOUND THE ISSUE: companyName field error: {errors}")
                        
            except Exception as e:
                print(f"Could not parse error JSON: {e}")
                print(f"Raw response: {response.text}")
        
        elif response.status_code == 201:
            print("✅ SUCCESS: Vendor created successfully!")
            
    except Exception as e:
        print(f"❌ Request failed: {e}")

def test_empty_company_name():
    """Test with empty company name to see if validation works"""
    print("\n=== TESTING EMPTY COMPANY NAME ===")
    
    token = get_auth_token()
    if not token:
        return
    
    test_data = {
        "displayName": "Test Individual",
        "firstName": "Jane",
        "lastName": "Smith",
        "companyName": "",  # Empty company name
        "email": "<EMAIL>",
        "phone": "************",
        "billingAddress": {
            "street": "456 Oak St",
            "city": "Test City",
            "state": "Test State",
            "postalCode": "12345",
            "country": "India"
        },
        "shippingAddress": {
            "sameAsBilling": True,
            "street": "",
            "city": "",
            "state": "",
            "postalCode": "",
            "country": "India"
        },
        "paymentTerms": "1",
        "creditLimit": 5000,
        "vendorCategory": "Service",
        "leadTimeDays": 5,
        "minimumOrderAmount": 100,
        "preferredVendor": False,
        "notes": "Test individual vendor",
        "is_active": True
    }
    
    url = "http://localhost:8000/api/contacts/vendors/"
    headers = {
        "Authorization": f"Token {token}",
        "Content-Type": "application/json"
    }
    
    print(f"Testing with empty companyName...")
    
    try:
        response = requests.post(url, json=test_data, headers=headers)
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 400:
            error_data = response.json()
            if 'companyName' in error_data:
                print(f"❌ Empty companyName still causes error: {error_data['companyName']}")
            else:
                print("✅ Empty companyName doesn't cause error")
        elif response.status_code == 201:
            print("✅ Empty companyName works fine")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    debug_vendor_creation()
    test_empty_company_name()
