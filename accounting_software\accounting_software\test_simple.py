#!/usr/bin/env python3
"""
Simple test to check if server is running
"""
import requests

# Configuration
BASE_URL = "http://localhost:8000"
TOKEN = "e15bc01f831c5111f413f534ef82288744cb7d41"

# Headers with authentication
HEADERS = {
    'Content-Type': 'application/json',
    'Authorization': f'Token {TOKEN}'
}

def test_server():
    """Test if server is running"""
    try:
        # Test basic endpoint
        response = requests.get(f"{BASE_URL}/api/contacts/vendors/", headers=HEADERS, timeout=5)
        print(f"Vendors endpoint - Status: {response.status_code}")

        # Test vendor bills endpoint
        response2 = requests.get(f"{BASE_URL}/api/purchase/vendor-bills/", headers=HEADERS, timeout=5)
        print(f"Vendor bills endpoint - Status: {response2.status_code}")
        print(f"Content-Type: {response2.headers.get('content-type')}")
        print(f"Response length: {len(response2.text)}")
        print(f"First 200 chars: {response2.text[:200]}")

        if response2.status_code == 200:
            print("✅ Vendor bills endpoint is accessible")
        else:
            print("❌ Vendor bills endpoint error")

    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to server - is it running?")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_server()
