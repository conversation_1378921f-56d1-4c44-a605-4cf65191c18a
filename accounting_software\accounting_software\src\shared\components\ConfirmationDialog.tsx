import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  IconButton,
  styled,
  useTheme,
} from '@mui/material';
import {
  Close as CloseIcon,
  CheckCircle as SuccessIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
  Info as InfoIcon,
} from '@mui/icons-material';

// Styled components for consistent appearance
const StyledDialog = styled(Dialog)(({ theme }) => ({
  '& .MuiDialog-paper': {
    borderRadius: '12px',
    padding: theme.spacing(1),
    minWidth: '400px',
    maxWidth: '500px',
    position: 'relative',
  },
}));

const StyledDialogTitle = styled(DialogTitle)(({ theme }) => ({
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  padding: theme.spacing(2, 3),
  paddingBottom: theme.spacing(1),
  '& .MuiIconButton-root': {
    marginRight: -theme.spacing(1),
  },
}));

const StyledDialogContent = styled(DialogContent)(({ theme }) => ({
  padding: theme.spacing(2, 3),
  textAlign: 'center',
}));

const StyledDialogActions = styled(DialogActions)(({ theme }) => ({
  padding: theme.spacing(2, 3),
  justifyContent: 'center',
  gap: theme.spacing(2),
}));

const IconContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  justifyContent: 'center',
  marginBottom: theme.spacing(2),
  '& .MuiSvgIcon-root': {
    fontSize: '3rem',
  },
}));

export type ConfirmationType = 'success' | 'warning' | 'error' | 'info' | 'confirm';

export interface ConfirmationDialogProps {
  open: boolean;
  onClose: () => void;
  onConfirm?: () => void;
  title: string;
  message: string;
  type?: ConfirmationType;
  confirmText?: string;
  cancelText?: string;
  showCancel?: boolean;
  confirmColor?: 'primary' | 'secondary' | 'error' | 'warning' | 'info' | 'success';
  autoClose?: boolean;
  autoCloseDelay?: number;
}

const ConfirmationDialog: React.FC<ConfirmationDialogProps> = ({
  open,
  onClose,
  onConfirm,
  title,
  message,
  type = 'confirm',
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  showCancel = true,
  confirmColor = 'primary',
  autoClose = false,
  autoCloseDelay = 3000,
}) => {
  const theme = useTheme();

  // Auto close for success/info messages
  React.useEffect(() => {
    if (open && autoClose && (type === 'success' || type === 'info')) {
      const timer = setTimeout(() => {
        onClose();
      }, autoCloseDelay);
      return () => clearTimeout(timer);
    }
  }, [open, autoClose, autoCloseDelay, type, onClose]);

  const getIcon = () => {
    switch (type) {
      case 'success':
        return <SuccessIcon sx={{ color: theme.palette.success.main }} />;
      case 'warning':
        return <WarningIcon sx={{ color: theme.palette.warning.main }} />;
      case 'error':
        return <ErrorIcon sx={{ color: theme.palette.error.main }} />;
      case 'info':
        return <InfoIcon sx={{ color: theme.palette.info.main }} />;
      default:
        return <WarningIcon sx={{ color: theme.palette.warning.main }} />;
    }
  };

  const getConfirmButtonColor = () => {
    switch (type) {
      case 'error':
        return 'error';
      case 'warning':
        return 'warning';
      case 'success':
        return 'success';
      default:
        return confirmColor;
    }
  };

  const handleConfirm = () => {
    if (onConfirm) {
      onConfirm();
    } else {
      // For success/info dialogs without onConfirm, just close
      onClose();
    }
  };

  return (
    <StyledDialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth={false}
      disableEscapeKeyDown={type === 'success' || type === 'info'}
    >
      <StyledDialogTitle>
        <Typography variant="h6" component="div" sx={{ fontWeight: 600 }}>
          {title}
        </Typography>
        {(type === 'success' || type === 'info') && (
          <IconButton onClick={onClose} size="small">
            <CloseIcon />
          </IconButton>
        )}
      </StyledDialogTitle>

      <StyledDialogContent>
        <IconContainer>
          {getIcon()}
        </IconContainer>
        
        <Typography 
          variant="body1" 
          sx={{ 
            color: theme.palette.text.primary,
            lineHeight: 1.6,
            fontSize: '1rem',
          }}
        >
          {message}
        </Typography>
      </StyledDialogContent>

      <StyledDialogActions>
        {showCancel && type !== 'success' && type !== 'info' && (
          <Button
            onClick={onClose}
            variant="outlined"
            sx={{ 
              minWidth: '100px',
              textTransform: 'none',
              fontWeight: 500,
            }}
          >
            {cancelText}
          </Button>
        )}
        
        {(type === 'confirm' || type === 'warning' || type === 'error') && onConfirm && (
          <Button
            onClick={handleConfirm}
            variant="contained"
            color={getConfirmButtonColor()}
            sx={{ 
              minWidth: '100px',
              textTransform: 'none',
              fontWeight: 500,
            }}
          >
            {confirmText}
          </Button>
        )}

        {(type === 'success' || type === 'info') && (
          <Button
            onClick={handleConfirm}
            variant="contained"
            color="primary"
            sx={{
              minWidth: '100px',
              textTransform: 'none',
              fontWeight: 500,
            }}
          >
            OK
          </Button>
        )}
      </StyledDialogActions>
    </StyledDialog>
  );
};

export default ConfirmationDialog;
