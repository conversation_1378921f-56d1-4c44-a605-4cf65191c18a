from django.test import TestCase
from django.contrib.auth.models import User
from rest_framework.test import APIClient
from rest_framework import status
from .models import Vendor, Contact
import json

class VendorAPITestCase(TestCase):
    def setUp(self):
        """Set up test data"""
        self.client = APIClient()
        
        # Create test user
        self.user = User.objects.create_user(
            username='testuser',
            password='testpass123'
        )
        
        # Authenticate client
        self.client.force_authenticate(user=self.user)
        
        # Test vendor data
        self.vendor_data = {
            "displayName": "Test Vendor Company",
            "firstName": "John",
            "lastName": "Doe", 
            "companyName": "Test Vendor Company Ltd",
            "email": "<EMAIL>",
            "phone": "************",
            "mobile": "************",
            "billingAddress": {
                "street": "123 Test Street",
                "city": "Test City",
                "state": "Test State",
                "postalCode": "12345",
                "country": "India"
            },
            "paymentTerms": "Net 30",
            "creditLimit": 10000,
            "vendorCategory": "Technology",
            "leadTimeDays": 7,
            "minimumOrderAmount": 500,
            "preferredVendor": True,
            "notes": "Test vendor for API validation",
            "is_active": True
        }

    def test_vendor_creation_with_company_name(self):
        """Test that vendor creation works with companyName field"""
        response = self.client.post('/api/contacts/vendors/', self.vendor_data, format='json')
        
        # Check if creation was successful
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        
        # Verify vendor was created
        self.assertTrue(Vendor.objects.filter(company_name="Test Vendor Company Ltd").exists())
        
        # Verify contact was created
        self.assertTrue(Contact.objects.filter(name="Test Vendor Company").exists())

    def test_vendor_creation_empty_company_name(self):
        """Test that vendor creation works with empty companyName field"""
        # Test with empty company name
        test_data = self.vendor_data.copy()
        test_data["companyName"] = ""
        
        response = self.client.post('/api/contacts/vendors/', test_data, format='json')
        
        # Check if creation was successful
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)

    def test_vendor_list(self):
        """Test vendor list endpoint"""
        response = self.client.get('/api/contacts/vendors/')
        
        # Check if list request was successful
        self.assertEqual(response.status_code, status.HTTP_200_OK)
