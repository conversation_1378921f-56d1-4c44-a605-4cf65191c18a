import React, { useState, useEffect } from 'react';
import {
  Box,
  TextField,
  Button,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Switch,
  Typography,
  Alert,
  CircularProgress,
  Divider,
} from '@mui/material';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { usePurchaseProducts } from '../../../contexts/PurchaseProductContext';
import { PurchaseProduct, PurchaseProductFormData } from '../../../shared/types/purchase.types';

interface PurchaseProductFormProps {
  product?: PurchaseProduct | null;
  onSuccess: () => void;
  onCancel: () => void;
}

const validationSchema = Yup.object({
  name: Yup.string().required('Product name is required'),
  sku: Yup.string().required('SKU is required'),
  type: Yup.string().oneOf(['product', 'service']).required('Type is required'),
  purchasePrice: Yup.number().min(0, 'Price must be positive').required('Purchase price is required'),
  category: Yup.string().required('Category is required'),
});

const PurchaseProductForm: React.FC<PurchaseProductFormProps> = ({
  product,
  onSuccess,
  onCancel,
}) => {
  const { addPurchaseProduct, updatePurchaseProduct } = usePurchaseProducts();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const isEditMode = Boolean(product);

  const formik = useFormik<PurchaseProductFormData>({
    initialValues: {
      name: product?.name || '',
      sku: product?.sku || '',
      description: product?.description || '',
      type: product?.type || 'product',
      category: product?.category || 'general',
      purchasePrice: product?.purchasePrice || 0,
      vendorId: product?.vendorId || '',
      vendorName: product?.vendorName || '',
      vendorSKU: product?.vendorSKU || '',
      leadTime: product?.leadTime || 0,
      minimumOrderQuantity: product?.minimumOrderQuantity || 1,
      preferredVendor: product?.preferredVendor || false,
      trackInventory: product?.trackInventory ?? true,
      inventoryQuantity: product?.inventoryQuantity || 0,
      lowStockAlert: product?.lowStockAlert || false,
      lowStockThreshold: product?.lowStockThreshold || 0,
      status: product?.status || 'active',
      notes: product?.notes || '',
      attachments: product?.attachments || [],
    },
    validationSchema,
    onSubmit: async (values) => {
      try {
        setIsSubmitting(true);
        setError(null);

        if (isEditMode && product) {
          const updatedProduct: PurchaseProduct = {
            ...product,
            ...values,
          };
          await updatePurchaseProduct(updatedProduct);
        } else {
          await addPurchaseProduct(values);
        }

        onSuccess();
      } catch (err) {
        console.error('Form submission error:', err);
        setError(err instanceof Error ? err.message : 'Failed to save product');
      } finally {
        setIsSubmitting(false);
      }
    },
  });

  return (
    <Box component="form" onSubmit={formik.handleSubmit} sx={{ mt: 2 }}>
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* Basic Information */}
        <Grid item xs={12}>
          <Typography variant="h6" gutterBottom>
            Basic Information
          </Typography>
        </Grid>

        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            name="name"
            label="Product/Service Name"
            value={formik.values.name}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            error={formik.touched.name && Boolean(formik.errors.name)}
            helperText={formik.touched.name && formik.errors.name}
            required
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            name="sku"
            label="SKU"
            value={formik.values.sku}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            error={formik.touched.sku && Boolean(formik.errors.sku)}
            helperText={formik.touched.sku && formik.errors.sku}
            required
          />
        </Grid>

        <Grid item xs={12}>
          <TextField
            fullWidth
            name="description"
            label="Description"
            multiline
            rows={3}
            value={formik.values.description}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
          />
        </Grid>

        <Grid item xs={12} md={6}>
          <FormControl fullWidth required>
            <InputLabel>Type</InputLabel>
            <Select
              name="type"
              value={formik.values.type}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              error={formik.touched.type && Boolean(formik.errors.type)}
            >
              <MenuItem value="product">Product</MenuItem>
              <MenuItem value="service">Service</MenuItem>
            </Select>
          </FormControl>
        </Grid>

        <Grid item xs={12} md={6}>
          <FormControl fullWidth required>
            <InputLabel>Category</InputLabel>
            <Select
              name="category"
              value={formik.values.category}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              error={formik.touched.category && Boolean(formik.errors.category)}
            >
              <MenuItem value="general">General</MenuItem>
              <MenuItem value="raw_materials">Raw Materials</MenuItem>
              <MenuItem value="components">Components</MenuItem>
              <MenuItem value="finished_goods">Finished Goods</MenuItem>
              <MenuItem value="office_supplies">Office Supplies</MenuItem>
              <MenuItem value="equipment">Equipment</MenuItem>
              <MenuItem value="services">Services</MenuItem>
              <MenuItem value="software">Software</MenuItem>
              <MenuItem value="maintenance">Maintenance</MenuItem>
              <MenuItem value="consulting">Consulting</MenuItem>
            </Select>
          </FormControl>
        </Grid>

        {/* Pricing */}
        <Grid item xs={12}>
          <Divider sx={{ my: 2 }} />
          <Typography variant="h6" gutterBottom>
            Pricing
          </Typography>
        </Grid>

        <Grid item xs={12} md={6}>
          <TextField
            fullWidth
            name="purchasePrice"
            label="Purchase Price"
            type="number"
            inputProps={{ min: 0, step: 0.01 }}
            value={formik.values.purchasePrice}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
            error={formik.touched.purchasePrice && Boolean(formik.errors.purchasePrice)}
            helperText={formik.touched.purchasePrice && formik.errors.purchasePrice}
            required
          />
        </Grid>

        {/* Inventory */}
        {formik.values.type === 'product' && (
          <>
            <Grid item xs={12}>
              <Divider sx={{ my: 2 }} />
              <Typography variant="h6" gutterBottom>
                Inventory
              </Typography>
            </Grid>

            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Switch
                    name="trackInventory"
                    checked={formik.values.trackInventory}
                    onChange={formik.handleChange}
                  />
                }
                label="Track Inventory"
              />
            </Grid>

            {formik.values.trackInventory && (
              <>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    name="inventoryQuantity"
                    label="Current Quantity"
                    type="number"
                    inputProps={{ min: 0 }}
                    value={formik.values.inventoryQuantity}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    name="lowStockThreshold"
                    label="Low Stock Threshold"
                    type="number"
                    inputProps={{ min: 0 }}
                    value={formik.values.lowStockThreshold}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                  />
                </Grid>

                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        name="lowStockAlert"
                        checked={formik.values.lowStockAlert}
                        onChange={formik.handleChange}
                      />
                    }
                    label="Enable Low Stock Alerts"
                  />
                </Grid>
              </>
            )}
          </>
        )}

        {/* Status */}
        <Grid item xs={12}>
          <Divider sx={{ my: 2 }} />
          <Typography variant="h6" gutterBottom>
            Status
          </Typography>
        </Grid>

        <Grid item xs={12} md={6}>
          <FormControl fullWidth>
            <InputLabel>Status</InputLabel>
            <Select
              name="status"
              value={formik.values.status}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
            >
              <MenuItem value="active">Active</MenuItem>
              <MenuItem value="inactive">Inactive</MenuItem>
            </Select>
          </FormControl>
        </Grid>

        {/* Notes */}
        <Grid item xs={12}>
          <TextField
            fullWidth
            name="notes"
            label="Notes"
            multiline
            rows={3}
            value={formik.values.notes}
            onChange={formik.handleChange}
            onBlur={formik.handleBlur}
          />
        </Grid>

        {/* Form Actions */}
        <Grid item xs={12}>
          <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end', mt: 3 }}>
            <Button
              variant="outlined"
              onClick={onCancel}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="contained"
              disabled={isSubmitting}
              startIcon={isSubmitting ? <CircularProgress size={20} /> : null}
            >
              {isSubmitting 
                ? 'Saving...' 
                : isEditMode 
                  ? 'Update Product' 
                  : 'Create Product'
              }
            </Button>
          </Box>
        </Grid>
      </Grid>
    </Box>
  );
};

export default PurchaseProductForm;
