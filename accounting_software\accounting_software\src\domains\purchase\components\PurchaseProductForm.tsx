import React, { useState, useEffect } from 'react';
import {
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Grid,
  Typography,
  Box,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormControlLabel,
  Checkbox,
  CircularProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Chip,
  IconButton,
  Tooltip,
  FormHelperText,
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Category as CategoryIcon,
  LocalOffer as PriceIcon,
  Inventory as InventoryIcon,
  Info as InfoIcon,
  Close as CloseIcon,
} from '@mui/icons-material';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { usePurchaseProducts } from '../../../contexts/PurchaseProductContext';
import { useProductCategories } from '../../../contexts/ProductCategoriesContext';
import { PurchaseProduct, PurchaseProductFormData } from '../../../shared/types/purchase.types';
import FormattedCurrencyInput from '../../../shared/components/FormattedCurrencyInput';
import { QuantityInput } from '../../../shared/components';
import ProductCategoryModal from '../../../shared/components/ProductCategoryModal';
import ConfirmationDialog from '../../../shared/components/ConfirmationDialog';
import { useConfirmation } from '../../../shared/hooks/useConfirmation';

interface PurchaseProductFormProps {
  mode: 'create' | 'edit' | 'view';
  initialData?: PurchaseProduct | null;
  onClose: () => void;
}

const defaultInitialValues: PurchaseProductFormData = {
  name: '',
  sku: '',
  description: '',
  type: 'product',
  category: '',
  purchasePrice: 0,
  vendorId: '',
  vendorName: '',
  vendorSKU: '',
  leadTime: 0,
  minimumOrderQuantity: 1,
  preferredVendor: false,
  trackInventory: true,
  inventoryQuantity: 0,
  lowStockAlert: false,
  lowStockThreshold: 0,
  status: 'active',
  notes: '',
  attachments: [],
};

const PurchaseProductForm: React.FC<PurchaseProductFormProps> = ({ mode, initialData, onClose }) => {
  const { addPurchaseProduct, updatePurchaseProduct } = usePurchaseProducts();
  const { categories, loadCategories } = useProductCategories();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [expanded, setExpanded] = useState<string | false>('panel1');
  const [categoryModalOpen, setCategoryModalOpen] = useState(false);
  const { confirmation, showSuccess, showError, hideConfirmation } = useConfirmation();

  const isReadOnly = mode === 'view';

  const validationSchema = Yup.object({
    name: Yup.string().required('Product name is required'),
    sku: Yup.string().required('SKU is required'),
    type: Yup.string().required('Type is required'),
    category: Yup.string().required('Category is required'),
    purchasePrice: Yup.number().min(0, 'Price must be positive').required('Purchase price is required'),
    status: Yup.string().required('Status is required'),
  });

  const formik = useFormik({
    initialValues: initialData || defaultInitialValues,
    validationSchema,
    enableReinitialize: true,
    onSubmit: async (values) => {
      setIsSubmitting(true);
      try {
        if (mode === 'create') {
          await addPurchaseProduct(values);
          showSuccess(
            'Product Created!',
            `Purchase product "${values.name}" has been created successfully.`
          );
        } else if (mode === 'edit' && initialData) {
          await updatePurchaseProduct({ ...initialData, ...values });
          showSuccess(
            'Product Updated!',
            `Purchase product "${values.name}" has been updated successfully.`
          );
        }

        // Close after a short delay to show the success message
        setTimeout(() => {
          onClose();
        }, 1500);
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
        console.error('Error submitting form:', error);
        showError(
          'Save Failed',
          errorMessage
        );
      } finally {
        setIsSubmitting(false);
      }
    },
  });

  useEffect(() => {
    loadCategories();
  }, [loadCategories]);

  const handleAccordionChange = (panel: string) => (event: React.SyntheticEvent, isExpanded: boolean) => {
    setExpanded(isExpanded ? panel : false);
  };

  const generateSKU = () => {
    const selectedCategory = categories.find(cat => cat.id === formik.values.category);
    const categoryCode = selectedCategory?.code || 'GEN';
    const name = formik.values.name.toUpperCase().replace(/[^A-Z0-9]/g, '').substring(0, 3);
    const timestamp = Date.now().toString().slice(-4);
    const sku = `${categoryCode}-${name}-${timestamp}`;
    formik.setFieldValue('sku', sku);
  };

  return (
    <>
      <DialogTitle sx={{ borderBottom: '1px solid rgba(0, 0, 0, 0.12)', pb: 2, display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', pt: 2 }}>
        <Typography variant="h5" fontWeight="bold" sx={{ mt: 1 }}>
          {mode === 'create' ? 'Create New Product' : 
           mode === 'edit' ? 'Edit Product' : 
           'View Product'}
        </Typography>
        <IconButton onClick={onClose} disabled={isSubmitting} sx={{ mt: -0.5, mr: -1 }}>
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <form onSubmit={formik.handleSubmit}>
        <DialogContent dividers sx={{ p: 0 }}>



          {/* Basic Information */}
          <Accordion
            expanded={expanded === 'panel1'}
            onChange={handleAccordionChange('panel1')}
          >
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Box display="flex" alignItems="center" gap={1}>
                <CategoryIcon />
                <Typography variant="subtitle1" fontWeight="bold">Basic Product Information</Typography>
              </Box>
            </AccordionSummary>
            <AccordionDetails>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    id="name"
                    name="name"
                    label="Product Name *"
                    value={formik.values.name}
                    onChange={formik.handleChange}
                    error={formik.touched.name && Boolean(formik.errors.name)}
                    helperText={formik.touched.name && formik.errors.name}
                    disabled={isReadOnly}
                  />
                </Grid>
                
                <Grid item xs={12} sm={8}>
                  <TextField
                    fullWidth
                    id="sku"
                    name="sku"
                    label="SKU *"
                    value={formik.values.sku}
                    onChange={formik.handleChange}
                    error={formik.touched.sku && Boolean(formik.errors.sku)}
                    helperText={formik.touched.sku && formik.errors.sku}
                    disabled={isReadOnly}
                  />
                </Grid>
                
                <Grid item xs={12} sm={4}>
                  {!isReadOnly && (
                    <Button
                      fullWidth
                      variant="outlined"
                      onClick={generateSKU}
                      disabled={!formik.values.name || !formik.values.category}
                      sx={{ height: '56px' }}
                    >
                      Auto Generate SKU
                    </Button>
                  )}
                </Grid>
                
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    id="description"
                    name="description"
                    label="Description"
                    multiline
                    rows={3}
                    value={formik.values.description}
                    onChange={formik.handleChange}
                    disabled={isReadOnly}
                  />
                </Grid>
                
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth error={formik.touched.type && Boolean(formik.errors.type)}>
                    <InputLabel>Product Type *</InputLabel>
                    <Select
                      id="type"
                      name="type"
                      value={formik.values.type}
                      onChange={formik.handleChange}
                      label="Product Type *"
                      disabled={isReadOnly}
                    >
                      <MenuItem value="product">Physical Product</MenuItem>
                      <MenuItem value="service">Service</MenuItem>
                      <MenuItem value="bundle">Product Bundle</MenuItem>
                    </Select>
                    {formik.touched.type && formik.errors.type && (
                      <FormHelperText>{formik.errors.type}</FormHelperText>
                    )}
                  </FormControl>
                </Grid>
                
                <Grid item xs={12} sm={6}>
                  <Box display="flex" gap={1}>
                    <FormControl fullWidth error={formik.touched.category && Boolean(formik.errors.category)}>
                      <InputLabel>Category *</InputLabel>
                      <Select
                        id="category"
                        name="category"
                        value={formik.values.category}
                        onChange={formik.handleChange}
                        label="Category *"
                        disabled={isReadOnly}
                      >
                        {categories.map((category) => (
                          <MenuItem key={category.id} value={category.id}>
                            <Box display="flex" alignItems="center" gap={1}>
                              <Chip size="small" label={category.code} color="primary" variant="outlined" />
                              {category.name}
                            </Box>
                          </MenuItem>
                        ))}
                      </Select>
                      {formik.touched.category && formik.errors.category && (
                        <FormHelperText>{formik.errors.category}</FormHelperText>
                      )}
                    </FormControl>
                    {!isReadOnly && (
                      <Tooltip title="Manage Categories">
                        <IconButton
                          color="primary"
                          onClick={() => setCategoryModalOpen(true)}
                          sx={{ minWidth: 'auto' }}
                        >
                          <CategoryIcon />
                        </IconButton>
                      </Tooltip>
                    )}
                  </Box>
                </Grid>
              </Grid>
            </AccordionDetails>
          </Accordion>

          {/* Pricing Information */}
          <Accordion
            expanded={expanded === 'panel2'}
            onChange={handleAccordionChange('panel2')}
          >
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Box display="flex" alignItems="center" gap={1}>
                <PriceIcon />
                <Typography variant="subtitle1" fontWeight="bold">Pricing Information</Typography>
              </Box>
            </AccordionSummary>
            <AccordionDetails>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <FormattedCurrencyInput
                    fullWidth
                    label="Purchase Price *"
                    name="purchasePrice"
                    value={formik.values.purchasePrice}
                    onChange={(e) => formik.setFieldValue('purchasePrice', parseFloat(e.target.value) || 0)}
                    currencySymbol="$"
                    error={formik.touched.purchasePrice && Boolean(formik.errors.purchasePrice)}
                    helperText={formik.touched.purchasePrice && formik.errors.purchasePrice}
                    disabled={isReadOnly}
                  />
                </Grid>
                
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="leadTime"
                    name="leadTime"
                    label="Lead Time (days)"
                    type="number"
                    value={formik.values.leadTime}
                    onChange={formik.handleChange}
                    disabled={isReadOnly}
                    InputProps={{ inputProps: { min: 0 } }}
                  />
                </Grid>
              </Grid>
            </AccordionDetails>
          </Accordion>

          {/* Inventory Management */}
          <Accordion
            expanded={expanded === 'panel3'}
            onChange={handleAccordionChange('panel3')}
          >
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Box display="flex" alignItems="center" gap={1}>
                <InventoryIcon />
                <Typography variant="subtitle1" fontWeight="bold">Inventory Management</Typography>
              </Box>
            </AccordionSummary>
            <AccordionDetails>
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Checkbox
                        id="trackInventory"
                        name="trackInventory"
                        checked={formik.values.trackInventory}
                        onChange={formik.handleChange}
                        disabled={isReadOnly}
                      />
                    }
                    label="Track Inventory for this Product"
                  />
                </Grid>
                
                {formik.values.trackInventory && (
                  <>
                    <Grid item xs={12} sm={6}>
                      <QuantityInput
                        fullWidth
                        label="Initial Stock Quantity"
                        value={formik.values.inventoryQuantity}
                        onChange={(e) => formik.setFieldValue('inventoryQuantity', e.target.value)}
                        disabled={isReadOnly}
                        min={0}
                        precision={0}
                        placeholder="0"
                      />
                    </Grid>
                    
                    <Grid item xs={12} sm={6}>
                      <QuantityInput
                        fullWidth
                        label="Low Stock Alert Threshold"
                        value={formik.values.lowStockThreshold}
                        onChange={(e) => formik.setFieldValue('lowStockThreshold', e.target.value)}
                        disabled={isReadOnly}
                        min={0}
                        precision={0}
                        placeholder="0"
                      />
                    </Grid>
                    
                    <Grid item xs={12}>
                      <FormControlLabel
                        control={
                          <Checkbox
                            id="lowStockAlert"
                            name="lowStockAlert"
                            checked={formik.values.lowStockAlert}
                            onChange={formik.handleChange}
                            disabled={isReadOnly}
                          />
                        }
                        label="Enable Low Stock Alerts"
                      />
                    </Grid>
                  </>
                )}
              </Grid>
            </AccordionDetails>
          </Accordion>

          {/* Additional Information */}
          <Accordion
            expanded={expanded === 'panel4'}
            onChange={handleAccordionChange('panel4')}
          >
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Box display="flex" alignItems="center" gap={1}>
                <InfoIcon />
                <Typography variant="subtitle1" fontWeight="bold">Additional Information</Typography>
              </Box>
            </AccordionSummary>
            <AccordionDetails>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth>
                    <InputLabel>Status</InputLabel>
                    <Select
                      id="status"
                      name="status"
                      value={formik.values.status}
                      onChange={formik.handleChange}
                      label="Status"
                      disabled={isReadOnly}
                    >
                      <MenuItem value="active">Active</MenuItem>
                      <MenuItem value="inactive">Inactive</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    id="notes"
                    name="notes"
                    label="Notes"
                    multiline
                    rows={3}
                    value={formik.values.notes}
                    onChange={formik.handleChange}
                    disabled={isReadOnly}
                    placeholder="Additional notes about this product..."
                  />
                </Grid>
              </Grid>
            </AccordionDetails>
          </Accordion>
        </DialogContent>

        <DialogActions sx={{ px: 3, py: 2 }}>
          <Button onClick={onClose} disabled={isSubmitting}>
            Cancel
          </Button>
          {!isReadOnly && (
            <Button
              type="submit"
              variant="contained"
              disabled={isSubmitting}
              startIcon={isSubmitting ? <CircularProgress size={16} /> : undefined}
            >
              {isSubmitting ? 'Saving Product...' : 
               mode === 'create' ? 'Create Product' : 'Update Product'
              }
            </Button>
          )}
        </DialogActions>
      </form>

      <ProductCategoryModal
        open={categoryModalOpen}
        onClose={() => setCategoryModalOpen(false)}
      />

      {/* Confirmation Dialog */}
      <ConfirmationDialog
        open={confirmation.open}
        onClose={hideConfirmation}
        onConfirm={confirmation.onConfirm}
        title={confirmation.title}
        message={confirmation.message}
        type={confirmation.type}
        confirmText={confirmation.confirmText}
        cancelText={confirmation.cancelText}
        showCancel={confirmation.showCancel}
        confirmColor={confirmation.confirmColor}
        autoClose={confirmation.autoClose}
        autoCloseDelay={confirmation.autoCloseDelay}
      />
    </>
  );
};

export default PurchaseProductForm; 