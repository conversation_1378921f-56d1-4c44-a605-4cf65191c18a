#!/usr/bin/env python3
"""
Simple test for vendor bill update
"""
import requests

BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api"
TOKEN = "e15bc01f831c5111f413f534ef82288744cb7d41"

HEADERS = {
    'Content-Type': 'application/json',
    'Authorization': f'Token {TOKEN}'
}

# Simple update test
try:
    # Create a bill first
    vendors_response = requests.get(f"{API_BASE}/contacts/vendors/", headers=HEADERS, timeout=3)
    vendor_id = vendors_response.json()['results'][0]['contact']
    
    # Create bill
    bill_data = {
        "vendor": vendor_id,
        "bill_date": "2025-07-07",
        "due_date": "2025-08-07",
        "status": "draft",
        "payment_terms": "Net 30",
        "reference_number": "UPDATE-TEST",
        "notes": "Test for update",
        "line_items": [
            {
                "item_description": "Original item",
                "quantity": 1,
                "unit_price": 100.00,
                "tax_rate": 10.0,
                "account_code": "5010-COGS",
                "line_order": 1
            }
        ]
    }
    
    create_response = requests.post(f"{API_BASE}/purchase/vendor-bills/", json=bill_data, headers=HEADERS, timeout=5)
    
    if create_response.status_code == 201:
        bill = create_response.json()
        bill_id = bill['id']
        print(f"Created bill {bill['bill_number']} with ID {bill_id}")
        
        # Now update it
        update_data = {
            "vendor": vendor_id,
            "bill_date": "2025-07-07",
            "due_date": "2025-08-07",
            "status": "draft",
            "payment_terms": "Net 45",  # Changed
            "reference_number": "UPDATE-TEST-MODIFIED",  # Changed
            "notes": "Updated test bill",  # Changed
            "line_items": [
                {
                    "item_description": "Updated item",  # Changed
                    "quantity": 2,  # Changed
                    "unit_price": 150.00,  # Changed
                    "tax_rate": 10.0,
                    "account_code": "5010-COGS",
                    "line_order": 1
                }
            ]
        }
        
        update_response = requests.patch(f"{API_BASE}/purchase/vendor-bills/{bill_id}/", json=update_data, headers=HEADERS, timeout=5)
        
        print(f"Update status: {update_response.status_code}")
        if update_response.status_code == 200:
            print("✅ UPDATE SUCCESS")
            updated_bill = update_response.json()
            print(f"New reference: {updated_bill['reference_number']}")
            print(f"New total: ${updated_bill['total_amount']}")
        else:
            print(f"❌ UPDATE FAILED: {update_response.text}")
    else:
        print(f"❌ CREATE FAILED: {create_response.text}")
        
except Exception as e:
    print(f"❌ Exception: {e}")
