import React, { useState, useEffect } from 'react';
import {
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Grid,
  Typography,
  Box,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Autocomplete,
  Card,
  CardContent,
  Chip,
} from '@mui/material';
import { QuantityInput } from '../../../shared/components';
import {
  ExpandMore as ExpandMoreIcon,
  Receipt as ReceiptIcon,
  Inventory as InventoryIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
  Person as PersonIcon,
} from '@mui/icons-material';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { grnService, GRNFormData, GRNItem, PendingPO } from '../services/grn.service';
import { inventoryService, Warehouse } from '../services/inventory.service';
import { purchaseOrderService } from '../../../services/purchaseOrder.service';
import { useAuth } from '../../../contexts/AuthContext';
import ConfirmationDialog from '../../../shared/components/ConfirmationDialog';
import { useConfirmation } from '../../../shared/hooks/useConfirmation';

// Product interface for search
interface Product {
  id: number;
  product_id: string;
  name: string;
  sku: string;
  unit_price: string | number;
  cost_price: string | number;
  description?: string;
}

interface GRNFormProps {
  onClose: () => void;
  onSave: () => void;
  initialValues?: any;
}

const validationSchema = Yup.object({
  purchase_order: Yup.number().required('Purchase Order is required'),
  warehouse: Yup.number().required('Warehouse is required'),
  receipt_date: Yup.string().required('Receipt Date is required'),
  items: Yup.array().of(
    Yup.object({
      quantity_received: Yup.number().min(0, 'Quantity must be positive').required('Quantity is required'),
      condition: Yup.string().required('Condition is required')
    })
  ).min(1, 'At least one item is required'),
});

const GRNForm: React.FC<GRNFormProps> = ({ onClose, onSave, initialValues }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [expanded, setExpanded] = useState<string>('panel1');
  const { confirmation, showSuccess, showError, hideConfirmation } = useConfirmation();
  const [pendingPOs, setPendingPOs] = useState<PendingPO[]>([]);
  const [warehouses, setWarehouses] = useState<Warehouse[]>([]);
  const [selectedPO, setSelectedPO] = useState<any>(null);
  const [loadingPOs, setLoadingPOs] = useState(false);
  const [loadingWarehouses, setLoadingWarehouses] = useState(false);
  const [products, setProducts] = useState<Product[]>([]);
  const [loadingProducts, setLoadingProducts] = useState(false);
  const { user } = useAuth();

  const isEditMode = Boolean(initialValues?.grn_id);

  // Load data
  useEffect(() => {
    const loadData = async () => {
      try {
        console.log('🔍 Loading GRN form data...');
        setLoadingPOs(true);
        setLoadingWarehouses(true);
        
        const loadProducts = async () => {
          try {
            setLoadingProducts(true);
            
            // Load products from the new dropdown API endpoint
            const token = localStorage.getItem('token');
            const response = await fetch('http://localhost:8000/api/sales/products/dropdown/', {
              headers: {
                'Authorization': `Token ${token}`,
                'Content-Type': 'application/json',
              },
            });

            if (!response.ok) {
              throw new Error('Failed to fetch products');
            }

            const productList = await response.json();
            console.log('🛍️ Products loaded for GRN from dropdown:', productList.length);
            setProducts(productList);
          } catch (err) {
            console.error('Failed to load products for GRN:', err);
            setProducts([]); // Ensure products is always an array
          } finally {
            setLoadingProducts(false);
          }
        };

        const [posData, warehousesData] = await Promise.all([
          grnService.getPendingPOs(),
          inventoryService.getAllWarehouses(),
          loadProducts(),
        ]);
        
        console.log('📦 Pending POs loaded:', posData);
        console.log('🏢 Warehouses loaded:', warehousesData);
        
        setPendingPOs(posData);
        setWarehouses(warehousesData);

        // Debug information
        if (posData.length === 0) {
          console.warn('⚠️ No pending Purchase Orders found. Cannot create GRN without PO.');
        }
        if (warehousesData.length === 0) {
          console.warn('⚠️ No warehouses found. Cannot create GRN without warehouse.');
        }
        
      } catch (err) {
        console.error('❌ Failed to load GRN form data:', err);
        setError(err instanceof Error ? err.message : 'Failed to load data');
      } finally {
        setLoadingPOs(false);
        setLoadingWarehouses(false);
      }
    };

    loadData();
  }, []);

  const formik = useFormik<GRNFormData>({
    initialValues: {
      purchase_order: initialValues?.purchase_order || '',
      warehouse: initialValues?.warehouse || '',
      receipt_date: initialValues?.receipt_date || new Date().toISOString().split('T')[0],
      notes: initialValues?.notes || '',
      items: initialValues?.items || [],
    },
    validationSchema,
    onSubmit: async (values) => {
      try {
        console.log('📝 Submitting GRN form with values:', values);
        setLoading(true);
        setError(null);

        // Validate and clean the data before sending
        const cleanedValues = {
          purchase_order: parseInt(String(values.purchase_order)),
          warehouse: parseInt(String(values.warehouse)),
          receipt_date: values.receipt_date,
          notes: values.notes || '',
          items: values.items.map((item, index) => {
            // SAFE PRODUCT ID FILTERING - Ensure only valid numbers or null reach backend
            let productId: number | null = null;
            
            console.log(`🔍 Processing item ${index + 1}:`, {
              product_field: item.product,
              product_type: typeof item.product,
              product_name: item.product_name
            });
            
            // Only process if product field has a value
            if (item.product != null && item.product !== 0) {
              // If it's already a number, validate it
              if (typeof item.product === 'number' && Number.isInteger(item.product) && item.product > 0) {
                productId = item.product;
                console.log(`✅ Valid numeric product ID:`, productId);
              } 
              // If it's a string that could be a number, try to parse it
              else if (typeof item.product === 'string') {
                const productStr = item.product as string;
                const trimmed = productStr.trim();
                const parsed = parseInt(trimmed, 10);
                
                // Only accept if it's a valid positive integer and the conversion is exact
                if (!isNaN(parsed) && parsed > 0 && parsed.toString() === trimmed) {
                  productId = parsed;
                  console.log(`✅ Converted string "${trimmed}" to valid product ID:`, productId);
                } else {
                  console.warn(`⚠️ Rejecting invalid string product ID: "${item.product}" for item "${item.product_name}"`);
                  productId = null; // Force to null - will be description-only
                }
              }
              // Any other type, reject
              else {
                console.warn(`⚠️ Rejecting invalid product type: ${typeof item.product} for item "${item.product_name}"`);
                productId = null;
              }
            }
            
            let unitCost = parseFloat(String(item.unit_cost)) || 0;
            const quantityReceived = parseFloat(String(item.quantity_received)) || 0;
            const totalCost = quantityReceived * unitCost;
            
            const processedItem = {
              id: item.id, // Include ID if present
              product: productId, // Will be null or valid positive integer
              product_name: item.product_name || 'Unknown Item',
              quantity_ordered: parseFloat(String(item.quantity_ordered)) || 0,
              quantity_received: quantityReceived,
              unit_cost: unitCost,
              total_cost: totalCost,
              condition: item.condition,
              batch_number: item.batch_number || '',
              notes: item.notes || '',
              line_order: index,
              po_line_item_id: item.po_line_item_id, // Track PO line item reference
            };
            
            console.log(`✅ Final processed item ${index + 1}:`, {
              product: processedItem.product,
              product_type: typeof processedItem.product,
              product_name: processedItem.product_name
            });
            
            return processedItem;
          })
        };

        console.log('🧹 Cleaned data for submission:', cleanedValues);

        // FINAL SAFETY CHECK - Ensure no string product IDs reach backend
        console.log('🔒 Final validation of product IDs before submission...');
        for (let i = 0; i < cleanedValues.items.length; i++) {
          const item = cleanedValues.items[i];
          if (item.product !== null && (typeof item.product !== 'number' || item.product <= 0)) {
            console.error(`❌ CRITICAL: Invalid product ID detected in final submission:`, {
              item_index: i,
              product_value: item.product,
              product_type: typeof item.product,
              item_name: item.product_name
            });
            // Force to null as absolute last resort
            item.product = null;
            console.log(`🔧 Forced invalid product ID to null for item: ${item.product_name}`);
          }
        }

        // Validate required fields
        if (!cleanedValues.purchase_order || cleanedValues.purchase_order === 0) {
          throw new Error('Purchase Order is required');
        }
        if (!cleanedValues.warehouse || cleanedValues.warehouse === 0) {
          throw new Error('Warehouse is required');
        }
        if (!cleanedValues.receipt_date) {
          throw new Error('Receipt Date is required');
        }
        if (!cleanedValues.items || cleanedValues.items.length === 0) {
          throw new Error('At least one item is required');
        }

        // Validate each item
        for (let i = 0; i < cleanedValues.items.length; i++) {
          const item = cleanedValues.items[i];
          if (item.quantity_received < 0) {
            throw new Error(`Item ${i + 1}: Quantity received cannot be negative`);
          }
        }

        console.log('✅ Final validation passed. Submitting to backend...');

        if (isEditMode) {
          console.log('✏️ Updating existing GRN:', initialValues.grn_id);
          await grnService.updateGRN(initialValues.grn_id, cleanedValues);
          showSuccess(
            'GRN Updated!',
            `GRN ${cleanedValues.grn_number} has been updated successfully.`
          );
        } else {
          console.log('🆕 Creating new GRN...');
          const result = await grnService.createGRN(cleanedValues);
          console.log('✅ GRN created successfully:', result);
          showSuccess(
            'GRN Created!',
            `GRN ${cleanedValues.grn_number} has been created successfully.`
          );
        }

        // Close after a short delay to show the success message
        setTimeout(() => {
          onSave();
          onClose();
        }, 1500);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to save GRN';
        console.error('❌ Failed to save GRN:', err);
        setError(errorMessage);
        showError(
          'Save Failed',
          errorMessage
        );
      } finally {
        setLoading(false);
      }
    },
  });

  const handleAccordionChange = (panel: string) => (
    event: React.SyntheticEvent,
    isExpanded: boolean
  ) => {
    setExpanded(isExpanded ? panel : '');
  };

  const handlePOChange = async (event: any) => {
    const poId = event.target.value;
    formik.setFieldValue('purchase_order', poId);
    
    if (poId) {
      try {
        // Load PO details to populate items
        const po = await purchaseOrderService.getPurchaseOrder(poId);
        console.log('📦 ===== COMPLETE PO DATA STRUCTURE =====');
        console.log('📦 Full PO Object:', JSON.stringify(po, null, 2));
        console.log('📦 PO ID:', po.id);
        console.log('📦 PO Number:', po.po_number);
        console.log('📦 PO Date:', po.po_date);
        console.log('📦 Vendor:', po.vendor, po.vendor_name);
        console.log('📦 Status:', po.status);
        console.log('📦 Total Amount:', po.total_amount);
        console.log('📦 Line Items Array:', po.line_items);
        console.log('📦 Line Items Length:', po.line_items?.length);
        console.log('📦 ===== END PO DATA STRUCTURE =====');
        
        if (po.line_items && po.line_items.length > 0) {
          console.log('📦 ===== LINE ITEMS ANALYSIS =====');
          po.line_items.forEach((item: any, index: number) => {
            console.log(`📦 Line Item ${index + 1}:`, JSON.stringify(item, null, 2));
            console.log(`📦 Line Item ${index + 1} Keys:`, Object.keys(item));
          });
          console.log('📦 ===== END LINE ITEMS ANALYSIS =====');
        }
        
        setSelectedPO(po);
        
        // Convert PO line items to GRN items
        // Get the detailed PO data with remaining quantities
        const poData = pendingPOs.find(p => p.po_id === po.id);
        const lineItemsWithRemaining = poData?.line_items || [];
        
        const grnItems: GRNItem[] = (po.line_items || []).map((item: any, index: number) => {
          console.log('🔍 Processing PO line item:', item);
          console.log('🔍 Item product field:', item.product);
          console.log('🔍 Item product_id field:', item.product_id);
          console.log('🔍 Item all fields:', Object.keys(item));
          
          // Try to find the product ID in various possible field names
          let productId = null;
          
          // Check multiple possible field names for product ID
          if (item.product_id && item.product_id !== null && item.product_id !== 0) {
            // Parse product ID and validate it's a number
            const parsedId = parseInt(String(item.product_id));
            if (!isNaN(parsedId) && parsedId > 0) {
              productId = parsedId;
              console.log(`✅ Found valid product ID in 'product_id' field:`, productId);
            } else {
              console.warn(`⚠️ Invalid product_id value (not a number):`, item.product_id);
            }
          } else if (item.product && item.product !== null && item.product !== 0) {
            // Parse product ID and validate it's a number
            const parsedId = parseInt(String(item.product));
            if (!isNaN(parsedId) && parsedId > 0) {
              productId = parsedId;
              console.log(`✅ Found valid product ID in 'product' field:`, productId);
            } else {
              console.warn(`⚠️ Invalid product value (not a number):`, item.product);
            }
          }
          
          // If we couldn't find a valid numeric product ID, set to null
          if (!productId) {
            console.warn('⚠️ PO line item has no valid numeric product ID - will require manual selection:', {
              line_item_id: item.id,
              product_field: item.product,
              product_id_field: item.product_id,
              description: item.description,
              product_name: item.product_name
            });
            productId = null;
          }
          
          const unitCost = parseFloat(String(item.unit_price || item.price)) || 0;
          
          // Find remaining quantity for this specific line item
          const lineItemData = lineItemsWithRemaining.find(li => li.id === item.id);
          const quantityRemaining = lineItemData?.quantity_remaining || 0;
          const quantityReceived = quantityRemaining; // Pre-fill with remaining quantity, not full quantity
          
          const processedItem = {
            id: Date.now() + index, // Generate temporary ID for new items
            product: productId, // Use null if no valid product ID found
            product_name: item.product_name || item.description || item.name || 'Unknown Item',
            quantity_ordered: parseFloat(String(item.quantity)) || 0,
            quantity_received: quantityReceived,
            unit_cost: unitCost,
            total_cost: quantityReceived * unitCost,
            po_unit_price: parseFloat(String(item.unit_price || item.price)) || 0, // Store original PO price
            condition: 'GOOD' as const,
            batch_number: '',
            notes: '',
            line_order: index,
            po_line_item_id: item.id, // Track which PO line item this relates to
          };
          console.log('✅ Processed GRN item:', processedItem);
          return processedItem;
        });
        
        console.log('📋 Final GRN items:', grnItems);
        formik.setFieldValue('items', grnItems);
        
        // Trigger validation after setting items
        setTimeout(() => {
          formik.validateForm();
        }, 100);
      } catch (err) {
        console.error('Failed to load PO details:', err);
        setError(err instanceof Error ? err.message : 'Failed to load Purchase Order details');
      }
    } else {
      setSelectedPO(null);
      formik.setFieldValue('items', []);
    }
  };

  const updateItemField = (index: number, field: string, value: any) => {
    const updatedItems = [...formik.values.items];
    updatedItems[index] = { ...updatedItems[index], [field]: value };
    
    // Recalculate total cost if quantity or unit cost changed
    if (field === 'quantity_received' || field === 'unit_cost') {
      updatedItems[index].total_cost = Number(updatedItems[index].quantity_received) * Number(updatedItems[index].unit_cost);
    }
    
    formik.setFieldValue('items', updatedItems);
  };

  const removeItem = (index: number) => {
    const newItems = formik.values.items.filter((_, i) => i !== index);
    formik.setFieldValue('items', newItems);
  };

  // Total value calculation removed - focusing on quantity audit only

  const calculateTotalQuantity = () => {
    if (!formik.values.items || formik.values.items.length === 0) {
      return 0;
    }
    
    return formik.values.items.reduce((total, item) => {
      const quantity = parseFloat(String(item.quantity_received)) || 0;
      return total + quantity;
    }, 0);
  };

  const handleProductSelect = (index: number, product: Product | null) => {
    if (product) {
      console.log('🔍 PRODUCT SELECTION DEBUG:', {
        product_object: product,
        product_id: product.id,
        product_id_type: typeof product.id,
        product_name: product.name
      });
      
      // Ensure product.id is a valid number
      const productId = parseInt(String(product.id));
      if (isNaN(productId) || productId <= 0) {
        console.error('❌ Invalid product ID from selection:', product.id);
        alert(`Error: Selected product has invalid ID "${product.id}". Please contact support.`);
        return;
      }
      
      // Update the item with the selected product
      const updatedItems = [...formik.values.items];
      updatedItems[index] = {
        ...updatedItems[index],
        product: productId, // Use validated numeric ID
        product_name: product.name,
        unit_cost: typeof product.cost_price === 'string' ? parseFloat(product.cost_price) : (product.cost_price || 0),
      };
      formik.setFieldValue('items', updatedItems);
      console.log('✅ Product linked to GRN item:', product.name, 'ID:', productId);
    }
  };

  return (
    <>
      <DialogTitle sx={{ borderBottom: '1px solid rgba(0, 0, 0, 0.12)', pb: 2 }}>
        {isEditMode ? 'Edit Goods Receipt Note' : 'New Goods Receipt Note'}
      </DialogTitle>

      <form onSubmit={formik.handleSubmit}>
        <DialogContent dividers sx={{ p: 0 }}>
          {error && (
            <Alert severity="error" sx={{ m: 3, mb: 0 }}>
              {error}
            </Alert>
          )}

          {/* Current User Info */}
          <Card sx={{ m: 3, mb: 0 }}>
            <CardContent sx={{ py: 2 }}>
              <Box display="flex" alignItems="center" gap={2}>
                <PersonIcon color="primary" />
                <Box>
                  <Typography variant="subtitle2" color="primary">
                    Receiving Staff
                  </Typography>
                  <Typography variant="body1" fontWeight="medium">
                    {user?.first_name && user?.last_name 
                      ? `${user.first_name} ${user.last_name}` 
                      : user?.username || 'Unknown User'}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    This person will be recorded as the goods receiver
                  </Typography>
                </Box>
                <Box sx={{ ml: 'auto' }}>
                  <Chip 
                    label={user?.role || 'Staff'} 
                    size="small" 
                    color="primary" 
                    variant="outlined" 
                  />
                </Box>
              </Box>
            </CardContent>
          </Card>

          {/* Basic Information */}
          <Accordion
            expanded={expanded === 'panel1'}
            onChange={handleAccordionChange('panel1')}
          >
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Box display="flex" alignItems="center" gap={1}>
                <ReceiptIcon />
                <Typography variant="subtitle1" fontWeight="bold">
                  GRN Details
                </Typography>
              </Box>
            </AccordionSummary>
            <AccordionDetails>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth>
                    <InputLabel>Purchase Order *</InputLabel>
                    <Select
                      id="purchase_order"
                      name="purchase_order"
                      value={formik.values.purchase_order}
                      onChange={handlePOChange}
                      label="Purchase Order *"
                      error={formik.touched.purchase_order && Boolean(formik.errors.purchase_order)}
                      disabled={loadingPOs || isEditMode}
                    >
                      {loadingPOs ? (
                        <MenuItem disabled>
                          <em>Loading Purchase Orders...</em>
                        </MenuItem>
                      ) : pendingPOs.length === 0 ? (
                        <MenuItem disabled>
                          <em>No Purchase Orders available for GRN creation</em>
                        </MenuItem>
                      ) : (
                        pendingPOs.map((po) => (
                          <MenuItem key={po.po_id} value={po.po_id}>
                            <Box>
                              <Typography variant="body2" fontWeight="medium">
                                {po.po_number} - {po.vendor_name}
                              </Typography>
                              <Typography variant="caption" color="text.secondary">
                                Date: {new Date(po.po_date).toLocaleDateString()} | 
                                Amount: ₹{po.total_amount.toLocaleString()} | 
                                Items: {po.line_items_count}
                              </Typography>
                              <Typography variant="caption" color="primary.main" display="block">
                                {po.existing_grns_count > 0 ? 
                                  `⚠️ Partial: ${po.remaining_quantity} units remaining` : 
                                  `✅ Ready: ${po.total_ordered} units to receive`
                                }
                              </Typography>
                            </Box>
                          </MenuItem>
                        ))
                      )}
                    </Select>
                  </FormControl>
                </Grid>
                
                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth>
                    <InputLabel>Warehouse *</InputLabel>
                    <Select
                      id="warehouse"
                      name="warehouse"
                      value={formik.values.warehouse}
                      onChange={formik.handleChange}
                      label="Warehouse *"
                      error={formik.touched.warehouse && Boolean(formik.errors.warehouse)}
                      disabled={loadingWarehouses}
                    >
                      {warehouses.map((warehouse) => (
                        <MenuItem key={warehouse.warehouse_id} value={warehouse.warehouse_id}>
                          {warehouse.name} ({warehouse.code})
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>
                
                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    id="receipt_date"
                    name="receipt_date"
                    label="Receipt Date *"
                    type="date"
                    value={formik.values.receipt_date}
                    onChange={formik.handleChange}
                    error={formik.touched.receipt_date && Boolean(formik.errors.receipt_date)}
                    helperText={formik.touched.receipt_date && formik.errors.receipt_date}
                    InputLabelProps={{ shrink: true }}
                  />
                </Grid>
                
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    id="notes"
                    name="notes"
                    label="Notes"
                    multiline
                    rows={3}
                    value={formik.values.notes}
                    onChange={formik.handleChange}
                  />
                </Grid>
              </Grid>
            </AccordionDetails>
          </Accordion>

          {/* Items */}
          <Accordion
            expanded={expanded === 'panel2'}
            onChange={handleAccordionChange('panel2')}
          >
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Box display="flex" alignItems="center" gap={1}>
                <InventoryIcon />
                <Typography variant="subtitle1" fontWeight="bold">
                  Items ({formik.values.items.length})
                </Typography>
              </Box>
            </AccordionSummary>
            <AccordionDetails>
              {formik.values.items.length > 0 ? (
                <>
                  <Alert severity="info" sx={{ mb: 2 }}>
                    <Typography variant="body2">
                      <strong>🔗 Product Linking (Optional):</strong> Link items to inventory products for automatic stock updates.
                      <br />
                      <strong>📋 Goods Receipt:</strong> Enter the quantities you actually received for each item.
                      <br />
                      <strong>✅ Flexible:</strong> Items without product links can still be received as description-only entries.
                      <br />
                      <strong>🔧 Post-Processing:</strong> You can link products later if needed.
                    </Typography>
                  </Alert>
                  
                  {/* Show PO receipt status - simplified and clear */}
                  {selectedPO && pendingPOs.find(po => po.po_id === selectedPO.id) && (() => {
                    const poData = pendingPOs.find(po => po.po_id === selectedPO.id);
                    const hasExistingGRNs = (poData?.existing_grns_count || 0) > 0;
                    const remainingQty = poData?.remaining_quantity || 0;
                    
                    return (
                      <>
                        <Alert 
                          severity={hasExistingGRNs ? "warning" : "info"} 
                          sx={{ mb: 2 }}
                        >
                          <Typography variant="body2">
                            {hasExistingGRNs ? (
                              <>
                                <strong>⚠️ Partial Receipt:</strong> This PO has {poData?.existing_grns_count} existing GRN(s).
                                <br />
                                <strong>📊 You can receive:</strong> {remainingQty > 0 ? `${remainingQty} more units` : 'No more units (fully received)'}
                              </>
                            ) : (
                              <>
                                <strong>✅ New Receipt:</strong> No previous GRNs for this PO.
                                <br />
                                <strong>📦 Total to receive:</strong> {poData?.total_ordered} units
                              </>
                            )}
                          </Typography>
                        </Alert>

                        {/* Product-wise Remaining Quantities */}
                        {poData?.line_items && poData.line_items.length > 0 && (
                          <Card sx={{ mb: 2 }}>
                            <CardContent>
                              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                <InventoryIcon color="primary" />
                                Products Available for Receipt
                              </Typography>
                              <TableContainer>
                                <Table size="small">
                                  <TableHead>
                                    <TableRow>
                                      <TableCell><strong>Product</strong></TableCell>
                                      <TableCell align="right"><strong>Ordered</strong></TableCell>
                                      <TableCell align="right"><strong>Received</strong></TableCell>
                                      <TableCell align="right"><strong>Remaining</strong></TableCell>
                                      <TableCell align="center"><strong>Status</strong></TableCell>
                                    </TableRow>
                                  </TableHead>
                                  <TableBody>
                                    {poData.line_items.map((lineItem) => (
                                      <TableRow 
                                        key={lineItem.id}
                                        sx={{ 
                                          bgcolor: lineItem.has_remaining ? 'success.light' : 'grey.100',
                                          opacity: lineItem.has_remaining ? 1 : 0.6
                                        }}
                                      >
                                        <TableCell>
                                          <Box>
                                            <Typography variant="body2" fontWeight="medium">
                                              {lineItem.product_name}
                                            </Typography>
                                            {lineItem.product_sku && (
                                              <Typography variant="caption" color="text.secondary">
                                                SKU: {lineItem.product_sku}
                                              </Typography>
                                            )}
                                          </Box>
                                        </TableCell>
                                        <TableCell align="right">
                                          <Typography variant="body2">
                                            {lineItem.quantity_ordered.toFixed(2)} {lineItem.unit_of_measure}
                                          </Typography>
                                        </TableCell>
                                        <TableCell align="right">
                                          <Typography variant="body2" color="text.secondary">
                                            {lineItem.quantity_received.toFixed(2)} {lineItem.unit_of_measure}
                                          </Typography>
                                        </TableCell>
                                        <TableCell align="right">
                                          <Typography 
                                            variant="body2" 
                                            fontWeight="bold"
                                            color={lineItem.has_remaining ? 'success.main' : 'text.secondary'}
                                          >
                                            {lineItem.quantity_remaining.toFixed(2)} {lineItem.unit_of_measure}
                                          </Typography>
                                        </TableCell>
                                        <TableCell align="center">
                                          {lineItem.has_remaining ? (
                                            <Chip 
                                              label="Available" 
                                              color="success" 
                                              size="small" 
                                              variant="outlined"
                                            />
                                          ) : (
                                            <Chip 
                                              label="Complete" 
                                              color="default" 
                                              size="small"
                                              variant="outlined"
                                            />
                                          )}
                                        </TableCell>
                                      </TableRow>
                                    ))}
                                  </TableBody>
                                </Table>
                              </TableContainer>
                              
                              {/* Summary */}
                              <Box sx={{ mt: 2, p: 1, bgcolor: 'info.light', borderRadius: 1 }}>
                                <Typography variant="body2" color="info.dark">
                                  <strong>💡 Tip:</strong> Only products with remaining quantities can be received. 
                                  The GRN items below are pre-filled with remaining quantities - adjust as needed.
                                </Typography>
                              </Box>
                            </CardContent>
                          </Card>
                        )}
                      </>
                    );
                  })()}
                <TableContainer component={Paper} variant="outlined">
                  <Table size="small">
                    <TableHead>
                      <TableRow>
                          <TableCell>Product Link</TableCell>
                          <TableCell>Item Description</TableCell>
                          <TableCell>Ordered Qty</TableCell>
                          <TableCell>Remaining Qty</TableCell>
                          <TableCell>Received Qty *</TableCell>
                        <TableCell>Condition *</TableCell>
                        <TableCell>Batch No.</TableCell>
                        <TableCell>Actions</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {formik.values.items.map((item, index) => (
                        <TableRow key={index}>
                          <TableCell>
                            <Autocomplete
                              size="small"
                              options={products}
                              getOptionLabel={(option) => `${option.sku} - ${option.name}`}
                              value={products.find(p => p.id === item.product) || null}
                              onChange={(_, newValue) => handleProductSelect(index, newValue)}
                              loading={loadingProducts}
                              sx={{ width: 200 }}
                              renderInput={(params) => (
                                <TextField
                                  {...params}
                                  placeholder="🔍 Link product..."
                                  helperText={
                                    item.product && typeof item.product === 'number' && item.product > 0 
                                      ? `✅ Linked (ID: ${item.product})` 
                                      : item.product && typeof item.product === 'string'
                                        ? `❌ Invalid: "${item.product}"`
                                        : "💡 Optional - Link for inventory tracking"
                                  }
                                  error={!!(item.product && typeof item.product === 'string')}
                                  InputProps={{
                                    ...params.InputProps,
                                    endAdornment: (
                                      <>
                                        {loadingProducts ? <CircularProgress color="inherit" size={20} /> : null}
                                        {params.InputProps.endAdornment}
                                      </>
                                    ),
                                  }}
                                />
                              )}
                              renderOption={(props, option) => (
                                <Box component="li" {...props}>
                                  <Box>
                                    <Typography variant="body2" fontWeight="medium">
                                      {option.sku} - {option.name}
                                    </Typography>
                                    <Typography variant="caption" color="text.secondary">
                                      Cost: ₹{(() => {
                                        const costPrice = typeof option.cost_price === 'string' ? parseFloat(option.cost_price) : (option.cost_price || 0);
                                        const unitPrice = typeof option.unit_price === 'string' ? parseFloat(option.unit_price) : (option.unit_price || 0);
                                        return Number(costPrice || unitPrice || 0).toFixed(2);
                                      })()}
                                    </Typography>
                                  </Box>
                                </Box>
                              )}
                            />
                          </TableCell>
                          <TableCell>
                              <Box>
                                <Typography variant="body2" fontWeight="medium">
                                  {item.product_name || 'Unnamed Item'}
                                </Typography>
                                {item.product && typeof item.product === 'number' && item.product > 0 ? (
                                  <Typography variant="caption" color="success.main">
                                    ✅ Linked to Product ID: {item.product}
                                  </Typography>
                                ) : item.product && typeof item.product === 'string' ? (
                                  <Typography variant="caption" color="error.main">
                                    ❌ Invalid product ID: "{item.product}" - Please reselect
                            </Typography>
                                ) : (
                                  <Typography variant="caption" color="info.main">
                                    💡 Description-only item (no inventory tracking)
                            </Typography>
                                )}
                              </Box>
                          </TableCell>
                          <TableCell>
                              <Typography variant="body2" color="text.secondary">
                                {item.quantity_ordered || 0}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            {(() => {
                              // Find remaining quantity for this item
                              const poData = pendingPOs.find(po => po.po_id === formik.values.purchase_order);
                              const lineItemData = poData?.line_items?.find(li => li.id === item.po_line_item_id);
                              const remainingQty = lineItemData?.quantity_remaining || 0;
                              return (
                                <Typography 
                                  variant="body2" 
                                  fontWeight="bold"
                                  color={remainingQty > 0 ? 'success.main' : 'text.secondary'}
                                >
                                  {remainingQty.toFixed(2)}
                                </Typography>
                              );
                            })()}
                          </TableCell>
                          <TableCell>
                            <QuantityInput
                              size="small"
                              value={item.quantity_received}
                              onChange={(e) => updateItemField(index, 'quantity_received', parseFloat(e.target.value) || 0)}
                              min={0}
                              sx={{ width: 120 }}
                            />
                          </TableCell>
                          <TableCell>
                            <Select
                              size="small"
                              value={item.condition}
                              onChange={(e) => updateItemField(index, 'condition', e.target.value)}
                              sx={{ width: 120 }}
                            >
                              <MenuItem value="GOOD">Good</MenuItem>
                              <MenuItem value="DAMAGED">Damaged</MenuItem>
                              <MenuItem value="EXPIRED">Expired</MenuItem>
                              <MenuItem value="REJECTED">Rejected</MenuItem>
                            </Select>
                          </TableCell>
                          <TableCell>
                            <TextField
                              size="small"
                              value={item.batch_number || ''}
                              onChange={(e) => updateItemField(index, 'batch_number', e.target.value)}
                                sx={{ width: 120 }}
                                placeholder="Optional"
                            />
                          </TableCell>
                          <TableCell>
                            <IconButton
                              size="small"
                              onClick={() => removeItem(index)}
                              disabled={formik.values.items.length === 1}
                            >
                              <DeleteIcon />
                            </IconButton>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
                </>
              ) : (
                <Alert severity="info">
                  Select a Purchase Order to load items for receipt.
                </Alert>
              )}
              
              {formik.values.items.length > 0 && (
                <Box sx={{ mt: 2, p: 2, backgroundColor: 'grey.50', borderRadius: 1 }}>
                  <Typography variant="h6" gutterBottom>
                    📊 Receipt Summary
                  </Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={12} sm={6}>
                      <Paper sx={{ p: 2 }}>
                        <Typography variant="subtitle2" color="text.secondary">
                          📋 Ordered Total
                        </Typography>
                        <Typography variant="h6" color="primary">
                          {formik.values.items.reduce((sum, item) => sum + (parseFloat(String(item.quantity_ordered)) || 0), 0).toFixed(2)} units
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          From Purchase Order
                        </Typography>
                      </Paper>
                    </Grid>
                    <Grid item xs={12} sm={6}>
                      <Paper sx={{ p: 2 }}>
                        <Typography variant="subtitle2" color="primary">
                          📦 Received Total
                        </Typography>
                        <Typography variant="h6" color="success.main">
                          {(calculateTotalQuantity() || 0).toFixed(2)} units
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Actually received
                  </Typography>
                      </Paper>
                    </Grid>
                  </Grid>
                </Box>
              )}
            </AccordionDetails>
          </Accordion>
        </DialogContent>

        <DialogActions sx={{ px: 3, py: 2 }}>
          <Button onClick={onClose} disabled={loading}>
            Cancel
          </Button>
          <Button
            type="submit"
            variant="contained"
            disabled={loading || !formik.isValid || formik.values.items.length === 0}
            startIcon={loading ? <CircularProgress size={20} /> : null}
          >
            {loading ? 'Saving...' : isEditMode ? 'Update GRN' : 'Create GRN'}
          </Button>
        </DialogActions>
      </form>

      {/* Confirmation Dialog */}
      <ConfirmationDialog
        open={confirmation.open}
        onClose={hideConfirmation}
        onConfirm={confirmation.onConfirm}
        title={confirmation.title}
        message={confirmation.message}
        type={confirmation.type}
        confirmText={confirmation.confirmText}
        cancelText={confirmation.cancelText}
        showCancel={confirmation.showCancel}
        confirmColor={confirmation.confirmColor}
        autoClose={confirmation.autoClose}
        autoCloseDelay={confirmation.autoCloseDelay}
      />
    </>
  );
};

export default GRNForm; 