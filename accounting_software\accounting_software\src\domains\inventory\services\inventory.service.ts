// API Configuration
const API_BASE_URL = 'http://localhost:8000';

export interface InventoryItem {
  inventory_id: number;
  product: number;
  product_name: string;
  product_sku: string;
  warehouse: number;
  warehouse_name: string;
  location?: number;
  location_code?: string;
  quantity_on_hand: number;
  quantity_reserved: number;
  quantity_allocated: number;
  quantity_available: number;
  reorder_point: number;
  reorder_quantity: number;
  max_stock_level?: number;
  safety_stock: number;
  average_cost: number;
  last_cost: number;
  standard_cost: number;
  valuation_method: 'FIFO' | 'LIFO' | 'AVERAGE' | 'STANDARD';
  abc_classification?: 'A' | 'B' | 'C';
  is_below_reorder_point: boolean;
  is_out_of_stock: boolean;
  total_value_average: number;
  total_value_last: number;
  last_transaction_date?: string;
  last_count_date?: string;
  created_at: string;
  updated_at: string;
}

export interface InventoryStats {
  total_items: number;
  low_stock_items: number;
  out_of_stock_items: number;
  total_value: number;
}

export interface StockTransaction {
  stock_txn_id: number;
  product: number;
  product_name: string;
  product_sku: string;
  warehouse: number;
  warehouse_name: string;
  transaction_type: 'RECEIPT' | 'ISSUE' | 'TRANSFER' | 'ADJUSTMENT' | 'SALE' | 'RETURN' | 'DAMAGE' | 'OPENING';
  reference_type: 'GRN' | 'SO' | 'PO' | 'ADJ' | 'TRF' | 'RET' | 'OPEN';
  reference_id: number;
  quantity: number;
  unit_cost: number;
  total_cost: number;
  txn_date: string;
  description: string;
  batch_number?: string;
  created_at: string;
  created_by: number;
  created_by_name: string;
}

export interface Warehouse {
  warehouse_id: number;
  name: string;
  warehouse_code: string;
  warehouse_type: 'MAIN' | 'BRANCH' | 'TRANSIT' | 'VIRTUAL';
  location: string;
  address?: string;
  contact_person?: string;
  phone?: string;
  email?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  created_by: number;
  created_by_name?: string;
  // Enterprise fields
  locations?: WarehouseLocation[];
  total_locations?: number;
  active_locations?: number;
}

export interface WarehouseLocation {
  location_id: number;
  warehouse: number;
  warehouse_name: string;
  zone: string;
  aisle?: string;
  rack?: string;
  bin?: string;
  location_code: string;
  description?: string;
  location_type: 'STANDARD' | 'BULK' | 'PICKING' | 'RECEIVING' | 'SHIPPING' | 'QUARANTINE' | 'DAMAGED' | 'RETURNS';
  max_capacity?: number;
  current_utilization: number;
  current_utilization_percent: number;
  is_active: boolean;
  allow_mixed_products: boolean;
  created_at: string;
  updated_at: string;
}

export interface StockValuation {
  valuation_id: number;
  valuation_date: string;
  valuation_type: 'MONTHLY' | 'QUARTERLY' | 'ANNUAL' | 'AD_HOC';
  warehouse?: number;
  warehouse_name?: string;
  product_category?: string;
  status: 'CALCULATING' | 'COMPLETED' | 'ERROR';
  total_items: number;
  total_quantity: number;
  fifo_total_value: number;
  lifo_total_value: number;
  average_total_value: number;
  standard_total_value: number;
  created_by: number;
  created_by_name: string;
  created_at: string;
  completed_at?: string;
  items?: StockValuationItem[];
}

export interface StockValuationItem {
  valuation_item_id: number;
  valuation: number;
  inventory: number;
  product_name: string;
  product_sku: string;
  warehouse_name: string;
  quantity_on_hand: number;
  fifo_unit_cost: number;
  fifo_total_value: number;
  lifo_unit_cost: number;
  lifo_total_value: number;
  average_unit_cost: number;
  average_total_value: number;
  standard_unit_cost: number;
  standard_total_value: number;
}

export interface InventoryValuation {
  fifo_value: number;
  lifo_value: number;
  average_value: number;
  standard_value: number;
  current_value: number;
  fifo_unit_cost: number;
  lifo_unit_cost: number;
  average_unit_cost: number;
  standard_unit_cost: number;
}

export interface CostLayer {
  layer_id: number;
  inventory: number;
  cost_per_unit: number;
  quantity_received: number;
  remaining_quantity: number;
  total_value: number;
  received_date: string;
  reference_type: string;
  reference_id: number;
  is_depleted: boolean;
  created_at: string;
}

class InventoryService {
  private baseUrl = `${API_BASE_URL}/api/inventory`;
  private enterpriseUrl = `${API_BASE_URL}/api/inventory/enterprise`;

  // Enhanced Inventory Management
  async getEnhancedInventory(params?: {
    page?: number;
    search?: string;
    warehouse?: number;
    product?: number;
    location?: number;
    valuation_method?: string;
    abc_classification?: string;
    low_stock?: boolean;
    out_of_stock?: boolean;
    negative_stock?: boolean;
    page_size?: number;
  }) {
    const token = localStorage.getItem('token');
    const queryParams = new URLSearchParams();

    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.search) queryParams.append('search', params.search);
    if (params?.warehouse) queryParams.append('warehouse', params.warehouse.toString());
    if (params?.product) queryParams.append('product', params.product.toString());
    if (params?.location) queryParams.append('location', params.location.toString());
    if (params?.valuation_method) queryParams.append('valuation_method', params.valuation_method);
    if (params?.abc_classification) queryParams.append('abc_classification', params.abc_classification);
    if (params?.low_stock) queryParams.append('low_stock', 'true');
    if (params?.out_of_stock) queryParams.append('out_of_stock', 'true');
    if (params?.negative_stock) queryParams.append('negative_stock', 'true');
    if (params?.page_size) queryParams.append('page_size', params.page_size.toString());

    const response = await fetch(`${this.enterpriseUrl}/inventory/?${queryParams}`, {
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Failed to fetch enhanced inventory');
    }

    return response.json();
  }

  // Legacy Inventory Management (for backward compatibility)
  async getInventory(params?: {
    page?: number;
    search?: string;
    warehouse?: number;
    product?: number;
    low_stock?: boolean;
    out_of_stock?: boolean;
    page_size?: number;
  }) {
    const token = localStorage.getItem('token');
    const queryParams = new URLSearchParams();
    
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.search) queryParams.append('search', params.search);
    if (params?.warehouse) queryParams.append('warehouse', params.warehouse.toString());
    if (params?.product) queryParams.append('product', params.product.toString());
    if (params?.low_stock) queryParams.append('low_stock', 'true');
    if (params?.out_of_stock) queryParams.append('out_of_stock', 'true');
    if (params?.page_size) queryParams.append('page_size', params.page_size.toString());

    const response = await fetch(`${this.baseUrl}/inventory/?${queryParams}`, {
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Failed to fetch inventory');
    }

    return response.json();
  }

  async getInventoryStats(): Promise<InventoryStats> {
    const token = localStorage.getItem('token');
    const response = await fetch(`${this.baseUrl}/inventory/stats/`, {
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Failed to fetch inventory stats');
    }

    return response.json();
  }

  // Stock Transactions
  async getStockTransactions(params?: {
    page?: number;
    search?: string;
    transaction_type?: string;
    reference_type?: string;
    warehouse?: number;
    product?: number;
    start_date?: string;
    end_date?: string;
  }) {
    const token = localStorage.getItem('token');
    const queryParams = new URLSearchParams();
    
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.search) queryParams.append('search', params.search);
    if (params?.transaction_type) queryParams.append('transaction_type', params.transaction_type);
    if (params?.reference_type) queryParams.append('reference_type', params.reference_type);
    if (params?.warehouse) queryParams.append('warehouse', params.warehouse.toString());
    if (params?.product) queryParams.append('product', params.product.toString());
    if (params?.start_date) queryParams.append('start_date', params.start_date);
    if (params?.end_date) queryParams.append('end_date', params.end_date);

    const response = await fetch(`${this.baseUrl}/stock-transactions/?${queryParams}`, {
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Failed to fetch stock transactions');
    }

    return response.json();
  }

  async getAllStockTransactions(): Promise<StockTransaction[]> {
    const token = localStorage.getItem('token');
    const response = await fetch(`${this.baseUrl}/stock-transactions/?no_pagination=true`, {
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Failed to fetch all stock transactions');
    }

    const data = await response.json();
    // When no_pagination=true, the response is directly an array
    return Array.isArray(data) ? data : (data.results || []);
  }

  async getStockTransactionSummary() {
    const token = localStorage.getItem('token');
    const response = await fetch(`${this.baseUrl}/stock-transactions/summary/`, {
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Failed to fetch stock transaction summary');
    }

    return response.json();
  }

  // Warehouses
  async getWarehouses(params?: {
    page?: number;
    search?: string;
    is_active?: boolean;
  }) {
    const token = localStorage.getItem('token');
    const queryParams = new URLSearchParams();
    
    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.search) queryParams.append('search', params.search);
    if (params?.is_active !== undefined) queryParams.append('is_active', params.is_active.toString());

    const response = await fetch(`${this.baseUrl}/warehouses/?${queryParams}`, {
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Failed to fetch warehouses');
    }

    return response.json();
  }

  async getAllWarehouses(): Promise<Warehouse[]> {
    const token = localStorage.getItem('token');
    const response = await fetch(`${this.baseUrl}/warehouses/?is_active=true&page_size=100`, {
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Failed to fetch warehouses');
    }

    const data = await response.json();
    return data.results || data;
  }

  async createWarehouse(warehouseData: Partial<Warehouse>): Promise<Warehouse> {
    const token = localStorage.getItem('token');
    
    const response = await fetch(`${this.baseUrl}/warehouses/`, {
      method: 'POST',
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(warehouseData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Failed to create warehouse');
    }

    return response.json();
  }

  async updateWarehouse(id: number, warehouseData: Partial<Warehouse>): Promise<Warehouse> {
    const token = localStorage.getItem('token');
    
    const response = await fetch(`${this.baseUrl}/warehouses/${id}/`, {
      method: 'PATCH',
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(warehouseData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Failed to update warehouse');
    }

    return response.json();
  }

  async deleteWarehouse(id: number): Promise<void> {
    const token = localStorage.getItem('token');
    
    const response = await fetch(`${this.baseUrl}/warehouses/${id}/`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Failed to delete warehouse');
    }
  }

  // Enhanced Enterprise Warehouse Methods
  async getEnterpriseWarehouses(params?: {
    page?: number;
    search?: string;
    warehouse_type?: string;
    is_active?: boolean;
  }) {
    const token = localStorage.getItem('token');
    const queryParams = new URLSearchParams();

    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.search) queryParams.append('search', params.search);
    if (params?.warehouse_type) queryParams.append('warehouse_type', params.warehouse_type);
    if (params?.is_active !== undefined) queryParams.append('is_active', params.is_active.toString());

    const response = await fetch(`${this.enterpriseUrl}/warehouses/?${queryParams}`, {
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Failed to fetch enterprise warehouses');
    }

    return response.json();
  }

  async getWarehouseInventorySummary(warehouseId: number) {
    const token = localStorage.getItem('token');
    const response = await fetch(`${this.enterpriseUrl}/warehouses/${warehouseId}/inventory_summary/`, {
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Failed to fetch warehouse inventory summary');
    }

    return response.json();
  }

  async getWarehouseLocations(warehouseId: number) {
    const token = localStorage.getItem('token');
    const response = await fetch(`${this.enterpriseUrl}/warehouses/${warehouseId}/locations/`, {
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Failed to fetch warehouse locations');
    }

    return response.json();
  }

  // Warehouse Location Management
  async getAllWarehouseLocations(params?: {
    warehouse?: number;
    location_type?: string;
    is_active?: boolean;
    search?: string;
  }) {
    const token = localStorage.getItem('token');
    const queryParams = new URLSearchParams();

    if (params?.warehouse) queryParams.append('warehouse', params.warehouse.toString());
    if (params?.location_type) queryParams.append('location_type', params.location_type);
    if (params?.is_active !== undefined) queryParams.append('is_active', params.is_active.toString());
    if (params?.search) queryParams.append('search', params.search);

    const response = await fetch(`${this.enterpriseUrl}/locations/?${queryParams}`, {
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Failed to fetch warehouse locations');
    }

    return response.json();
  }

  async createWarehouseLocation(locationData: Partial<WarehouseLocation>): Promise<WarehouseLocation> {
    const token = localStorage.getItem('token');

    const response = await fetch(`${this.enterpriseUrl}/locations/`, {
      method: 'POST',
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(locationData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Failed to create warehouse location');
    }

    return response.json();
  }

  async updateWarehouseLocation(id: number, locationData: Partial<WarehouseLocation>): Promise<WarehouseLocation> {
    const token = localStorage.getItem('token');

    const response = await fetch(`${this.enterpriseUrl}/locations/${id}/`, {
      method: 'PATCH',
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(locationData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Failed to update warehouse location');
    }

    return response.json();
  }

  async deleteWarehouseLocation(id: number): Promise<void> {
    const token = localStorage.getItem('token');

    const response = await fetch(`${this.enterpriseUrl}/locations/${id}/`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Failed to delete warehouse location');
    }
  }

  // Enhanced Inventory Methods
  async getInventoryValuation(inventoryId: number): Promise<InventoryValuation> {
    const token = localStorage.getItem('token');
    const response = await fetch(`${this.enterpriseUrl}/inventory/${inventoryId}/valuation/`, {
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Failed to fetch inventory valuation');
    }

    return response.json();
  }

  async getInventoryCostLayers(inventoryId: number): Promise<CostLayer[]> {
    const token = localStorage.getItem('token');
    const response = await fetch(`${this.enterpriseUrl}/inventory/${inventoryId}/cost_layers/`, {
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Failed to fetch inventory cost layers');
    }

    return response.json();
  }

  // Stock Valuation Methods
  async getStockValuations(params?: {
    page?: number;
    search?: string;
    status?: string;
    valuation_type?: string;
    warehouse?: number;
    page_size?: number;
  }): Promise<{ results: StockValuation[]; count: number; next?: string; previous?: string }> {
    const token = localStorage.getItem('token');
    const queryParams = new URLSearchParams();

    if (params?.page) queryParams.append('page', params.page.toString());
    if (params?.search) queryParams.append('search', params.search);
    if (params?.status) queryParams.append('status', params.status);
    if (params?.valuation_type) queryParams.append('valuation_type', params.valuation_type);
    if (params?.warehouse) queryParams.append('warehouse', params.warehouse.toString());
    if (params?.page_size) queryParams.append('page_size', params.page_size.toString());

    const response = await fetch(`${this.enterpriseUrl}/valuations/?${queryParams}`, {
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Failed to fetch stock valuations');
    }

    return response.json();
  }

  async getStockValuation(valuationId: number): Promise<StockValuation> {
    const token = localStorage.getItem('token');
    const response = await fetch(`${this.enterpriseUrl}/valuations/${valuationId}/`, {
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Failed to fetch stock valuation');
    }

    return response.json();
  }

  async generateStockValuationReport(data: {
    valuation_date: string;
    valuation_type: string;
    warehouse_id?: number;
    product_category?: string;
  }): Promise<StockValuation> {
    const token = localStorage.getItem('token');
    const response = await fetch(`${this.enterpriseUrl}/valuations/generate_report/`, {
      method: 'POST',
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to generate valuation report');
    }

    return response.json();
  }

  async deleteStockValuation(valuationId: number): Promise<void> {
    const token = localStorage.getItem('token');
    const response = await fetch(`${this.enterpriseUrl}/valuations/${valuationId}/`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Failed to delete stock valuation');
    }
  }

  async createInventoryAlert(inventoryId: number) {
    const token = localStorage.getItem('token');
    const response = await fetch(`${this.enterpriseUrl}/inventory/${inventoryId}/create_alert/`, {
      method: 'POST',
      headers: {
        'Authorization': `Token ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Failed to create inventory alert');
    }

    return response.json();
  }
}

export const inventoryService = new InventoryService(); 