"""
Customer Invoice Admin - Sales Module
"""

from django.contrib import admin
from .customer_invoice_models import CustomerInvoice, CustomerInvoiceItem


class CustomerInvoiceItemInline(admin.TabularInline):
    model = CustomerInvoiceItem
    extra = 1
    fields = ['product', 'item_description', 'quantity', 'unit_price', 'discount_percent', 'line_total', 'tax_rate', 'tax_amount', 'account_code']
    readonly_fields = ['line_total', 'tax_amount']


@admin.register(CustomerInvoice)
class CustomerInvoiceAdmin(admin.ModelAdmin):
    list_display = ['invoice_number', 'customer', 'invoice_date', 'due_date', 'total_amount', 'amount_paid', 'balance_due', 'status']
    list_filter = ['status', 'invoice_date', 'due_date', 'created_at']
    search_fields = ['invoice_number', 'customer__display_name', 'reference_number']
    readonly_fields = ['invoice_number', 'subtotal', 'tax_amount', 'total_amount', 'balance_due', 'created_at', 'updated_at']
    inlines = [CustomerInvoiceItemInline]
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('invoice_number', 'customer', 'invoice_date', 'due_date', 'status')
        }),
        ('Document Links', {
            'fields': ('sales_order', 'delivery_note'),
            'classes': ('collapse',)
        }),
        ('Financial Information', {
            'fields': ('subtotal', 'discount_amount', 'shipping_amount', 'tax_amount', 'total_amount', 'amount_paid', 'balance_due')
        }),
        ('Additional Information', {
            'fields': ('payment_terms', 'reference_number', 'notes', 'terms_and_conditions'),
            'classes': ('collapse',)
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at', 'created_by'),
            'classes': ('collapse',)
        }),
    )


@admin.register(CustomerInvoiceItem)
class CustomerInvoiceItemAdmin(admin.ModelAdmin):
    list_display = ['invoice', 'product', 'item_description', 'quantity', 'unit_price', 'line_total', 'tax_amount']
    list_filter = ['invoice__status', 'product__product_type']
    search_fields = ['invoice__invoice_number', 'product__name', 'item_description']
    readonly_fields = ['line_total', 'tax_amount']
