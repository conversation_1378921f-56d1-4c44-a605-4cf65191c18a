# Generated by Django 4.2.21 on 2025-07-02 17:59

import datetime
from decimal import Decimal
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('sales', '0011_update_customer_references'),
        ('inventory', '0005_alter_goodsreturnnote_vendor'),
    ]

    operations = [
        migrations.CreateModel(
            name='InventoryCostLayer',
            fields=[
                ('cost_layer_id', models.AutoField(primary_key=True, serialize=False)),
                ('layer_date', models.DateTimeField(help_text='Date this layer was created')),
                ('reference_type', models.CharField(choices=[('GRN', 'Goods Receipt Note'), ('ADJ', 'Stock Adjustment'), ('TRF', 'Transfer In'), ('OPEN', 'Opening Balance'), ('PROD', 'Production')], max_length=20)),
                ('reference_id', models.PositiveIntegerField(help_text='ID of source document')),
                ('original_quantity', models.DecimalField(decimal_places=4, help_text='Original quantity in this layer', max_digits=15)),
                ('remaining_quantity', models.DecimalField(decimal_places=4, help_text='Remaining quantity in this layer', max_digits=15)),
                ('unit_cost', models.DecimalField(decimal_places=4, help_text='Cost per unit for this layer', max_digits=15)),
                ('batch_number', models.CharField(blank=True, max_length=50)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'inventory_cost_layers',
                'ordering': ['layer_date', 'cost_layer_id'],
            },
        ),
        migrations.CreateModel(
            name='LowStockAlert',
            fields=[
                ('alert_id', models.AutoField(primary_key=True, serialize=False)),
                ('alert_type', models.CharField(choices=[('LOW_STOCK', 'Low Stock'), ('OUT_OF_STOCK', 'Out of Stock'), ('OVERSTOCK', 'Overstock'), ('REORDER_POINT', 'Reorder Point Reached'), ('EXPIRY_WARNING', 'Expiry Warning')], max_length=20)),
                ('threshold_quantity', models.DecimalField(decimal_places=4, help_text='Threshold quantity that triggered alert', max_digits=15)),
                ('current_quantity', models.DecimalField(decimal_places=4, help_text='Current quantity when alert was created', max_digits=15)),
                ('status', models.CharField(choices=[('ACTIVE', 'Active'), ('ACKNOWLEDGED', 'Acknowledged'), ('RESOLVED', 'Resolved'), ('DISMISSED', 'Dismissed')], default='ACTIVE', max_length=20)),
                ('priority', models.CharField(choices=[('LOW', 'Low'), ('MEDIUM', 'Medium'), ('HIGH', 'High'), ('CRITICAL', 'Critical')], default='MEDIUM', max_length=10)),
                ('message', models.TextField(help_text='Alert message')),
                ('recommended_action', models.TextField(blank=True, help_text='Recommended action')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('acknowledged_at', models.DateTimeField(blank=True, null=True)),
                ('resolved_at', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'db_table': 'low_stock_alerts',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='StockAdjustment',
            fields=[
                ('adjustment_id', models.AutoField(primary_key=True, serialize=False)),
                ('adjustment_number', models.CharField(help_text='Adjustment reference number', max_length=50, unique=True)),
                ('adjustment_date', models.DateField(default=datetime.date.today)),
                ('adjustment_type', models.CharField(choices=[('PHYSICAL', 'Physical Count Adjustment'), ('DAMAGE', 'Damage/Loss Adjustment'), ('FOUND', 'Found Stock Adjustment'), ('WRITE_OFF', 'Write-off Adjustment'), ('REVALUATION', 'Cost Revaluation'), ('CORRECTION', 'Data Correction')], max_length=20)),
                ('reason_code', models.CharField(choices=[('CYCLE_COUNT', 'Cycle Count'), ('ANNUAL_COUNT', 'Annual Physical Count'), ('DAMAGED', 'Damaged Goods'), ('EXPIRED', 'Expired Products'), ('THEFT', 'Theft/Loss'), ('SYSTEM_ERROR', 'System Error'), ('OTHER', 'Other')], max_length=20)),
                ('description', models.TextField(help_text='Detailed description of adjustment')),
                ('status', models.CharField(choices=[('DRAFT', 'Draft'), ('PENDING', 'Pending Approval'), ('APPROVED', 'Approved'), ('POSTED', 'Posted to Inventory'), ('REJECTED', 'Rejected'), ('CANCELLED', 'Cancelled')], default='DRAFT', max_length=20)),
                ('total_adjustment_value', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Total financial impact of adjustment', max_digits=15)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('approved_at', models.DateTimeField(blank=True, null=True)),
                ('posted_at', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'db_table': 'stock_adjustments',
                'ordering': ['-adjustment_date', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='StockAdjustmentItem',
            fields=[
                ('adjustment_item_id', models.AutoField(primary_key=True, serialize=False)),
                ('system_quantity', models.DecimalField(decimal_places=4, help_text='System recorded quantity', max_digits=15)),
                ('physical_quantity', models.DecimalField(decimal_places=4, help_text='Actual physical quantity', max_digits=15)),
                ('adjustment_quantity', models.DecimalField(decimal_places=4, help_text='Adjustment quantity (physical - system)', max_digits=15)),
                ('unit_cost', models.DecimalField(decimal_places=4, help_text='Cost per unit for adjustment', max_digits=15)),
                ('adjustment_value', models.DecimalField(decimal_places=2, help_text='Total value of adjustment', max_digits=15)),
                ('batch_number', models.CharField(blank=True, max_length=50)),
                ('notes', models.TextField(blank=True)),
                ('line_order', models.PositiveIntegerField(default=0)),
            ],
            options={
                'db_table': 'stock_adjustment_items',
                'ordering': ['line_order'],
            },
        ),
        migrations.CreateModel(
            name='StockValuation',
            fields=[
                ('valuation_id', models.AutoField(primary_key=True, serialize=False)),
                ('valuation_date', models.DateField(help_text='Date of valuation')),
                ('valuation_type', models.CharField(choices=[('MONTHLY', 'Monthly Valuation'), ('QUARTERLY', 'Quarterly Valuation'), ('ANNUAL', 'Annual Valuation'), ('AD_HOC', 'Ad-hoc Valuation')], max_length=20)),
                ('product_category', models.CharField(blank=True, help_text='Specific product category (blank for all categories)', max_length=100)),
                ('fifo_total_value', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Total value using FIFO method', max_digits=15)),
                ('lifo_total_value', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Total value using LIFO method', max_digits=15)),
                ('average_total_value', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Total value using weighted average method', max_digits=15)),
                ('standard_total_value', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Total value using standard cost method', max_digits=15)),
                ('total_items', models.PositiveIntegerField(default=0, help_text='Number of different items')),
                ('total_quantity', models.DecimalField(decimal_places=4, default=Decimal('0.0000'), help_text='Total quantity across all items', max_digits=15)),
                ('status', models.CharField(choices=[('CALCULATING', 'Calculating'), ('COMPLETED', 'Completed'), ('ERROR', 'Error')], default='CALCULATING', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'db_table': 'stock_valuations',
                'ordering': ['-valuation_date', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='StockValuationItem',
            fields=[
                ('valuation_item_id', models.AutoField(primary_key=True, serialize=False)),
                ('quantity_on_hand', models.DecimalField(decimal_places=4, help_text='Quantity on hand at valuation date', max_digits=15)),
                ('fifo_unit_cost', models.DecimalField(decimal_places=4, default=Decimal('0.0000'), max_digits=15)),
                ('fifo_total_value', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=15)),
                ('lifo_unit_cost', models.DecimalField(decimal_places=4, default=Decimal('0.0000'), max_digits=15)),
                ('lifo_total_value', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=15)),
                ('average_unit_cost', models.DecimalField(decimal_places=4, default=Decimal('0.0000'), max_digits=15)),
                ('average_total_value', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=15)),
                ('standard_unit_cost', models.DecimalField(decimal_places=4, default=Decimal('0.0000'), max_digits=15)),
                ('standard_total_value', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=15)),
            ],
            options={
                'db_table': 'stock_valuation_items',
                'ordering': ['inventory__product__name'],
            },
        ),
        migrations.CreateModel(
            name='WarehouseLocation',
            fields=[
                ('location_id', models.AutoField(primary_key=True, serialize=False)),
                ('zone', models.CharField(help_text='Zone/Area (e.g., A, B, C)', max_length=50)),
                ('aisle', models.CharField(blank=True, help_text='Aisle number', max_length=50)),
                ('rack', models.CharField(blank=True, help_text='Rack/Shelf number', max_length=50)),
                ('bin', models.CharField(blank=True, help_text='Bin/Position number', max_length=50)),
                ('location_code', models.CharField(help_text='Full location code (e.g., A-01-R1-B05)', max_length=100)),
                ('description', models.CharField(blank=True, max_length=200)),
                ('location_type', models.CharField(choices=[('STANDARD', 'Standard Storage'), ('BULK', 'Bulk Storage'), ('PICKING', 'Picking Location'), ('RECEIVING', 'Receiving Area'), ('SHIPPING', 'Shipping Area'), ('QUARANTINE', 'Quarantine Area'), ('DAMAGED', 'Damaged Goods'), ('RETURNS', 'Returns Area')], default='STANDARD', max_length=20)),
                ('max_capacity', models.DecimalField(blank=True, decimal_places=4, help_text='Maximum capacity in base units', max_digits=15, null=True)),
                ('current_utilization', models.DecimalField(decimal_places=2, default=Decimal('0.00'), help_text='Current utilization percentage', max_digits=5)),
                ('is_active', models.BooleanField(default=True)),
                ('allow_mixed_products', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'warehouse_locations',
                'ordering': ['warehouse__name', 'location_code'],
            },
        ),
        migrations.AddField(
            model_name='inventory',
            name='abc_classification',
            field=models.CharField(blank=True, choices=[('A', 'Class A - High Value'), ('B', 'Class B - Medium Value'), ('C', 'Class C - Low Value')], max_length=1, null=True),
        ),
        migrations.AddField(
            model_name='inventory',
            name='last_count_date',
            field=models.DateTimeField(blank=True, help_text='Last physical count date', null=True),
        ),
        migrations.AddField(
            model_name='inventory',
            name='max_stock_level',
            field=models.DecimalField(blank=True, decimal_places=4, help_text='Maximum stock level', max_digits=15, null=True),
        ),
        migrations.AddField(
            model_name='inventory',
            name='quantity_allocated',
            field=models.DecimalField(decimal_places=4, default=Decimal('0.0000'), help_text='Quantity allocated to production orders', max_digits=15),
        ),
        migrations.AddField(
            model_name='inventory',
            name='safety_stock',
            field=models.DecimalField(decimal_places=4, default=Decimal('0.0000'), help_text='Safety stock buffer', max_digits=15),
        ),
        migrations.AddField(
            model_name='inventory',
            name='standard_cost',
            field=models.DecimalField(decimal_places=4, default=Decimal('0.0000'), help_text='Standard cost per unit', max_digits=15),
        ),
        migrations.AddField(
            model_name='inventory',
            name='valuation_method',
            field=models.CharField(choices=[('FIFO', 'First In, First Out'), ('LIFO', 'Last In, First Out'), ('AVERAGE', 'Weighted Average'), ('STANDARD', 'Standard Cost')], default='AVERAGE', max_length=20),
        ),
        migrations.AddIndex(
            model_name='inventory',
            index=models.Index(fields=['product', 'warehouse'], name='inventory_product_5c401c_idx'),
        ),
        migrations.AddIndex(
            model_name='inventory',
            index=models.Index(fields=['reorder_point'], name='inventory_reorder_5f3b79_idx'),
        ),
        migrations.AddIndex(
            model_name='inventory',
            index=models.Index(fields=['abc_classification'], name='inventory_abc_cla_3f65be_idx'),
        ),
        migrations.AddIndex(
            model_name='inventory',
            index=models.Index(fields=['last_transaction_date'], name='inventory_last_tr_dccdc9_idx'),
        ),
        migrations.AddField(
            model_name='warehouselocation',
            name='warehouse',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='locations', to='inventory.warehouse'),
        ),
        migrations.AddField(
            model_name='stockvaluationitem',
            name='inventory',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='valuation_items', to='inventory.inventory'),
        ),
        migrations.AddField(
            model_name='stockvaluationitem',
            name='valuation',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='inventory.stockvaluation'),
        ),
        migrations.AddField(
            model_name='stockvaluation',
            name='created_by',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='valuations_created', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='stockvaluation',
            name='warehouse',
            field=models.ForeignKey(blank=True, help_text='Specific warehouse (null for all warehouses)', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='valuations', to='inventory.warehouse'),
        ),
        migrations.AddField(
            model_name='stockadjustmentitem',
            name='adjustment',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='inventory.stockadjustment'),
        ),
        migrations.AddField(
            model_name='stockadjustmentitem',
            name='location',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='adjustment_items', to='inventory.warehouselocation'),
        ),
        migrations.AddField(
            model_name='stockadjustmentitem',
            name='product',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='adjustment_items', to='sales.product'),
        ),
        migrations.AddField(
            model_name='stockadjustmentitem',
            name='warehouse',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='adjustment_items', to='inventory.warehouse'),
        ),
        migrations.AddField(
            model_name='stockadjustment',
            name='approved_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='adjustments_approved', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='stockadjustment',
            name='created_by',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='adjustments_created', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='stockadjustment',
            name='posted_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='adjustments_posted', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='lowstockalert',
            name='acknowledged_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='alerts_acknowledged', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='lowstockalert',
            name='inventory',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='alerts', to='inventory.inventory'),
        ),
        migrations.AddField(
            model_name='lowstockalert',
            name='resolved_by',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='alerts_resolved', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='inventorycostlayer',
            name='inventory',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='cost_layers', to='inventory.inventory'),
        ),
        migrations.AlterUniqueTogether(
            name='inventory',
            unique_together=set(),
        ),
        migrations.AddField(
            model_name='inventory',
            name='location',
            field=models.ForeignKey(blank=True, help_text='Specific location within warehouse', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='inventory_items', to='inventory.warehouselocation'),
        ),
        migrations.AlterUniqueTogether(
            name='inventory',
            unique_together={('product', 'warehouse', 'location')},
        ),
        migrations.AddIndex(
            model_name='warehouselocation',
            index=models.Index(fields=['warehouse', 'zone'], name='warehouse_l_warehou_9c17ac_idx'),
        ),
        migrations.AddIndex(
            model_name='warehouselocation',
            index=models.Index(fields=['location_type'], name='warehouse_l_locatio_6097c4_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='warehouselocation',
            unique_together={('warehouse', 'location_code')},
        ),
        migrations.AddIndex(
            model_name='stockvaluationitem',
            index=models.Index(fields=['valuation', 'inventory'], name='stock_valua_valuati_5f242b_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='stockvaluationitem',
            unique_together={('valuation', 'inventory')},
        ),
        migrations.AddIndex(
            model_name='stockvaluation',
            index=models.Index(fields=['valuation_date'], name='stock_valua_valuati_5cc20a_idx'),
        ),
        migrations.AddIndex(
            model_name='stockvaluation',
            index=models.Index(fields=['warehouse'], name='stock_valua_warehou_ee3887_idx'),
        ),
        migrations.AddIndex(
            model_name='stockvaluation',
            index=models.Index(fields=['status'], name='stock_valua_status_9cb42f_idx'),
        ),
        migrations.AddIndex(
            model_name='stockadjustmentitem',
            index=models.Index(fields=['adjustment', 'product'], name='stock_adjus_adjustm_54fdd8_idx'),
        ),
        migrations.AddIndex(
            model_name='stockadjustmentitem',
            index=models.Index(fields=['warehouse', 'product'], name='stock_adjus_warehou_29782b_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='stockadjustmentitem',
            unique_together={('adjustment', 'product', 'warehouse', 'location', 'batch_number')},
        ),
        migrations.AddIndex(
            model_name='stockadjustment',
            index=models.Index(fields=['status'], name='stock_adjus_status_c4358c_idx'),
        ),
        migrations.AddIndex(
            model_name='stockadjustment',
            index=models.Index(fields=['adjustment_date'], name='stock_adjus_adjustm_566336_idx'),
        ),
        migrations.AddIndex(
            model_name='stockadjustment',
            index=models.Index(fields=['adjustment_type'], name='stock_adjus_adjustm_bafd7c_idx'),
        ),
        migrations.AddIndex(
            model_name='lowstockalert',
            index=models.Index(fields=['status', 'priority'], name='low_stock_a_status_8fd3c7_idx'),
        ),
        migrations.AddIndex(
            model_name='lowstockalert',
            index=models.Index(fields=['alert_type'], name='low_stock_a_alert_t_47a489_idx'),
        ),
        migrations.AddIndex(
            model_name='lowstockalert',
            index=models.Index(fields=['created_at'], name='low_stock_a_created_97ea06_idx'),
        ),
        migrations.AddIndex(
            model_name='inventorycostlayer',
            index=models.Index(fields=['inventory', 'layer_date'], name='inventory_c_invento_4b4a88_idx'),
        ),
        migrations.AddIndex(
            model_name='inventorycostlayer',
            index=models.Index(fields=['reference_type', 'reference_id'], name='inventory_c_referen_293618_idx'),
        ),
        migrations.AddIndex(
            model_name='inventorycostlayer',
            index=models.Index(fields=['batch_number'], name='inventory_c_batch_n_abad16_idx'),
        ),
    ]
