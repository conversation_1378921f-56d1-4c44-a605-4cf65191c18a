import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import {
  Box,
  Paper,
  Typography,
  Grid,
  Button,
  Alert,
  Snackbar,
  Card,
  CardContent,
  Chip,
  CircularProgress,
  Backdrop,
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  CheckCircle as CheckCircleIcon,
} from '@mui/icons-material';
import dayjs from 'dayjs';

// Import services
import { vendorBillService } from '../../../services/vendor-bill.service';
import { purchaseOrderService, type PurchaseOrder } from '../../../services/purchaseOrder.service';
import { formatCurrency } from '../../../shared/utils/formatters';

interface SnackbarState {
  open: boolean;
  message: string;
  severity: 'success' | 'error' | 'warning' | 'info';
}

const CreateVendorBillFromPOPage: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  // Get PO ID from URL params
  const poId = searchParams.get('po_id');

  // State
  const [loading, setLoading] = useState(false);
  const [poLoading, setPOLoading] = useState(true);
  const [selectedPO, setSelectedPO] = useState<PurchaseOrder | null>(null);
  const [availablePOs, setAvailablePOs] = useState<PurchaseOrder[]>([]);
  const [showPOSelection, setShowPOSelection] = useState(!poId);
  const [snackbar, setSnackbar] = useState<SnackbarState>({
    open: false,
    message: '',
    severity: 'success',
  });

  // Load available POs for selection
  const loadAvailablePOs = async () => {
    try {
      const pos = await purchaseOrderService.getPurchaseOrders();
      // Filter POs that can be used for bill creation
      // Include 'sent' status for service POs that don't require physical receipt
      const billablePOs = pos.filter(po =>
        ['sent', 'acknowledged', 'partial', 'received'].includes(po.status)
      );
      setAvailablePOs(billablePOs);
    } catch (error) {
      console.error('Failed to load POs:', error);
      setSnackbar({
        open: true,
        message: 'Failed to load Purchase Orders',
        severity: 'error',
      });
    }
  };

  // Load specific PO
  const loadPurchaseOrder = async (poId: string) => {
    try {
      setPOLoading(true);
      const po = await purchaseOrderService.getPurchaseOrder(parseInt(poId));
      setSelectedPO(po);
      setShowPOSelection(false);
    } catch (error) {
      console.error('Failed to load PO:', error);
      setSnackbar({
        open: true,
        message: 'Failed to load Purchase Order',
        severity: 'error',
      });
    } finally {
      setPOLoading(false);
    }
  };

  // Load initial data
  useEffect(() => {
    if (poId) {
      loadPurchaseOrder(poId);
    } else {
      loadAvailablePOs();
      setPOLoading(false);
    }
  }, [poId]);

  // Handle PO selection
  const handlePOSelection = (po: PurchaseOrder) => {
    setSelectedPO(po);
    setShowPOSelection(false);
    setSnackbar({
      open: true,
      message: `Purchase Order ${po.po_number} selected successfully!`,
      severity: 'success',
    });
  };

  // Create vendor bill from PO
  const handleCreateFromPO = async () => {
    if (!selectedPO) {
      setSnackbar({
        open: true,
        message: 'No Purchase Order selected',
        severity: 'error',
      });
      return;
    }

    try {
      setLoading(true);
      const createdBill = await vendorBillService.createVendorBillFromPO(selectedPO.id!);

      setSnackbar({
        open: true,
        message: `Vendor bill ${createdBill.bill_number} created successfully!`,
        severity: 'success',
      });

      // Navigate to the created bill
      setTimeout(() => {
        navigate(`/dashboard/purchases/vendor-bills/${createdBill.id}/edit`);
      }, 1500);

    } catch (error) {
      console.error('Failed to create vendor bill:', error);
      setSnackbar({
        open: true,
        message: `Failed to create vendor bill: ${error instanceof Error ? error.message : 'Unknown error'}`,
        severity: 'error',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCloseSnackbar = () => {
    setSnackbar(prev => ({ ...prev, open: false }));
  };

  // Loading state
  if (poLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress />
        <Typography sx={{ ml: 2 }}>Loading...</Typography>
      </Box>
    );
  }

  // PO Selection Interface
  if (showPOSelection) {
    return (
      <Box sx={{ minHeight: '100vh', bgcolor: '#f5f5f5' }}>
        <Backdrop open={loading} sx={{ zIndex: 9999 }}>
          <CircularProgress color="primary" />
        </Backdrop>

        {/* Header */}
        <Box sx={{ bgcolor: 'white', borderBottom: 1, borderColor: 'divider', p: 3 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Box>
              <Typography variant="h4" component="h1" sx={{ fontWeight: 600, color: '#1a1a1a' }}>
                Select Purchase Order
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
                Choose a Purchase Order to create vendor bill from
              </Typography>
            </Box>
            <Button
              variant="outlined"
              startIcon={<ArrowBackIcon />}
              onClick={() => navigate('/dashboard/purchases/vendor-bills')}
              sx={{ borderRadius: '8px' }}
            >
              Back to Vendor Bills
            </Button>
          </Box>
        </Box>

        {/* PO Selection Content */}
        <Box sx={{ p: 3 }}>
          <Grid container spacing={3}>
            {availablePOs.length === 0 ? (
              <Grid item xs={12}>
                <Paper sx={{ p: 4, textAlign: 'center' }}>
                  <Typography variant="h6" color="text.secondary" sx={{ mb: 2 }}>
                    No Purchase Orders Available
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                    There are no received or acknowledged Purchase Orders available for bill creation.
                  </Typography>
                  <Button
                    variant="contained"
                    onClick={() => navigate('/dashboard/purchases/purchase-orders')}
                    sx={{ borderRadius: '8px' }}
                  >
                    View Purchase Orders
                  </Button>
                </Paper>
              </Grid>
            ) : (
              availablePOs.map((po) => (
                <Grid item xs={12} md={6} lg={4} key={po.id}>
                  <Card 
                    sx={{ 
                      cursor: 'pointer',
                      transition: 'all 0.2s',
                      '&:hover': {
                        transform: 'translateY(-2px)',
                        boxShadow: 4,
                      }
                    }}
                    onClick={() => handlePOSelection(po)}
                  >
                    <CardContent>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                        <Typography variant="h6" sx={{ fontWeight: 600 }}>
                          {po.po_number}
                        </Typography>
                        <Chip
                          label={po.status}
                          size="small"
                          color={po.status === 'received' ? 'success' : 'primary'}
                          sx={{ textTransform: 'capitalize' }}
                        />
                      </Box>
                      
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                        <strong>Vendor:</strong> {po.vendor_name}
                      </Typography>
                      
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                        <strong>Date:</strong> {dayjs(po.po_date).format('DD/MM/YYYY')}
                      </Typography>
                      
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                        <strong>Total:</strong> {formatCurrency(po.total_amount, '$')}
                      </Typography>
                      
                      <Button
                        variant="contained"
                        fullWidth
                        size="small"
                        sx={{ borderRadius: '6px' }}
                      >
                        Create Bill from this PO
                      </Button>
                    </CardContent>
                  </Card>
                </Grid>
              ))
            )}
          </Grid>
        </Box>

        {/* Snackbar */}
        <Snackbar
          open={snackbar.open}
          autoHideDuration={6000}
          onClose={handleCloseSnackbar}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        >
          <Alert onClose={handleCloseSnackbar} severity={snackbar.severity} sx={{ width: '100%' }}>
            {snackbar.message}
          </Alert>
        </Snackbar>
      </Box>
    );
  }

  // PO not found
  if (!selectedPO) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">
          Purchase Order not found. Please check the PO ID and try again.
        </Alert>
        <Button
          startIcon={<ArrowBackIcon />}
          onClick={() => navigate('/dashboard/purchases/vendor-bills')}
          sx={{ mt: 2 }}
        >
          Back to Vendor Bills
        </Button>
      </Box>
    );
  }

  // Main PO to Bill Creation Interface
  return (
    <Box sx={{ minHeight: '100vh', bgcolor: '#f5f5f5' }}>
      <Backdrop open={loading} sx={{ zIndex: 9999 }}>
        <CircularProgress color="primary" />
      </Backdrop>

      {/* Header */}
      <Box sx={{ bgcolor: 'white', borderBottom: 1, borderColor: 'divider', p: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box>
            <Typography variant="h4" component="h1" sx={{ fontWeight: 600, color: '#1a1a1a' }}>
              Create Vendor Bill from Purchase Order
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
              Creating bill from PO {selectedPO.po_number}
            </Typography>
          </Box>
          <Button
            variant="outlined"
            startIcon={<ArrowBackIcon />}
            onClick={() => navigate('/dashboard/purchases/vendor-bills')}
            sx={{ borderRadius: '8px' }}
          >
            Back to Vendor Bills
          </Button>
        </Box>
      </Box>

      {/* Content */}
      <Box sx={{ p: 3 }}>
        <Grid container spacing={3}>
          {/* PO Information Card */}
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
                  Purchase Order Information
                </Typography>
                
                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <Typography variant="body2" color="text.secondary">
                      <strong>PO Number:</strong> {selectedPO.po_number}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Typography variant="body2" color="text.secondary">
                      <strong>Vendor:</strong> {selectedPO.vendor_name}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Typography variant="body2" color="text.secondary">
                      <strong>Date:</strong> {dayjs(selectedPO.po_date).format('DD/MM/YYYY')}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Typography variant="body2" color="text.secondary">
                      <strong>Total:</strong> {formatCurrency(selectedPO.total_amount, '$')}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Chip
                      label={selectedPO.status}
                      size="small"
                      color={selectedPO.status === 'received' ? 'success' : 'primary'}
                      sx={{ textTransform: 'capitalize' }}
                    />
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          {/* Action Card */}
          <Grid item xs={12}>
            <Card>
              <CardContent>
                <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
                  Create Vendor Bill
                </Typography>
                
                <Alert severity="info" sx={{ mb: 3 }}>
                  <Typography variant="body2">
                    Click "Create Bill from PO" to automatically create a vendor bill with all PO details.
                  </Typography>
                </Alert>

                <Button
                  variant="contained"
                  startIcon={<CheckCircleIcon />}
                  onClick={handleCreateFromPO}
                  disabled={loading}
                  size="large"
                  sx={{
                    bgcolor: 'success.main',
                    '&:hover': { bgcolor: 'success.dark' },
                    borderRadius: '8px',
                  }}
                >
                  Create Bill from PO
                </Button>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Box>

      {/* Snackbar */}
      <Snackbar
        open={snackbar.open}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
      >
        <Alert onClose={handleCloseSnackbar} severity={snackbar.severity} sx={{ width: '100%' }}>
          {snackbar.message}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default CreateVendorBillFromPOPage;
