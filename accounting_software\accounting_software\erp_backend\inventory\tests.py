"""
Comprehensive Test Suite for Enterprise Inventory Module

Tests cover:
- Model functionality and validation
- Stock valuation methods (FIFO, LIFO, Average)
- Cost layer management
- Stock transactions
- Low stock alerts
- Stock adjustments
- Valuation reports
"""

from django.test import TestCase
from django.contrib.auth.models import User
from django.utils import timezone
from decimal import Decimal
from datetime import date, timedelta

from .models import (
    UnitsOfMeasure, Warehouse, WarehouseLocation, Inventory, InventoryCostLayer,
    StockTransaction, StockAdjustment, StockAdjustmentItem, LowStockAlert,
    StockValuation, StockValuationItem
)
from .services import (
    InventoryValuationService, CostLayerService, StockTransactionService,
    LowStockAlertService, StockAdjustmentService, StockValuationReportService
)
from sales.models import Product, ProductCategory


class InventoryModelTests(TestCase):
    """Test inventory models"""
    
    def setUp(self):
        self.user = User.objects.create_user(username='testuser', password='testpass')
        
        # Create test data
        self.category = ProductCategory.objects.create(name='Test Category')
        self.product = Product.objects.create(
            name='Test Product',
            sku='TEST001',
            category=self.category,
            unit_price=Decimal('10.00')
        )
        
        self.warehouse = Warehouse.objects.create(
            name='Main Warehouse',
            warehouse_code='MAIN',
            location='Test Location',
            created_by=self.user
        )
        
        self.location = WarehouseLocation.objects.create(
            warehouse=self.warehouse,
            zone='A',
            aisle='01',
            rack='R1',
            bin='B01'
        )
    
    def test_warehouse_location_code_generation(self):
        """Test automatic location code generation"""
        self.assertEqual(self.location.location_code, 'A-01-R1-B01')
    
    def test_inventory_available_quantity(self):
        """Test inventory available quantity calculation"""
        inventory = Inventory.objects.create(
            product=self.product,
            warehouse=self.warehouse,
            location=self.location,
            quantity_on_hand=Decimal('100.0000'),
            quantity_reserved=Decimal('20.0000'),
            quantity_allocated=Decimal('10.0000')
        )
        
        self.assertEqual(inventory.available_quantity, Decimal('70.0000'))
    
    def test_inventory_reorder_point_check(self):
        """Test reorder point checking"""
        inventory = Inventory.objects.create(
            product=self.product,
            warehouse=self.warehouse,
            quantity_on_hand=Decimal('5.0000'),
            reorder_point=Decimal('10.0000')
        )
        
        self.assertTrue(inventory.is_below_reorder_point)
        self.assertFalse(inventory.is_out_of_stock)
    
    def test_inventory_out_of_stock_check(self):
        """Test out of stock checking"""
        inventory = Inventory.objects.create(
            product=self.product,
            warehouse=self.warehouse,
            quantity_on_hand=Decimal('0.0000')
        )
        
        self.assertTrue(inventory.is_out_of_stock)
    
    def test_cost_layer_creation(self):
        """Test cost layer creation and consumption"""
        inventory = Inventory.objects.create(
            product=self.product,
            warehouse=self.warehouse,
            quantity_on_hand=Decimal('100.0000'),
            average_cost=Decimal('10.00')
        )
        
        # Create cost layer
        layer = InventoryCostLayer.objects.create(
            inventory=inventory,
            layer_date=timezone.now(),
            reference_type='GRN',
            reference_id=1,
            original_quantity=Decimal('100.0000'),
            remaining_quantity=Decimal('100.0000'),
            unit_cost=Decimal('10.00')
        )
        
        # Test consumption
        consumed_qty, cost = layer.consume_quantity(Decimal('30.0000'))
        self.assertEqual(consumed_qty, Decimal('30.0000'))
        self.assertEqual(cost, Decimal('300.00'))
        self.assertEqual(layer.remaining_quantity, Decimal('70.0000'))


class InventoryValuationTests(TestCase):
    """Test inventory valuation methods"""
    
    def setUp(self):
        self.user = User.objects.create_user(username='testuser', password='testpass')
        
        self.category = ProductCategory.objects.create(name='Test Category')
        self.product = Product.objects.create(
            name='Test Product',
            sku='TEST001',
            category=self.category,
            unit_price=Decimal('10.00')
        )
        
        self.warehouse = Warehouse.objects.create(
            name='Main Warehouse',
            warehouse_code='MAIN',
            location='Test Location',
            created_by=self.user
        )
        
        self.inventory = Inventory.objects.create(
            product=self.product,
            warehouse=self.warehouse,
            quantity_on_hand=Decimal('100.0000'),
            average_cost=Decimal('12.00'),
            standard_cost=Decimal('11.00')
        )
        
        # Create cost layers for FIFO/LIFO testing
        InventoryCostLayer.objects.create(
            inventory=self.inventory,
            layer_date=timezone.now() - timedelta(days=3),
            reference_type='GRN',
            reference_id=1,
            original_quantity=Decimal('50.0000'),
            remaining_quantity=Decimal('50.0000'),
            unit_cost=Decimal('10.00')
        )
        
        InventoryCostLayer.objects.create(
            inventory=self.inventory,
            layer_date=timezone.now() - timedelta(days=1),
            reference_type='GRN',
            reference_id=2,
            original_quantity=Decimal('50.0000'),
            remaining_quantity=Decimal('50.0000'),
            unit_cost=Decimal('14.00')
        )
    
    def test_fifo_valuation(self):
        """Test FIFO valuation method"""
        total_cost, unit_cost = InventoryValuationService.calculate_fifo_cost(
            self.inventory, Decimal('30.0000')
        )
        
        # Should use oldest layer first (30 units @ $10.00)
        self.assertEqual(total_cost, Decimal('300.00'))
        self.assertEqual(unit_cost, Decimal('10.00'))
    
    def test_lifo_valuation(self):
        """Test LIFO valuation method"""
        total_cost, unit_cost = InventoryValuationService.calculate_lifo_cost(
            self.inventory, Decimal('30.0000')
        )
        
        # Should use newest layer first (30 units @ $14.00)
        self.assertEqual(total_cost, Decimal('420.00'))
        self.assertEqual(unit_cost, Decimal('14.00'))
    
    def test_weighted_average_valuation(self):
        """Test weighted average valuation method"""
        total_cost, unit_cost = InventoryValuationService.calculate_weighted_average_cost(
            self.inventory, Decimal('30.0000')
        )
        
        # Should use average cost (30 units @ $12.00)
        self.assertEqual(total_cost, Decimal('360.00'))
        self.assertEqual(unit_cost, Decimal('12.00'))
    
    def test_standard_cost_valuation(self):
        """Test standard cost valuation method"""
        total_cost, unit_cost = InventoryValuationService.calculate_standard_cost(
            self.inventory, Decimal('30.0000')
        )
        
        # Should use standard cost (30 units @ $11.00)
        self.assertEqual(total_cost, Decimal('330.00'))
        self.assertEqual(unit_cost, Decimal('11.00'))
    
    def test_get_inventory_value_all_methods(self):
        """Test getting inventory value using all methods"""
        values = InventoryValuationService.get_inventory_value(self.inventory)
        
        # Check that all valuation methods return values
        self.assertIn('fifo_value', values)
        self.assertIn('lifo_value', values)
        self.assertIn('average_value', values)
        self.assertIn('standard_value', values)
        self.assertIn('current_value', values)
        
        # FIFO: 50@$10 + 50@$14 = $1200
        self.assertEqual(values['fifo_value'], Decimal('1200.00'))
        
        # LIFO: 50@$14 + 50@$10 = $1200 (same total, different order)
        self.assertEqual(values['lifo_value'], Decimal('1200.00'))
        
        # Average: 100@$12 = $1200
        self.assertEqual(values['average_value'], Decimal('1200.00'))
        
        # Standard: 100@$11 = $1100
        self.assertEqual(values['standard_value'], Decimal('1100.00'))


class StockTransactionTests(TestCase):
    """Test stock transaction processing"""
    
    def setUp(self):
        self.user = User.objects.create_user(username='testuser', password='testpass')
        
        self.category = ProductCategory.objects.create(name='Test Category')
        self.product = Product.objects.create(
            name='Test Product',
            sku='TEST001',
            category=self.category,
            unit_price=Decimal('10.00')
        )
        
        self.warehouse = Warehouse.objects.create(
            name='Main Warehouse',
            warehouse_code='MAIN',
            location='Test Location',
            created_by=self.user
        )
    
    def test_stock_receipt_processing(self):
        """Test stock receipt transaction processing"""
        # Process stock receipt
        stock_txn = StockTransactionService.process_stock_receipt(
            product=self.product,
            warehouse=self.warehouse,
            quantity=Decimal('100.0000'),
            unit_cost=Decimal('10.00'),
            reference_type='GRN',
            reference_id=1,
            user=self.user
        )
        
        # Check transaction was created
        self.assertEqual(stock_txn.transaction_type, 'RECEIPT')
        self.assertEqual(stock_txn.quantity, Decimal('100.0000'))
        self.assertEqual(stock_txn.unit_cost, Decimal('10.00'))
        
        # Check inventory was updated
        inventory = Inventory.objects.get(product=self.product, warehouse=self.warehouse)
        self.assertEqual(inventory.quantity_on_hand, Decimal('100.0000'))
        self.assertEqual(inventory.average_cost, Decimal('10.00'))
        
        # Check cost layer was created
        cost_layer = InventoryCostLayer.objects.get(inventory=inventory)
        self.assertEqual(cost_layer.remaining_quantity, Decimal('100.0000'))
        self.assertEqual(cost_layer.unit_cost, Decimal('10.00'))
    
    def test_stock_issue_processing(self):
        """Test stock issue transaction processing"""
        # First, receive some stock
        StockTransactionService.process_stock_receipt(
            product=self.product,
            warehouse=self.warehouse,
            quantity=Decimal('100.0000'),
            unit_cost=Decimal('10.00'),
            reference_type='GRN',
            reference_id=1,
            user=self.user
        )
        
        # Then issue some stock
        stock_txn = StockTransactionService.process_stock_issue(
            product=self.product,
            warehouse=self.warehouse,
            quantity=Decimal('30.0000'),
            reference_type='SO',
            reference_id=1,
            user=self.user
        )
        
        # Check transaction was created
        self.assertEqual(stock_txn.transaction_type, 'ISSUE')
        self.assertEqual(stock_txn.quantity, Decimal('-30.0000'))  # Negative for outbound
        
        # Check inventory was updated
        inventory = Inventory.objects.get(product=self.product, warehouse=self.warehouse)
        self.assertEqual(inventory.quantity_on_hand, Decimal('70.0000'))
    
    def test_insufficient_stock_error(self):
        """Test error when trying to issue more stock than available"""
        # Create inventory with limited stock
        inventory = Inventory.objects.create(
            product=self.product,
            warehouse=self.warehouse,
            quantity_on_hand=Decimal('10.0000')
        )
        
        # Try to issue more than available
        with self.assertRaises(ValueError):
            StockTransactionService.process_stock_issue(
                product=self.product,
                warehouse=self.warehouse,
                quantity=Decimal('20.0000'),
                reference_type='SO',
                reference_id=1,
                user=self.user
            )


class LowStockAlertTests(TestCase):
    """Test low stock alert functionality"""

    def setUp(self):
        self.user = User.objects.create_user(username='testuser', password='testpass')

        self.category = ProductCategory.objects.create(name='Test Category')
        self.product = Product.objects.create(
            name='Test Product',
            sku='TEST001',
            category=self.category,
            unit_price=Decimal('10.00')
        )

        self.warehouse = Warehouse.objects.create(
            name='Main Warehouse',
            warehouse_code='MAIN',
            location='Test Location',
            created_by=self.user
        )

        self.inventory = Inventory.objects.create(
            product=self.product,
            warehouse=self.warehouse,
            quantity_on_hand=Decimal('5.0000'),
            reorder_point=Decimal('10.0000'),
            reorder_quantity=Decimal('50.0000')
        )

    def test_low_stock_alert_creation(self):
        """Test creation of low stock alert"""
        alert = LowStockAlertService.check_and_create_alerts(self.inventory)

        self.assertIsNotNone(alert)
        self.assertEqual(alert.alert_type, 'LOW_STOCK')
        self.assertEqual(alert.priority, 'HIGH')
        self.assertEqual(alert.status, 'ACTIVE')

    def test_out_of_stock_alert_creation(self):
        """Test creation of out of stock alert"""
        self.inventory.quantity_on_hand = Decimal('0.0000')
        self.inventory.save()

        alert = LowStockAlertService.check_and_create_alerts(self.inventory)

        self.assertIsNotNone(alert)
        self.assertEqual(alert.alert_type, 'OUT_OF_STOCK')
        self.assertEqual(alert.priority, 'CRITICAL')

    def test_alert_resolution(self):
        """Test alert resolution when stock increases"""
        # Create alert
        alert = LowStockAlertService.check_and_create_alerts(self.inventory)
        self.assertEqual(alert.status, 'ACTIVE')

        # Increase stock above reorder point
        self.inventory.quantity_on_hand = Decimal('15.0000')
        self.inventory.save()

        # Check alert resolution
        LowStockAlertService.check_and_resolve_alerts(self.inventory)

        alert.refresh_from_db()
        self.assertEqual(alert.status, 'RESOLVED')
        self.assertIsNotNone(alert.resolved_at)

    def test_no_duplicate_alerts(self):
        """Test that duplicate alerts are not created"""
        # Create first alert
        alert1 = LowStockAlertService.check_and_create_alerts(self.inventory)

        # Try to create another alert
        alert2 = LowStockAlertService.check_and_create_alerts(self.inventory)

        # Should return existing alert, not create new one
        self.assertEqual(alert1.alert_id, alert2.alert_id)

        # Verify only one alert exists
        alert_count = LowStockAlert.objects.filter(
            inventory=self.inventory,
            status='ACTIVE'
        ).count()
        self.assertEqual(alert_count, 1)


class StockAdjustmentTests(TestCase):
    """Test stock adjustment functionality"""

    def setUp(self):
        self.user = User.objects.create_user(username='testuser', password='testpass')

        self.category = ProductCategory.objects.create(name='Test Category')
        self.product = Product.objects.create(
            name='Test Product',
            sku='TEST001',
            category=self.category,
            unit_price=Decimal('10.00')
        )

        self.warehouse = Warehouse.objects.create(
            name='Main Warehouse',
            warehouse_code='MAIN',
            location='Test Location',
            created_by=self.user
        )

        self.inventory = Inventory.objects.create(
            product=self.product,
            warehouse=self.warehouse,
            quantity_on_hand=Decimal('100.0000'),
            average_cost=Decimal('10.00')
        )

    def test_stock_adjustment_creation(self):
        """Test stock adjustment creation"""
        adjustment = StockAdjustmentService.create_adjustment(
            adjustment_type='PHYSICAL',
            reason_code='CYCLE_COUNT',
            description='Monthly cycle count adjustment',
            user=self.user
        )

        self.assertEqual(adjustment.adjustment_type, 'PHYSICAL')
        self.assertEqual(adjustment.reason_code, 'CYCLE_COUNT')
        self.assertEqual(adjustment.status, 'DRAFT')
        self.assertTrue(adjustment.adjustment_number.startswith('ADJ-'))

    def test_adjustment_item_addition(self):
        """Test adding items to stock adjustment"""
        adjustment = StockAdjustmentService.create_adjustment(
            adjustment_type='PHYSICAL',
            reason_code='CYCLE_COUNT',
            description='Test adjustment',
            user=self.user
        )

        item = StockAdjustmentService.add_adjustment_item(
            adjustment=adjustment,
            product=self.product,
            warehouse=self.warehouse,
            system_quantity=Decimal('100.0000'),
            physical_quantity=Decimal('95.0000'),
            unit_cost=Decimal('10.00')
        )

        self.assertEqual(item.adjustment_quantity, Decimal('-5.0000'))
        self.assertEqual(item.adjustment_value, Decimal('-50.00'))

        # Check totals were updated
        adjustment.refresh_from_db()
        self.assertEqual(adjustment.total_adjustment_value, Decimal('-50.00'))

    def test_adjustment_approval_workflow(self):
        """Test adjustment approval workflow"""
        adjustment = StockAdjustmentService.create_adjustment(
            adjustment_type='PHYSICAL',
            reason_code='CYCLE_COUNT',
            description='Test adjustment',
            user=self.user
        )

        # Add item
        StockAdjustmentService.add_adjustment_item(
            adjustment=adjustment,
            product=self.product,
            warehouse=self.warehouse,
            system_quantity=Decimal('100.0000'),
            physical_quantity=Decimal('95.0000'),
            unit_cost=Decimal('10.00')
        )

        # Submit for approval
        adjustment.status = 'PENDING'
        adjustment.save()

        # Approve
        StockAdjustmentService.approve_adjustment(adjustment, self.user)

        adjustment.refresh_from_db()
        self.assertEqual(adjustment.status, 'APPROVED')
        self.assertIsNotNone(adjustment.approved_at)
        self.assertEqual(adjustment.approved_by, self.user)

    def test_adjustment_posting(self):
        """Test posting adjustment to inventory"""
        adjustment = StockAdjustmentService.create_adjustment(
            adjustment_type='PHYSICAL',
            reason_code='CYCLE_COUNT',
            description='Test adjustment',
            user=self.user
        )

        # Add item
        StockAdjustmentService.add_adjustment_item(
            adjustment=adjustment,
            product=self.product,
            warehouse=self.warehouse,
            system_quantity=Decimal('100.0000'),
            physical_quantity=Decimal('95.0000'),
            unit_cost=Decimal('10.00')
        )

        # Approve and post
        adjustment.status = 'APPROVED'
        adjustment.save()

        StockAdjustmentService.post_adjustment(adjustment, self.user)

        # Check adjustment status
        adjustment.refresh_from_db()
        self.assertEqual(adjustment.status, 'POSTED')
        self.assertIsNotNone(adjustment.posted_at)

        # Check inventory was updated
        self.inventory.refresh_from_db()
        self.assertEqual(self.inventory.quantity_on_hand, Decimal('95.0000'))

        # Check stock transaction was created
        stock_txn = StockTransaction.objects.filter(
            product=self.product,
            warehouse=self.warehouse,
            transaction_type='ADJUSTMENT'
        ).first()

        self.assertIsNotNone(stock_txn)
        self.assertEqual(stock_txn.quantity, Decimal('-5.0000'))


class StockValuationReportTests(TestCase):
    """Test stock valuation reporting"""

    def setUp(self):
        self.user = User.objects.create_user(username='testuser', password='testpass')

        self.category = ProductCategory.objects.create(name='Test Category')
        self.product1 = Product.objects.create(
            name='Product 1',
            sku='PROD001',
            category=self.category,
            unit_price=Decimal('10.00')
        )
        self.product2 = Product.objects.create(
            name='Product 2',
            sku='PROD002',
            category=self.category,
            unit_price=Decimal('20.00')
        )

        self.warehouse = Warehouse.objects.create(
            name='Main Warehouse',
            warehouse_code='MAIN',
            location='Test Location',
            created_by=self.user
        )

        # Create inventory for both products
        self.inventory1 = Inventory.objects.create(
            product=self.product1,
            warehouse=self.warehouse,
            quantity_on_hand=Decimal('100.0000'),
            average_cost=Decimal('10.00'),
            standard_cost=Decimal('9.50')
        )

        self.inventory2 = Inventory.objects.create(
            product=self.product2,
            warehouse=self.warehouse,
            quantity_on_hand=Decimal('50.0000'),
            average_cost=Decimal('20.00'),
            standard_cost=Decimal('19.00')
        )

    def test_valuation_report_creation(self):
        """Test creation of stock valuation report"""
        valuation = StockValuationReportService.create_valuation_report(
            valuation_date=date.today(),
            valuation_type='MONTHLY',
            warehouse=self.warehouse,
            user=self.user
        )

        self.assertEqual(valuation.status, 'COMPLETED')
        self.assertEqual(valuation.total_items, 2)
        self.assertEqual(valuation.total_quantity, Decimal('150.0000'))

        # Check valuation items were created
        valuation_items = valuation.items.all()
        self.assertEqual(valuation_items.count(), 2)

        # Check totals (Product1: 100@$10 + Product2: 50@$20 = $2000)
        self.assertEqual(valuation.average_total_value, Decimal('2000.00'))

        # Check standard cost total (Product1: 100@$9.50 + Product2: 50@$19 = $1900)
        self.assertEqual(valuation.standard_total_value, Decimal('1900.00'))

    def test_abc_analysis_report(self):
        """Test ABC analysis report generation"""
        # Create cost layers for more realistic valuation
        InventoryCostLayer.objects.create(
            inventory=self.inventory1,
            layer_date=timezone.now(),
            reference_type='GRN',
            reference_id=1,
            original_quantity=Decimal('100.0000'),
            remaining_quantity=Decimal('100.0000'),
            unit_cost=Decimal('10.00')
        )

        InventoryCostLayer.objects.create(
            inventory=self.inventory2,
            layer_date=timezone.now(),
            reference_type='GRN',
            reference_id=2,
            original_quantity=Decimal('50.0000'),
            remaining_quantity=Decimal('50.0000'),
            unit_cost=Decimal('20.00')
        )

        abc_report = StockValuationReportService.get_abc_analysis_report(self.warehouse)

        self.assertIn('A', abc_report)
        self.assertIn('B', abc_report)
        self.assertIn('C', abc_report)
        self.assertEqual(abc_report['total_value'], Decimal('2000.00'))

        # Check ABC classifications were updated
        self.inventory1.refresh_from_db()
        self.inventory2.refresh_from_db()

        # Both should be A items since they're high value
        self.assertIn(self.inventory1.abc_classification, ['A', 'B'])
        self.assertIn(self.inventory2.abc_classification, ['A', 'B'])
