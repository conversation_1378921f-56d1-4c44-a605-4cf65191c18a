import React from 'react';
import { createBrowserRouter, RouterProvider, Navigate } from 'react-router-dom';
import { ThemeProvider } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import { SnackbarProvider } from 'notistack';
import { LocalizationProvider } from '@mui/x-date-pickers';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { theme } from './theme';
import { CompanyProvider } from './contexts/CompanyContext';
import { CustomerProvider } from './contexts/CustomerContext';
import { PaymentTermsProvider } from './contexts/PaymentTermsContext';
import { ProductCategoriesProvider } from './contexts/ProductCategoriesContext';
import { VendorProvider } from './contexts/VendorContext';
import { BillProvider } from './contexts/BillContext';
import { PurchaseOrderProvider } from './contexts/PurchaseOrderContext';
import { PurchaseProductProvider } from './contexts/PurchaseProductContext';

// Import Auth Provider
import { AuthProvider } from './contexts/AuthContext';
import ProtectedRoute from './components/ProtectedRoute';

import LoginPage from './domains/auth/pages/LoginPage';
import DashboardLayout from './layouts/DashboardLayout';
import DashboardContent from './domains/dashboard/pages/DashboardContent';
import CreateCompanyPage from './domains/company/pages/CreateCompanyPage';
import CompanySettingsPage from './domains/company/pages/CompanySettingsPage';
import UserManagementPage from './domains/users/pages/UserManagementPage';
import CustomersPage from './domains/sales/pages/CustomersPage';

import { RouteErrorBoundary } from './components/ErrorBoundary';

import AllPurchasesPage from './domains/purchase/pages/AllPurchasesPage';
import BillsPage from './domains/purchase/pages/BillsPage';
import CreateJournalVoucherPage from './domains/purchase/pages/CreateJournalVoucherPage';
import PurchaseOrdersPage from './domains/purchase/pages/PurchaseOrdersPage';
import VendorsPage from './domains/purchase/pages/VendorsPage';
import PurchaseProductsPage from './domains/purchase/pages/PurchaseProductsPage';
import VendorBillsPage from './domains/purchase/pages/VendorBillsPage';
import CreateVendorBillPage from './domains/purchase/pages/CreateVendorBillPage';
import CreateVendorBillFromPOPage from './domains/purchase/pages/CreateVendorBillFromPOPage';
import CreateVendorBillFromGRNPage from './domains/purchase/pages/CreateVendorBillFromGRNPage';

// Import HR Module Pages
import HRDashboardPage from './domains/hr/pages/HRDashboardPage';
import EmployeesPage from './domains/hr/pages/EmployeesPage';
import RecruitmentPage from './domains/hr/pages/RecruitmentPage';
import TimeOffPage from './domains/hr/pages/TimeOffPage';
import AttendancePage from './domains/hr/pages/AttendancePage';
import DepartmentsPage from './domains/hr/pages/DepartmentsPage';

// Import GL Module Pages
import ChartOfAccountsPage from './domains/gl/pages/ChartOfAccountsPage';
import JournalEntriesPage from './domains/gl/pages/JournalEntriesPage';
import TrialBalancePage from './domains/gl/pages/TrialBalancePage';
import AccountLedgersPage from './domains/gl/pages/AccountLedgersPage';
import FinancialStatementsPage from './domains/gl/pages/FinancialStatementsPage';
import PurchaseOrdersReviewPage from './domains/gl/pages/PurchaseOrdersReviewPage';
import GRNReviewPage from './domains/gl/pages/GRNReviewPage';
import GRNReturnsReviewPage from './domains/gl/pages/GRNReturnsReviewPage';
import StockTransactionsReviewPage from './domains/gl/pages/StockTransactionsReviewPage';

// Import CMS Module Pages
import CashManagementDashboard from './domains/cms/pages/CashManagementDashboard';
import CashAccountsPage from './domains/cms/pages/CashAccountsPage';
import CashTransactionsPage from './domains/cms/pages/CashTransactionsPage';
import CashTransfersPage from './domains/cms/pages/CashTransfersPage';
import BankReconciliationPage from './domains/cms/pages/BankReconciliationPage';
import CashPaymentForm from './domains/cms/pages/CashPaymentForm';
import CashReceiptForm from './domains/cms/pages/CashReceiptForm';

// Import FP&A Module Pages
import { FPADashboard, BudgetPlanningForm, FinancialModels } from './domains/fpa';

// Import Inventory Module Pages
import InventoryDashboard from './domains/inventory/pages/InventoryDashboard';
import GoodsReceiptPage from './domains/inventory/pages/GoodsReceiptPage';
import GRNFormPage from './domains/inventory/pages/GRNFormPage';
import GRNDetailPage from './domains/inventory/pages/GRNDetailPage';
import GoodsReturnPage from './domains/inventory/pages/GoodsReturnPage';
import GRNReturnFormPage from './domains/inventory/pages/GRNReturnFormPage';
import GRNReturnDetailPage from './domains/inventory/pages/GRNReturnDetailPage';
import StockLevelsPage from './domains/inventory/pages/StockLevelsPage';
import StockTransactionsPage from './domains/inventory/pages/StockTransactionsPage';
import StockValuationPage from './domains/inventory/pages/StockValuationPage';
import CreateValuationPage from './domains/inventory/pages/CreateValuationPage';
import ValuationDetailPage from './domains/inventory/pages/ValuationDetailPage';
import ValuationDashboard from './domains/inventory/pages/ValuationDashboard';
import InventoryTransfersPage from './domains/inventory/pages/InventoryTransfersPage';

import SalesTaxPage from './domains/company/pages/SalesTaxPage';
import TDSPage from './domains/company/pages/TDSPage';

// Pricing Module Pages
import PricingDashboard from './domains/pricing/pages/PricingDashboard';
import PriceListsPage from './domains/pricing/pages/PriceListsPage';
import DiscountRulesPage from './domains/pricing/pages/DiscountRulesPage';
import PriceCalculatorPage from './domains/pricing/pages/PriceCalculatorPage';
import CustomerPricingPage from './domains/pricing/pages/CustomerPricingPage';
import CostManagementPage from './domains/pricing/pages/CostManagementPage';
import MarginAnalysisPage from './domains/pricing/pages/MarginAnalysisPage';



const router = createBrowserRouter([
  {
    path: "/login",
    element: <LoginPage />
  },
  {
    path: "/",
    element: <Navigate to="/dashboard" replace />
  },
  {
    path: "/dashboard",
    element: <ProtectedRoute><DashboardLayout /></ProtectedRoute>,
    errorElement: <RouteErrorBoundary />,
    children: [
      {
        index: true,
        element: <DashboardContent />
      },
      {
        path: "setup/company",
        element: <CreateCompanyPage />
      },
      {
        path: "setup/company/settings",
        element: <CompanySettingsPage />
      },
      
      {
        path: "setup/users",
        element: <UserManagementPage />
      },

      {
        path: "sales",
        children: [
          {
            index: true,
            element: <Navigate to="all" replace />
          },
          {
            path: "customers",
            element: <CustomersPage />
          }
        ]
      },
      {
        path: "purchases",
        children: [
          {
            index: true,
            element: <Navigate to="all" replace />
          },
          {
            path: "all",
            element: <AllPurchasesPage />
          },
          {
            path: "bills",
            element: <BillsPage />
          },
          {
            path: "vendor-bills",
            element: <VendorBillsPage />
          },
          {
            path: "vendor-bills/create",
            element: <CreateVendorBillPage />
          },
          {
            path: "vendor-bills/create-from-grn",
            element: <CreateVendorBillFromGRNPage />
          },
          {
            path: "vendor-bills/create-from-po",
            element: <CreateVendorBillFromPOPage />
          },
          {
            path: "vendor-bills/create-from-return",
            element: <CreateVendorBillPage />
          },
          {
            path: "vendor-bills/:id",
            element: <CreateVendorBillPage />
          },
          {
            path: "vendor-bills/:id/edit",
            element: <CreateVendorBillPage />
          },
          {
            path: "create-journal-voucher",
            element: <CreateJournalVoucherPage />
          },
          {
            path: "orders",
            element: <PurchaseOrdersPage />
          },
          {
            path: "vendors",
            element: <VendorsPage />
          },
          {
            path: "products",
            element: <PurchaseProductsPage />
          }
        ]
      },

      // HR Module Routes
      {
        path: "hr",
        children: [
          {
            index: true,
            element: <Navigate to="dashboard" replace />
          },
          {
            path: "dashboard",
            element: <HRDashboardPage />
          },
          {
            path: "employees",
            element: <EmployeesPage />
          },
          {
            path: "departments",
            element: <DepartmentsPage />
          },
          {
            path: "recruitment",
            element: <RecruitmentPage />
          },
          {
            path: "time-off",
            element: <TimeOffPage />
          },
          {
            path: "attendance",
            element: <AttendancePage />
          }
        ]
      },
      // General Ledger Module Routes
      {
        path: "gl",
        children: [
          {
            index: true,
            element: <Navigate to="chart-of-accounts" replace />
          },
          {
            path: "chart-of-accounts",
            element: <ChartOfAccountsPage />
          },
          {
            path: "journal-entries",
            element: <JournalEntriesPage />
          },
          {
            path: "trial-balance",
            element: <TrialBalancePage />
          },
          {
            path: "account-ledgers",
            element: <AccountLedgersPage />
          },
          {
            path: "reports",
            element: <FinancialStatementsPage />
          },
          {
            path: "purchase-orders",
            element: <PurchaseOrdersReviewPage />
          },
          {
            path: "grn-receipts",
            element: <GRNReviewPage />
          },
          {
            path: "grn-returns",
            element: <GRNReturnsReviewPage />
          },
          {
            path: "stock-transactions",
            element: <StockTransactionsReviewPage />
          },
        ]
      },
      // Cash Management System Routes
      {
        path: "cms",
        children: [
          {
            index: true,
            element: <Navigate to="dashboard" replace />
          },
          {
            path: "dashboard",
            element: <CashManagementDashboard />
          },
          {
            path: "accounts",
            element: <CashAccountsPage />
          },
          {
            path: "transactions",
            element: <CashTransactionsPage />
          },
          {
            path: "transfers",
            element: <CashTransfersPage />
          },
          {
            path: "reconciliation",
            element: <BankReconciliationPage />
          },
          {
            path: "payments/new",
            element: <CashPaymentForm />
          },
          {
            path: "payments/:id/edit",
            element: <CashPaymentForm />
          },
          {
            path: "payments/:id/view",
            element: <CashPaymentForm />
          },
          {
            path: "receipts/new",
            element: <CashReceiptForm />
          },
          {
            path: "receipts/:id/edit",
            element: <CashReceiptForm />
          },
          {
            path: "receipts/:id/view",
            element: <CashReceiptForm />
          },
        ]
      },
      // Financial Planning & Analysis Module Routes
      {
        path: "fpa",
        children: [
          {
            index: true,
            element: <Navigate to="dashboard" replace />
          },
          {
            path: "dashboard",
            element: <FPADashboard />
          },
          {
            path: "budget-planning",
            element: <BudgetPlanningForm />
          },
          {
            path: "financial-models",
            element: <FinancialModels />
          },
          {
            path: "variance-analysis",
            element: <div style={{ padding: '2rem', textAlign: 'center' }}>
              <h2>🔍 Variance Analysis</h2>
              <p>Detailed variance analysis module coming soon...</p>
            </div>
          },
          {
            path: "forecasting",
            element: <div style={{ padding: '2rem', textAlign: 'center' }}>
              <h2>🔮 Forecasting</h2>
              <p>AI-powered forecasting module coming soon...</p>
            </div>
          },
          {
            path: "plan-vs-actual",
            element: <div style={{ padding: '2rem', textAlign: 'center' }}>
              <h2>📊 Plan vs Actual</h2>
              <p>Plan vs actual reporting module coming soon...</p>
            </div>
          },
        ]
      },
      // Inventory Management Module Routes
      {
        path: "inventory",
        children: [
          {
            index: true,
            element: <InventoryDashboard />
          },
          {
            path: "grn",
            element: <GoodsReceiptPage />
          },
          {
            path: "grn/new",
            element: <GRNFormPage />
          },
          {
            path: "grn/:id",
            element: <GRNDetailPage />
          },
          {
            path: "grn/:id/edit",
            element: <GRNFormPage />
          },
          {
            path: "grn-returns",
            element: <GoodsReturnPage />
          },
          {
            path: "grn-returns/new",
            element: <GRNReturnFormPage />
          },
          {
            path: "grn-returns/:id",
            element: <GRNReturnDetailPage />
          },
          {
            path: "grn-returns/:id/edit",
            element: <GRNReturnFormPage />
          },
          {
            path: "stock",
            element: <StockLevelsPage />
          },
          {
            path: "transactions",
            element: <StockTransactionsPage />
          },
          {
            path: "transfers",
            element: <InventoryTransfersPage />
          },
          {
            path: "warehouses",
            element: <div style={{ padding: '2rem', textAlign: 'center' }}>
              <h2>🏢 Warehouses</h2>
              <p>Warehouse management coming soon...</p>
            </div>
          },
          {
            path: "valuations",
            element: <StockValuationPage />
          },
          {
            path: "valuations/new",
            element: <CreateValuationPage />
          },
          {
            path: "valuations/:id",
            element: <ValuationDetailPage />
          },
          {
            path: "valuations/dashboard",
            element: <ValuationDashboard />
          },
        ]
      },
      // Pricing Module Routes
      {
        path: "pricing",
        children: [
          {
            index: true,
            element: <Navigate to="dashboard" replace />
          },
          {
            path: "dashboard",
            element: <PricingDashboard />
          },
          {
            path: "price-lists",
            element: <PriceListsPage />
          },
          {
            path: "discount-rules",
            element: <DiscountRulesPage />
          },
          {
            path: "calculator",
            element: <PriceCalculatorPage />
          },
          {
            path: "customer-pricing",
            element: <CustomerPricingPage />
          },
          {
            path: "cost-management",
            element: <CostManagementPage />
          },
          {
            path: "margin-analysis",
            element: <MarginAnalysisPage />
          },
        ]
      },
      {
        path: "setup/company/sales-tax",
        element: <SalesTaxPage />
      },
      {
        path: "setup/company/tds",
        element: <TDSPage />
      }
    ]
  }
], {
  future: {
    v7_relativeSplatPath: true,
    v7_fetcherPersist: true,
    v7_normalizeFormMethod: true,
    v7_partialHydration: true,
    v7_skipActionErrorRevalidation: true,
  }
});

const App: React.FC = () => {
  return (
    <AuthProvider>
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <LocalizationProvider dateAdapter={AdapterDayjs}>
          <SnackbarProvider maxSnack={3}>
            <CompanyProvider>
              <CustomerProvider>
                <PaymentTermsProvider>
                  <ProductCategoriesProvider>
                    <VendorProvider>
                      <BillProvider>
                        <PurchaseOrderProvider>
                          <PurchaseProductProvider>
                            <RouterProvider
                              router={router}
                              future={{
                                v7_startTransition: true
                              }}
                            />
                          </PurchaseProductProvider>
                        </PurchaseOrderProvider>
                      </BillProvider>
                    </VendorProvider>
                  </ProductCategoriesProvider>
                </PaymentTermsProvider>
              </CustomerProvider>
            </CompanyProvider>
          </SnackbarProvider>
        </LocalizationProvider>
      </ThemeProvider>
    </AuthProvider>
  );
};

export default App;
