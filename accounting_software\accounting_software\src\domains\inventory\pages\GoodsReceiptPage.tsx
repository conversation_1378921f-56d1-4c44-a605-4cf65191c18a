import React, { useState, useEffect, useCallback } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  <PERSON>ton,
  Alert,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Tooltip,
} from '@mui/material';
import {
  Receipt as ReceiptIcon,
  Add as AddIcon,
  Edit as EditIcon,
  Visibility as ViewIcon,
  Delete as DeleteIcon,
  PostAdd as PostIcon,
  MoreVert as MoreVertIcon,
  Refresh as RefreshIcon,
  Download as DownloadIcon,
  CheckCircle as CheckCircleIcon,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { PageContainer, PageHeader } from '../../../layouts/components/PageComponents';
import DataTable from '../../../shared/components/DataTable';
import { formatCurrency, formatDate } from '../../../shared/utils/formatters';
import { grnService, GRN } from '../services/grn.service';
import { useCurrencyInfo } from '../../gl/hooks/useCurrencyInfo';

const GoodsReceiptPage: React.FC = () => {
  const navigate = useNavigate();
  const { currencyInfo, loading: currencyLoading } = useCurrencyInfo();
  const [grns, setGrns] = useState<GRN[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedGrn, setSelectedGrn] = useState<GRN | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [postDialogOpen, setPostDialogOpen] = useState(false);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  
  // Pagination and filtering
  const [page, setPage] = useState(0);
  const [pageSize, setPageSize] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');

  // IFRS-compliant currency formatting function
  const formatCurrency = (amount: number): string => {
    if (!currencyInfo || currencyLoading) {
      console.log('💰 Currency info not loaded yet, using fallback');
      return `₹${amount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
    }
    
    console.log('💰 Using currency info:', currencyInfo);
    const symbol = currencyInfo.functional_currency_symbol || '$';
    return `${symbol}${amount.toLocaleString('en-US', { 
      minimumFractionDigits: 2, 
      maximumFractionDigits: 2 
    })}`;
  };

  // Helper function to get product names from GRN items
  const getProductNames = (grn: GRN): { display: string; full: string[] } => {
    if (!grn.items || grn.items.length === 0) {
      return { display: 'No products', full: [] };
    }

    const productNames = grn.items
      .map(item => item.product_name || `Product ${item.product}`)
      .filter(Boolean);

    if (productNames.length <= 3) {
      return { 
        display: productNames.join(', '), 
        full: productNames 
      };
    }

    const displayed = productNames.slice(0, 3);
    const remaining = productNames.length - 3;
    return {
      display: `${displayed.join(', ')} +${remaining} more`,
      full: productNames
    };
  };

  useEffect(() => {
    loadGRNs();
  }, [page, pageSize, searchTerm, statusFilter]);

  const loadGRNs = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      console.log('🔍 Loading GRNs with search term:', searchTerm);
      
      const response = await grnService.getGRNs({
        page: page + 1, // API uses 1-based pagination
        search: searchTerm || undefined,
        status: statusFilter || undefined,
      });
      
      console.log('📋 Loaded GRNs:', response.results);
      setGrns(response.results || []);
      setTotalCount(response.count || 0);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load GRNs');
    } finally {
      setLoading(false);
    }
  }, [page, pageSize, searchTerm, statusFilter]);

  const handleCreateGRN = () => {
    navigate('/dashboard/inventory/grn/new');
  };

  const handleViewGRN = (grn: GRN) => {
    navigate(`/dashboard/inventory/grn/${grn.grn_id}`);
  };

  const handleEditGRN = (grn: GRN) => {
    navigate(`/dashboard/inventory/grn/${grn.grn_id}/edit`);
  };

  const handleDeleteGRN = async () => {
    if (!selectedGrn) return;
    
    try {
      await grnService.deleteGRN(selectedGrn.grn_id!);
      setDeleteDialogOpen(false);
      setSelectedGrn(null);
      loadGRNs();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete GRN');
    }
  };

  const handlePostToInventory = async () => {
    if (!selectedGrn?.grn_id) return;
    
    try {
      await grnService.postToInventory(selectedGrn.grn_id);
      setPostDialogOpen(false);
      setSelectedGrn(null);
      loadGRNs();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to post GRN to inventory');
    }
  };

  const handleMarkAsReceived = async (grn: GRN) => {
    if (!grn.grn_id) return;
    
    try {
      // Use direct API call to update status
      const token = localStorage.getItem('token');
      const response = await fetch(`http://localhost:8000/api/inventory/grns/${grn.grn_id}/`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Token ${token}`
        },
        body: JSON.stringify({ status: 'RECEIVED' })
      });

      if (!response.ok) {
        throw new Error('Failed to mark GRN as received');
      }
      
      loadGRNs(); // Reload the list
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to mark GRN as received');
    }
  };

  const getStatusChip = (status: string) => {
    const statusConfig = {
      DRAFT: { color: 'default' as const, label: 'Draft' },
      RECEIVED: { color: 'primary' as const, label: 'Received' },
      POSTED: { color: 'success' as const, label: 'Posted' },
      CANCELLED: { color: 'error' as const, label: 'Cancelled' },
    };
    
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.DRAFT;
    return <Chip label={config.label} color={config.color} size="small" />;
  };

  const columns = [
    {
      field: 'grn_number',
      headerName: 'GRN Number',
      flex: 1,
      renderCell: ({ row }: { row: GRN }) => (
        <Typography variant="body2" fontWeight="medium">
          {row.grn_number}
        </Typography>
      ),
    },
    {
      field: 'purchase_order_number',
      headerName: 'PO Number',
      flex: 1,
    },
    {
      field: 'vendor_name',
      headerName: 'Vendor',
      flex: 1.2,
    },
    {
      field: 'products',
      headerName: 'Products',
      flex: 1.5,
      renderCell: ({ row }: { row: GRN }) => {
        const { display, full } = getProductNames(row);
        
        if (full.length <= 3) {
          return (
            <Typography variant="body2" sx={{ fontSize: '0.875rem' }}>
              {display}
            </Typography>
          );
        }
        
        return (
          <Tooltip 
            title={
              <Box>
                <Typography variant="subtitle2" sx={{ fontWeight: 'bold', mb: 1 }}>
                  All Products ({full.length}):
                </Typography>
                {full.map((product, index) => (
                  <Typography key={index} variant="body2" sx={{ fontSize: '0.875rem' }}>
                    • {product}
                  </Typography>
                ))}
              </Box>
            }
            arrow
            placement="top"
          >
            <Typography 
              variant="body2" 
              sx={{ 
                fontSize: '0.875rem',
                cursor: 'help',
                '&:hover': { textDecoration: 'underline' }
              }}
            >
              {display}
            </Typography>
          </Tooltip>
        );
      },
    },
    {
      field: 'warehouse_name',
      headerName: 'Warehouse',
      flex: 1,
    },
    {
      field: 'receipt_date',
      headerName: 'Receipt Date',
      flex: 1,
      renderCell: ({ row }: { row: GRN }) => formatDate(row.receipt_date),
    },
    {
      field: 'status',
      headerName: 'Status',
      flex: 0.8,
      renderCell: ({ row }: { row: GRN }) => getStatusChip(row.status),
    },
    {
      field: 'total_quantity',
      headerName: 'Quantity',
      flex: 0.8,
      renderCell: ({ row }: { row: GRN }) => {
        const quantity = Number(row.total_quantity || 0);
        return (
          <Typography variant="body2" sx={{ textAlign: 'right', fontWeight: 'medium' }}>
            {quantity.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}
          </Typography>
        );
      },
    },
    {
      field: 'total_value',
      headerName: 'Value',
      flex: 1,
      renderCell: ({ row }: { row: GRN }) => (
        <Typography variant="body2" sx={{ textAlign: 'right', fontWeight: 'medium' }}>
          {formatCurrency(row.total_value || 0)}
        </Typography>
      ),
    },
    {
      field: 'received_by_name',
      headerName: 'Received By',
      flex: 1,
    },
  ];

  const rowActions = [
    {
      icon: <ViewIcon />,
      label: 'View',
      onClick: (row: GRN) => handleViewGRN(row),
    },
    {
      icon: <EditIcon />,
      label: 'Edit',
      onClick: (row: GRN) => handleEditGRN(row),
      disabled: (row: GRN) => row.status === 'POSTED' || row.status === 'CANCELLED',
    },
    {
      icon: <CheckCircleIcon />,
      label: 'Mark as Received',
      onClick: (row: GRN) => handleMarkAsReceived(row),
      disabled: (row: GRN) => row.status !== 'DRAFT',
      color: 'primary' as const,
    },
    {
      icon: <PostIcon />,
      label: 'Post to Inventory',
      onClick: (row: GRN) => {
        setSelectedGrn(row);
        setPostDialogOpen(true);
      },
      disabled: (row: GRN) => row.status !== 'RECEIVED',
    },
    {
      icon: <ReceiptIcon />,
      label: 'Create Vendor Bill',
      onClick: (row: GRN) => navigate(`/dashboard/purchases/vendor-bills/create-from-grn?grn_id=${row.grn_id}`),
      disabled: (row: GRN) => row.status !== 'RECEIVED' && row.status !== 'POSTED',
      color: 'success' as const,
    },
    {
      icon: <DeleteIcon />,
      label: 'Delete',
      onClick: (row: GRN) => {
        setSelectedGrn(row);
        setDeleteDialogOpen(true);
      },
      disabled: (row: GRN) => row.status === 'POSTED',
      color: 'error' as const,
    },
  ];

  return (
    <PageContainer>
      <PageHeader>
        <Box>
          <Typography variant="h5" fontWeight="bold">
            Goods Receipt Notes
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Manage goods receipt from purchase orders
          </Typography>
        </Box>
        <Box display="flex" gap={1}>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={loadGRNs}
            disabled={loading}
          >
            Refresh
          </Button>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={handleCreateGRN}
          >
            Create GRN
          </Button>
        </Box>
      </PageHeader>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <DataTable
        columns={columns}
        rows={grns}
        loading={loading}
        totalCount={totalCount}
        page={page}
        pageSize={pageSize}
        onPageChange={setPage}
        onPageSizeChange={setPageSize}
        rowActions={rowActions}
        serverSide={true}
        showToolbar={true}
        title="GRNs"
      />

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Delete GRN</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete GRN {selectedGrn?.grn_number}? This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleDeleteGRN} color="error" variant="contained">
            Delete
          </Button>
        </DialogActions>
      </Dialog>

      {/* Post to Inventory Confirmation Dialog */}
      <Dialog
        open={postDialogOpen}
        onClose={() => setPostDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>Post GRN to Inventory</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to post GRN {selectedGrn?.grn_number} to inventory? 
            This will update stock levels and create stock transactions.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setPostDialogOpen(false)}>Cancel</Button>
          <Button onClick={handlePostToInventory} color="primary" variant="contained">
            Post to Inventory
          </Button>
        </DialogActions>
      </Dialog>
    </PageContainer>
  );
};

export default GoodsReceiptPage; 