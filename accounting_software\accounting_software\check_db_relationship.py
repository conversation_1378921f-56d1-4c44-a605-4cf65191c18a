#!/usr/bin/env python3

import os
import sys
import django

# Add the project directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'erp_backend'))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'erp_backend.settings')
django.setup()

from purchase.models import PurchaseOrder, VendorBill
from sales.models import Product

def check_po_vendor_bills():
    """Check PO vendor bills relationship"""
    print("🔍 Checking PO-Vendor Bills Relationship")
    print("=" * 50)
    
    # Get PO-000033 and PO-000034
    try:
        po_033 = PurchaseOrder.objects.get(po_number="PO-000033")
        po_034 = PurchaseOrder.objects.get(po_number="PO-000034")
        
        print(f"✅ Found PO-000033: ID {po_033.id}")
        print(f"✅ Found PO-000034: ID {po_034.id}")
        
        # Check vendor bills for each
        bills_033 = po_033.vendor_bills.all()
        bills_034 = po_034.vendor_bills.all()
        
        print(f"\n📋 PO-000033 vendor bills: {bills_033.count()}")
        for bill in bills_033:
            print(f"  - Bill: {bill.bill_number} (Status: {bill.status})")
        
        print(f"\n📋 PO-000034 vendor bills: {bills_034.count()}")
        for bill in bills_034:
            print(f"  - Bill: {bill.bill_number} (Status: {bill.status})")
        
        # Check line items and products
        print(f"\n🔍 PO-000033 Line Items:")
        for item in po_033.line_items.all():
            if item.product:
                print(f"  - Product: {item.product.name} (Type: {item.product.product_type})")
            else:
                print(f"  - Description: {item.description} (No product linked)")
        
        print(f"\n🔍 PO-000034 Line Items:")
        for item in po_034.line_items.all():
            if item.product:
                print(f"  - Product: {item.product.name} (Type: {item.product.product_type})")
            else:
                print(f"  - Description: {item.description} (No product linked)")
        
        return po_033, po_034
        
    except PurchaseOrder.DoesNotExist as e:
        print(f"❌ PO not found: {e}")
        return None, None

def check_all_vendor_bills():
    """Check all vendor bills and their PO links"""
    print("\n" + "=" * 50)
    print("📄 ALL VENDOR BILLS")
    print("=" * 50)
    
    bills = VendorBill.objects.all()
    print(f"Total vendor bills: {bills.count()}")
    
    po_linked_bills = bills.filter(purchase_order__isnull=False)
    print(f"PO-linked bills: {po_linked_bills.count()}")
    
    for bill in po_linked_bills:
        print(f"  - Bill {bill.bill_number} → PO {bill.purchase_order.po_number}")

def simulate_filtering():
    """Simulate the filtering logic"""
    print("\n" + "=" * 50)
    print("🎯 SIMULATING FILTERING LOGIC")
    print("=" * 50)
    
    # Get POs with appropriate status
    billable_pos = PurchaseOrder.objects.filter(
        status__in=['sent', 'acknowledged', 'partial', 'received']
    ).select_related('vendor').prefetch_related('line_items__product')
    
    print(f"POs with billable status: {billable_pos.count()}")
    
    service_pos = []
    for po in billable_pos:
        print(f"\n🔍 Checking {po.po_number}:")
        
        # Check vendor bills
        has_bills = po.vendor_bills.exists()
        print(f"  - Has vendor bills: {has_bills}")
        
        if has_bills:
            print(f"  - ❌ Skipping due to existing bills")
            continue
        
        # Check services
        has_services = False
        has_goods = False
        
        for line_item in po.line_items.all():
            if line_item.product:
                product_type = line_item.product.product_type
                print(f"  - Line item product type: {product_type}")
                if product_type == 'service':
                    has_services = True
                elif product_type == 'product':
                    has_goods = True
            else:
                print(f"  - Line item with no product (assuming service)")
                has_services = True
        
        print(f"  - Has services: {has_services}, Has goods: {has_goods}")
        
        if has_services and not has_goods:
            print(f"  - ✅ Including as service PO")
            service_pos.append(po)
        else:
            print(f"  - ❌ Excluding (not pure service PO)")
    
    print(f"\n📊 Result: {len(service_pos)} service POs found")
    for po in service_pos:
        print(f"  - {po.po_number}")

if __name__ == "__main__":
    print("🚀 Database Relationship Check")
    print("=" * 50)
    
    po_033, po_034 = check_po_vendor_bills()
    check_all_vendor_bills()
    simulate_filtering()
    
    print("\n✅ Check completed!")
