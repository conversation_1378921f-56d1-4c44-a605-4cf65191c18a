#!/usr/bin/env python3
"""
Test vendor bill list endpoint
"""
import requests

BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api"
TOKEN = "e15bc01f831c5111f413f534ef82288744cb7d41"

HEADERS = {
    'Content-Type': 'application/json',
    'Authorization': f'Token {TOKEN}'
}

# Test list endpoint
try:
    print("Testing list endpoint...")
    response = requests.get(f"{API_BASE}/purchase/vendor-bills/", headers=HEADERS, timeout=5)
    
    print(f"Status: {response.status_code}")
    print(f"Response: {response.text[:500]}...")
    
    if response.status_code == 200:
        print("✅ List endpoint working")
    else:
        print("❌ List endpoint failed")
        
except Exception as e:
    print(f"❌ Exception: {e}")
