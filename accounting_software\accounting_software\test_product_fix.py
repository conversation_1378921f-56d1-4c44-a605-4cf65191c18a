#!/usr/bin/env python3
"""
Test product assignment fix
"""
import requests
import json

# Configuration
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api"
TOKEN = "e15bc01f831c5111f413f534ef82288744cb7d41"

HEADERS = {
    'Content-Type': 'application/json',
    'Authorization': f'Token {TOKEN}'
}

def test_product_assignment():
    print("🔧 TESTING PRODUCT ASSIGNMENT FIX")
    print("=" * 50)
    
    try:
        # 1. Get available products
        print("1. Getting available products...")
        products_response = requests.get(f"{API_BASE}/sales/products/", headers=HEADERS, timeout=5)
        
        if products_response.status_code == 200:
            products_data = products_response.json()
            if products_data.get('results'):
                products = products_data['results']
                print(f"✅ Found {len(products)} products")
                
                # Show first few products
                for i, product in enumerate(products[:5]):
                    print(f"   Product {product['id']}: {product['name']}")
                
                # Use the first product for testing
                test_product_id = products[0]['id']
                test_product_name = products[0]['name']
                print(f"\n📦 Using product ID {test_product_id}: {test_product_name}")
                
            else:
                print("❌ No products found")
                return False
        else:
            print(f"❌ Failed to get products: {products_response.status_code}")
            return False
        
        # 2. Get vendor
        print("\n2. Getting vendor...")
        vendors_response = requests.get(f"{API_BASE}/contacts/vendors/", headers=HEADERS, timeout=5)
        vendor_id = vendors_response.json()['results'][0]['contact']
        print(f"✅ Using vendor ID: {vendor_id}")
        
        # 3. Test vendor bill with real product
        print(f"\n3. Testing vendor bill with product ID {test_product_id}...")
        bill_data = {
            "vendor": vendor_id,
            "bill_date": "2025-07-06",
            "due_date": "2025-08-05",
            "status": "draft",
            "payment_terms": "Net 30",
            "reference_number": "PRODUCT-TEST-001",
            "notes": "Testing with real product",
            "line_items": [
                {
                    "product": test_product_id,  # Use real product ID
                    "item_description": f"Testing {test_product_name}",
                    "quantity": 2,
                    "unit_price": 150.00,
                    "tax_rate": 10.0,
                    "account_code": "5010-COGS",
                    "line_order": 1
                },
                {
                    "item_description": "Service without product",
                    "quantity": 1,
                    "unit_price": 100.00,
                    "tax_rate": 10.0,
                    "account_code": "5020-Services",
                    "line_order": 2
                }
            ]
        }
        
        print("Sending bill data:")
        print(json.dumps(bill_data, indent=2))
        
        response = requests.post(f"{API_BASE}/purchase/vendor-bills/", json=bill_data, headers=HEADERS, timeout=10)
        
        print(f"\nStatus Code: {response.status_code}")
        
        if response.status_code == 201:
            print("✅ SUCCESS! Vendor bill with product created!")
            data = response.json()
            print(f"Bill ID: {data['id']}")
            print(f"Bill Number: {data['bill_number']}")
            print(f"Total Amount: ${data['total_amount']}")
            print(f"Line Items: {len(data['line_items'])}")
            
            # Show line items
            for i, item in enumerate(data['line_items']):
                product_info = f" (Product: {item.get('product_name', 'N/A')})" if item.get('product_name') else ""
                print(f"  Item {i+1}: {item['item_description']}{product_info} - ${item['line_total']}")
            
            return True
        else:
            print(f"❌ FAILED! Status: {response.status_code}")
            try:
                error_data = response.json()
                print("Error details:")
                print(json.dumps(error_data, indent=2))
            except:
                print(f"Raw error: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_product_assignment()
    
    print("\n" + "=" * 50)
    print("📋 PRODUCT ASSIGNMENT TEST SUMMARY")
    print("=" * 50)
    
    if success:
        print("🎉 PRODUCT ASSIGNMENT IS NOW WORKING!")
        print("✅ Frontend can now select products successfully")
        print("✅ Product IDs are properly converted to Product instances")
        print("✅ Both product-based and service-based line items work")
        print("\n💡 Your frontend should now work with:")
        print("   - Gleam and Gym products")
        print("   - Any other products in the system")
        print("   - Mixed line items (products + services)")
    else:
        print("❌ Product assignment still has issues")
        print("🔧 Check the error details above")
