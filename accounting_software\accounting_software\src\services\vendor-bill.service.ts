import api from './api';

// Vendor Bill Types
export interface VendorBillVendor {
  id: number;
  display_name: string;
  email?: string;
  phone?: string;
  billing_address?: string;
  payment_terms?: string;
}

export interface VendorBillItem {
  id?: number;
  product?: number;
  product_name?: string;
  item_description: string;
  quantity: number;
  unit_price: number;
  line_total: number;
  tax_rate?: number;
  tax_amount?: number;
  account_code?: string;
  line_order?: number;
}

export interface VendorBill {
  id: number;
  bill_number: string;
  vendor: number;
  vendor_details?: VendorBillVendor;
  vendor_name?: string; // Computed field for display
  bill_date: string;
  due_date: string;
  status: 'draft' | 'posted' | 'paid';
  line_items: VendorBillItem[];
  subtotal: number;
  tax_amount: number;
  total_amount: number;
  amount_paid: number;
  balance_due: number;
  notes?: string;
  payment_terms?: string;
  reference_number?: string;
  source_type?: 'manual' | 'grn' | 'po' | 'return_note';
  created_at?: string;
  updated_at?: string;
}

export interface VendorBillFilters {
  status?: string;
  vendor?: number;
  date_from?: string;
  date_to?: string;
  search?: string;
}

export interface VendorBillStats {
  total_bills: number;
  total_payables: number;
  outstanding_amount: number;
  overdue_count: number;
  draft_count: number;
  paid_count: number;
}

class VendorBillService {
  private baseUrl = '/purchase';

  // Get all vendor bills with optional filters
  async getVendorBills(filters?: VendorBillFilters): Promise<{ results: VendorBill[]; count: number }> {
    const params = new URLSearchParams();
    if (filters?.status) params.append('status', filters.status);
    if (filters?.vendor) params.append('vendor', filters.vendor.toString());
    if (filters?.date_from) params.append('date_from', filters.date_from);
    if (filters?.date_to) params.append('date_to', filters.date_to);
    if (filters?.search) params.append('search', filters.search);

    const response = await api.get(`${this.baseUrl}/vendor-bills/?${params}`);

    // Map the response to include vendor_name for display
    const mappedResults = response.data.results?.map((bill: any) => ({
      ...bill,
      vendor_name: bill.vendor_details?.display_name || bill.vendor_details?.name || 'Unknown Vendor'
    })) || [];

    return {
      results: mappedResults,
      count: response.data.count || 0
    };
  }

  // Get single vendor bill by ID
  async getVendorBill(id: number): Promise<VendorBill> {
    const response = await api.get(`${this.baseUrl}/vendor-bills/${id}/`);
    return response.data;
  }

  // Create new vendor bill
  async createVendorBill(billData: Omit<VendorBill, 'id' | 'bill_number' | 'created_at' | 'updated_at'>): Promise<VendorBill> {
    const response = await api.post(`${this.baseUrl}/vendor-bills/`, billData);
    return response.data;
  }

  // Update existing vendor bill
  async updateVendorBill(id: number, billData: Partial<VendorBill>): Promise<VendorBill> {
    const response = await api.patch(`${this.baseUrl}/vendor-bills/${id}/`, billData);
    return response.data;
  }

  // Delete vendor bill
  async deleteVendorBill(id: number): Promise<void> {
    await api.delete(`${this.baseUrl}/vendor-bills/${id}/`);
  }

  // Get vendor bill statistics
  async getVendorBillStats(): Promise<VendorBillStats> {
    const response = await api.get(`${this.baseUrl}/vendor-bills/stats/`);
    return response.data;
  }

  // Mark vendor bill as paid
  async markAsPaid(id: number, paymentData: { payment_date: string; payment_amount: number; payment_method?: string; notes?: string }): Promise<VendorBill> {
    const response = await api.post(`${this.baseUrl}/vendor-bills/${id}/mark-paid/`, paymentData);
    return response.data;
  }

  // Send vendor bill (if applicable)
  async sendVendorBill(id: number, emailData?: { to_email?: string; subject?: string; message?: string }): Promise<void> {
    await api.post(`${this.baseUrl}/vendor-bills/${id}/send/`, emailData || {});
  }

  // Duplicate vendor bill
  async duplicateVendorBill(id: number): Promise<VendorBill> {
    const response = await api.post(`${this.baseUrl}/vendor-bills/${id}/duplicate/`);
    return response.data;
  }

  // Get vendor bills for a specific vendor
  async getVendorBillsByVendor(vendorId: number): Promise<VendorBill[]> {
    const response = await api.get(`${this.baseUrl}/vendor-bills/?vendor=${vendorId}`);
    return response.data.results || [];
  }

  // Get overdue vendor bills
  async getOverdueVendorBills(): Promise<VendorBill[]> {
    const response = await api.get(`${this.baseUrl}/vendor-bills/?status=overdue`);
    return response.data.results || [];
  }

  // Get vendor bills summary for dashboard
  async getVendorBillsSummary(): Promise<{
    total_bills: number;
    total_amount: number;
    paid_amount: number;
    outstanding_amount: number;
    overdue_amount: number;
  }> {
    const response = await api.get(`${this.baseUrl}/vendor-bills/summary/`);
    return response.data;
  }

  // Create vendor bill from Purchase Order
  async createVendorBillFromPO(poId: number): Promise<VendorBill> {
    const response = await api.post(`${this.baseUrl}/vendor-bills/create_from_po/`, {
      po_id: poId
    });

    if (response.status !== 201) {
      throw new Error('Failed to create vendor bill from PO');
    }

    return response.data;
  }

  // Create vendor bill from Goods Receipt Note
  async createVendorBillFromGRN(grnId: number): Promise<VendorBill> {
    const response = await api.post(`${this.baseUrl}/vendor-bills/create_from_grn/`, {
      grn_id: grnId
    });

    if (response.status !== 201) {
      throw new Error('Failed to create vendor bill from GRN');
    }

    return response.data;
  }

  // Create vendor bill from Return Note
  async createVendorBillFromReturnNote(returnNoteId: number): Promise<VendorBill> {
    const response = await api.post(`${this.baseUrl}/vendor-bills/create_from_return_note/`, {
      return_note_id: returnNoteId
    });

    if (response.status !== 201) {
      throw new Error('Failed to create vendor bill from Return Note');
    }

    return response.data;
  }

  // Get billable GRNs (Goods Receipt Notes)
  async getBillableGRNs(): Promise<any[]> {
    const response = await api.get(`${this.baseUrl}/purchase-orders/billable_grns/`);
    return response.data;
  }
}

export const vendorBillService = new VendorBillService();
