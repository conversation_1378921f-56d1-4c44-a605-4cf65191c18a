import React from 'react';
import {
  Card,
  CardContent,
  Typography,
  Grid,
  Box,
  LinearProgress,
  Tooltip,
  IconButton,
} from '@mui/material';
import {
  Info as InfoIcon,
} from '@mui/icons-material';
import { formatCurrency } from '../../../shared/utils/formatters';
import { StockValuation } from '../services/inventory.service';
import { useCurrencyInfo } from '../../gl/hooks/useCurrencyInfo';

interface ValuationMethodsComparisonProps {
  valuation: StockValuation;
  showPercentages?: boolean;
  showDescriptions?: boolean;
}

const ValuationMethodsComparison: React.FC<ValuationMethodsComparisonProps> = ({
  valuation,
  showPercentages = true,
  showDescriptions = false
}) => {
  const { currencyInfo } = useCurrencyInfo();

  const methods = [
    {
      name: 'FIFO',
      fullName: 'First In, First Out',
      value: valuation.fifo_total_value,
      color: 'primary',
      description: 'Values inventory using the cost of the oldest items first. Best for businesses with perishable goods or when costs are rising.'
    },
    {
      name: 'LIFO',
      fullName: 'Last In, First Out',
      value: valuation.lifo_total_value,
      color: 'secondary',
      description: 'Values inventory using the cost of the newest items first. Often used for tax benefits when costs are rising.'
    },
    {
      name: 'Average',
      fullName: 'Weighted Average',
      value: valuation.average_total_value,
      color: 'success',
      description: 'Values inventory using the average cost of all items. Smooths out price fluctuations and is widely accepted.'
    },
    {
      name: 'Standard',
      fullName: 'Standard Cost',
      value: valuation.standard_total_value,
      color: 'warning',
      description: 'Values inventory using predetermined standard costs. Useful for budgeting and variance analysis.'
    }
  ];

  const maxValue = Math.max(...methods.map(m => m.value));
  const minValue = Math.min(...methods.map(m => m.value));
  const range = maxValue - minValue;

  const getPercentage = (value: number) => {
    if (range === 0) return 100;
    return ((value - minValue) / range) * 100;
  };

  const getVarianceFromAverage = (value: number) => {
    const average = valuation.average_total_value;
    if (average === 0) return 0;
    return ((value - average) / average) * 100;
  };

  return (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          Valuation Methods Comparison
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
          Compare inventory values using different costing methods
        </Typography>

        <Grid container spacing={3}>
          {methods.map((method) => (
            <Grid item xs={12} sm={6} md={3} key={method.name}>
              <Card variant="outlined" sx={{ height: '100%' }}>
                <CardContent>
                  <Box display="flex" alignItems="center" justifyContent="space-between" mb={1}>
                    <Typography variant="h6" color={`${method.color}.main`}>
                      {method.name}
                    </Typography>
                    {showDescriptions && (
                      <Tooltip title={method.description} arrow>
                        <IconButton size="small">
                          <InfoIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    )}
                  </Box>
                  
                  <Typography variant="caption" color="text.secondary" display="block">
                    {method.fullName}
                  </Typography>
                  
                  <Typography variant="h4" sx={{ my: 2 }}>
                    {formatCurrency(method.value, currencyInfo)}
                  </Typography>

                  {showPercentages && (
                    <>
                      <Box sx={{ mb: 1 }}>
                        <LinearProgress
                          variant="determinate"
                          value={getPercentage(method.value)}
                          color={method.color as any}
                          sx={{ height: 8, borderRadius: 4 }}
                        />
                      </Box>
                      
                      <Box display="flex" justifyContent="space-between" alignItems="center">
                        <Typography variant="caption" color="text.secondary">
                          vs Range
                        </Typography>
                        <Typography variant="caption" color={`${method.color}.main`}>
                          {getPercentage(method.value).toFixed(1)}%
                        </Typography>
                      </Box>

                      <Box display="flex" justifyContent="space-between" alignItems="center" mt={0.5}>
                        <Typography variant="caption" color="text.secondary">
                          vs Average
                        </Typography>
                        <Typography 
                          variant="caption" 
                          color={getVarianceFromAverage(method.value) >= 0 ? 'success.main' : 'error.main'}
                        >
                          {getVarianceFromAverage(method.value) >= 0 ? '+' : ''}
                          {getVarianceFromAverage(method.value).toFixed(1)}%
                        </Typography>
                      </Box>
                    </>
                  )}
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>

        {/* Summary Statistics */}
        <Box mt={3} pt={3} borderTop={1} borderColor="divider">
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6} md={3}>
              <Typography variant="body2" color="text.secondary">
                Highest Value
              </Typography>
              <Typography variant="h6" color="success.main">
                {formatCurrency(maxValue, currencyInfo)}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                {methods.find(m => m.value === maxValue)?.name}
              </Typography>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Typography variant="body2" color="text.secondary">
                Lowest Value
              </Typography>
              <Typography variant="h6" color="error.main">
                {formatCurrency(minValue, currencyInfo)}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                {methods.find(m => m.value === minValue)?.name}
              </Typography>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Typography variant="body2" color="text.secondary">
                Value Range
              </Typography>
              <Typography variant="h6">
                {formatCurrency(range, currencyInfo)}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                Difference
              </Typography>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Typography variant="body2" color="text.secondary">
                Range %
              </Typography>
              <Typography variant="h6">
                {maxValue > 0 ? ((range / maxValue) * 100).toFixed(1) : 0}%
              </Typography>
              <Typography variant="caption" color="text.secondary">
                of max value
              </Typography>
            </Grid>
          </Grid>
        </Box>

        {showDescriptions && (
          <Box mt={3} pt={3} borderTop={1} borderColor="divider">
            <Typography variant="subtitle2" gutterBottom>
              Method Descriptions
            </Typography>
            {methods.map((method) => (
              <Box key={method.name} mb={1}>
                <Typography variant="body2">
                  <strong>{method.fullName} ({method.name}):</strong> {method.description}
                </Typography>
              </Box>
            ))}
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

export default ValuationMethodsComparison;
