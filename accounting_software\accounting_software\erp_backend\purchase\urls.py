from django.urls import path, include
from rest_framework.routers import Default<PERSON><PERSON><PERSON>
from . import views

# Create a router and register our viewsets with it
router = DefaultRouter()
router.register(r'vendors', views.VendorViewSet, basename='vendor')
router.register(r'vendor-bills', views.VendorBillViewSet, basename='vendor-bill')
router.register(r'purchase-orders', views.PurchaseOrderViewSet, basename='purchase-order')
router.register(r'payment-terms', views.PaymentTermViewSet, basename='payment-term')

# The API URLs are now determined automatically by the router
urlpatterns = [
    path('', include(router.urls)),
] 