# Tax Calculation Fix for Vendor Bills

## Issue Fixed
When selecting a sales tax rate from the dropdown in vendor bill line items, the tax calculation area did not update immediately. The tax amount would only show after selecting another product or making other changes.

## Root Cause
The `calculateTotals` function in `CreateVendorBillPage.tsx` was only summing existing `tax_amount` values instead of recalculating them when tax rates changed. Additionally, there was a timing issue with the `setTimeout` approach for triggering calculations.

## Solution Implemented

### 1. Updated `handleLineChange` Function
- **Before**: Used `setTimeout(calculateTotals, 100)` which caused delays
- **After**: Immediate calculation within the state update function
- **Benefit**: Tax amounts update instantly when tax rate changes

### 2. Enhanced Calculation Logic
- **Before**: Only summed existing `line_total` and `tax_amount` values
- **After**: Recalculates both `line_total` and `tax_amount` for each line item
- **Formula**: 
  - `line_total = quantity × unit_price`
  - `tax_amount = line_total × (tax_rate / 100)`

### 3. Improved State Management
- **Before**: Separate calls to update line items and totals
- **After**: Single state update that handles both line items and totals
- **Benefit**: Prevents race conditions and ensures consistency

## Code Changes

### Updated `handleLineChange` Function:
```typescript
const handleLineChange = (lineId: string, field: string, value: any) => {
  setFormData(prev => {
    const updatedLineItems = prev.line_items.map(item =>
      item.id === lineId ? { ...item, [field]: value } : item
    );
    
    // Recalculate line totals and tax amounts immediately
    const recalculatedLineItems = updatedLineItems.map(item => {
      const lineTotal = (item.quantity || 0) * (item.unit_price || 0);
      const taxAmount = lineTotal * ((item.tax_rate || 0) / 100);
      
      return {
        ...item,
        line_total: lineTotal,
        tax_amount: taxAmount
      };
    });

    // Calculate overall totals
    const subtotal = recalculatedLineItems.reduce((sum, item) => sum + (item.line_total || 0), 0);
    const taxAmount = recalculatedLineItems.reduce((sum, item) => sum + (item.tax_amount || 0), 0);
    const totalAmount = subtotal + taxAmount;

    return {
      ...prev,
      line_items: recalculatedLineItems,
      subtotal,
      tax_amount: taxAmount,
      total_amount: totalAmount
    };
  });
};
```

### Updated `calculateTotals` Function:
```typescript
const calculateTotals = () => {
  // Recalculate line totals and tax amounts for each line item
  const updatedLineItems = formData.line_items.map(item => {
    const lineTotal = (item.quantity || 0) * (item.unit_price || 0);
    const taxAmount = lineTotal * ((item.tax_rate || 0) / 100);
    
    return {
      ...item,
      line_total: lineTotal,
      tax_amount: taxAmount
    };
  });

  // Calculate overall totals
  const subtotal = updatedLineItems.reduce((sum, item) => sum + (item.line_total || 0), 0);
  const taxAmount = updatedLineItems.reduce((sum, item) => sum + (item.tax_amount || 0), 0);
  const totalAmount = subtotal + taxAmount;

  setFormData(prev => ({
    ...prev,
    line_items: updatedLineItems,
    subtotal,
    tax_amount: taxAmount,
    total_amount: totalAmount
  }));
};
```

## Expected Behavior After Fix

1. **Immediate Tax Calculation**: When user selects a tax rate from dropdown, tax amount appears instantly
2. **Real-time Updates**: Calculation area updates immediately without needing to click elsewhere
3. **Consistent Calculations**: All line item changes (quantity, price, tax rate) trigger immediate recalculation
4. **Accurate Totals**: Subtotal, tax amount, and total amount are always in sync

## Testing Instructions

1. Open vendor bill creation page
2. Add a line item with quantity and unit price
3. Select a tax rate from the dropdown
4. **Expected**: Tax amount should appear immediately in the calculation area
5. Change tax rate to different value
6. **Expected**: Tax amount should update instantly
7. Verify that subtotal, tax amount, and total are all correct

## Files Modified

- `src/domains/purchase/pages/CreateVendorBillPage.tsx`
  - Updated `handleLineChange` function
  - Updated `calculateTotals` function  
  - Updated `handleRemoveLine` function

## Impact

- ✅ **Improved User Experience**: Immediate feedback when selecting tax rates
- ✅ **Better Performance**: Eliminates unnecessary timeouts and delays
- ✅ **More Reliable**: Prevents race conditions in state updates
- ✅ **Consistent Behavior**: All field changes trigger immediate recalculation
