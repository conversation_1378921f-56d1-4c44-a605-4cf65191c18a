// Quick test script to check customer invoice API endpoints
const API_BASE = 'http://localhost:8000/api';

async function testCustomerInvoiceAPI() {
  console.log('🧪 Testing Customer Invoice API Endpoints...');
  
  // Get token from localStorage (you'll need to be logged in)
  const token = localStorage.getItem('token');
  if (!token) {
    console.error('❌ No authentication token found. Please log in first.');
    return;
  }

  const headers = {
    'Authorization': `Token ${token}`,
    'Content-Type': 'application/json'
  };

  // Test 1: Check if customer invoices endpoint exists
  try {
    console.log('\n📋 Testing customer invoices list endpoint...');
    const response = await fetch(`${API_BASE}/sales/customer-invoices/`, { headers });
    console.log(`Status: ${response.status}`);
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ Customer invoices endpoint working');
      console.log(`Found ${data.results?.length || 0} invoices`);
      console.log('Sample response:', JSON.stringify(data, null, 2).substring(0, 500) + '...');
    } else {
      console.log('❌ Customer invoices endpoint failed');
      const errorText = await response.text();
      console.log('Error response:', errorText);
    }
  } catch (error) {
    console.error('❌ Network error testing customer invoices:', error);
  }

  // Test 2: Check if stats endpoint exists
  try {
    console.log('\n📊 Testing customer invoice stats endpoint...');
    const response = await fetch(`${API_BASE}/sales/customer-invoices/stats/`, { headers });
    console.log(`Status: ${response.status}`);
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ Customer invoice stats endpoint working');
      console.log('Stats:', JSON.stringify(data, null, 2));
    } else {
      console.log('❌ Customer invoice stats endpoint failed');
      const errorText = await response.text();
      console.log('Error response:', errorText);
    }
  } catch (error) {
    console.error('❌ Network error testing stats:', error);
  }

  // Test 3: Check if contacts endpoint exists
  try {
    console.log('\n👥 Testing contacts endpoint...');
    const response = await fetch(`${API_BASE}/contacts/contacts/?contact_type=customer`, { headers });
    console.log(`Status: ${response.status}`);
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ Contacts endpoint working');
      console.log(`Found ${data.results?.length || 0} customers`);
      if (data.results?.length > 0) {
        console.log('Sample customer:', JSON.stringify(data.results[0], null, 2));
      }
    } else {
      console.log('❌ Contacts endpoint failed');
      const errorText = await response.text();
      console.log('Error response:', errorText);
    }
  } catch (error) {
    console.error('❌ Network error testing contacts:', error);
  }

  // Test 4: Check server health
  try {
    console.log('\n🏥 Testing server health...');
    const response = await fetch(`${API_BASE}/`, { headers });
    console.log(`Server status: ${response.status}`);
    
    if (response.ok) {
      console.log('✅ Server is responding');
    } else {
      console.log('⚠️ Server responded with error status');
    }
  } catch (error) {
    console.error('❌ Server is not responding:', error);
  }

  console.log('\n🏁 API testing complete!');
}

// Run the test
testCustomerInvoiceAPI();
