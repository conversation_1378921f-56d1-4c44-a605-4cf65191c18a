#!/usr/bin/env python3
"""
Test vendor bill update functionality
"""
import requests

BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api"
TOKEN = "e15bc01f831c5111f413f534ef82288744cb7d41"

HEADERS = {
    'Content-Type': 'application/json',
    'Authorization': f'Token {TOKEN}'
}

# Test update functionality
try:
    # Get the latest bill
    bills_response = requests.get(f"{API_BASE}/purchase/vendor-bills/", headers=HEADERS, timeout=5)
    bills = bills_response.json()['results']
    
    if bills:
        bill_id = bills[0]['id']
        print(f"Testing update of bill ID: {bill_id}")
        
        # Get vendor and products for update
        vendors_response = requests.get(f"{API_BASE}/contacts/vendors/", headers=HEADERS, timeout=5)
        vendor_id = vendors_response.json()['results'][0]['contact']
        
        products_response = requests.get(f"{API_BASE}/sales/products/", headers=HEADERS, timeout=5)
        products = products_response.json()['results']
        product_id = products[0]['id'] if products else None
        
        # Update data
        update_data = {
            "vendor": vendor_id,
            "bill_date": "2025-07-07",
            "due_date": "2025-08-07",
            "status": "draft",
            "payment_terms": "Net 45",
            "reference_number": "UPDATED-BILL-001",
            "notes": "Updated vendor bill test",
            "line_items": [
                {
                    "product": product_id,
                    "item_description": "Updated Product Line Item",
                    "quantity": 5,
                    "unit_price": 200.00,
                    "tax_rate": 10.0,
                    "account_code": "5010-COGS",
                    "line_order": 1
                },
                {
                    "item_description": "Updated Service Line Item",
                    "quantity": 2,
                    "unit_price": 300.00,
                    "tax_rate": 5.0,
                    "account_code": "5020-Services",
                    "line_order": 2
                }
            ]
        }
        
        # Update the bill
        update_response = requests.patch(f"{API_BASE}/purchase/vendor-bills/{bill_id}/", json=update_data, headers=HEADERS, timeout=10)
        
        print(f"Update status: {update_response.status_code}")
        if update_response.status_code == 200:
            updated_bill = update_response.json()
            print(f"✅ SUCCESS - Bill updated: {updated_bill['bill_number']}")
            print(f"New total: ${updated_bill['total_amount']}")
            print(f"New line items: {len(updated_bill['line_items'])}")
            print(f"Payment terms: {updated_bill['payment_terms']}")
            print(f"Reference: {updated_bill['reference_number']}")
        else:
            print(f"❌ ERROR: {update_response.text}")
    else:
        print("No bills found to update")
        
except Exception as e:
    print(f"❌ Exception: {e}")
    import traceback
    traceback.print_exc()
