// Product import removed - product.types.ts was deleted during cleanup

export type PurchaseStatus = 'draft' | 'pending' | 'approved' | 'received' | 'cancelled' | 'paid' | 'overdue';

export interface PurchaseLineItem {
  id: string;
  productId: string;
  description: string;
  quantity: number;
  unitPrice: number;
  taxRate: number;
  taxAmount: number;
  subtotal: number;
  total: number;
  // product?: Product; // Removed - Product type no longer available
}

export interface Bill {
  id: string;
  billNumber: string;
  vendorId: number; // Changed from string to number to match backend
  vendorName: string;
  date: string;
  dueDate: string;
  status: PurchaseStatus;
  subtotal: number;
  taxTotal: number;
  total: number;
  notes?: string;
  terms?: string;
  attachments?: string[];
  lineItems: PurchaseLineItem[];
  createdAt: string;
  updatedAt: string;
  paymentDate?: string;
  paymentMethod?: string;
  paymentReference?: string;
  expenseAccountId?: string;
}

export interface PurchaseOrder {
  id: string;
  poNumber: string;
  vendorId: number; // Changed from string to number to match backend
  vendorName: string;
  date: string;
  expectedDate: string;
  status: PurchaseStatus;
  subtotal: number;
  taxTotal: number;
  total: number;
  notes?: string;
  terms?: string;
  attachments?: string[];
  lineItems: PurchaseLineItem[];
  createdAt: string;
  updatedAt: string;
  approvedBy?: string;
  approvedDate?: string;
  receivedDate?: string;
}

export interface Vendor {
  id: number; // Changed from string to number to match backend
  name: string;
  contactName: string;
  email: string;
  phone: string;
  address: {
    street: string;
    city: string;
    state: string;
    postalCode: string;
    country: string;
  };
  taxId?: string;
  paymentTerms?: string;
  status: 'active' | 'inactive';
  notes?: string;
  attachments?: File[];
  balance: number;
  createdAt: string;
  updatedAt: string;
}

export interface BillFormData extends Omit<Bill, 'id' | 'createdAt' | 'updatedAt'> {}
export interface PurchaseOrderFormData extends Omit<PurchaseOrder, 'id' | 'createdAt' | 'updatedAt'> {}
export interface VendorFormData extends Omit<Vendor, 'id' | 'createdAt' | 'updatedAt' | 'balance'> {}

export type PaymentTermOption = {
  value: string;
  label: string;
  days: number;
};

export const PAYMENT_TERMS: PaymentTermOption[] = [
  { value: 'due_on_receipt', label: 'Due on Receipt', days: 0 },
  { value: 'net_7', label: 'Net 7', days: 7 },
  { value: 'net_15', label: 'Net 15', days: 15 },
  { value: 'net_30', label: 'Net 30', days: 30 },
  { value: 'net_45', label: 'Net 45', days: 45 },
  { value: 'net_60', label: 'Net 60', days: 60 },
  { value: 'custom', label: 'Custom', days: 0 }
];

export const COUNTRIES = [
  { value: 'US', label: 'United States' },
  { value: 'CA', label: 'Canada' },
  { value: 'UK', label: 'United Kingdom' },
  { value: 'AU', label: 'Australia' },
  { value: 'DE', label: 'Germany' },
  { value: 'FR', label: 'France' },
  { value: 'IN', label: 'India' },
  { value: 'CN', label: 'China' },
  { value: 'JP', label: 'Japan' },
  { value: 'BR', label: 'Brazil' },
  { value: 'MX', label: 'Mexico' },
  { value: 'ZA', label: 'South Africa' },
  { value: 'NG', label: 'Nigeria' },
  { value: 'EG', label: 'Egypt' },
  { value: 'SA', label: 'Saudi Arabia' }
];

// Purchase Product Types
export type PurchaseProductType = 'product' | 'service' | 'bundle';

export interface PurchaseBundleItem {
  productId: string;
  name?: string;
  quantity: number;
}

export interface PurchaseProduct {
  id: string;
  name: string;
  sku: string;
  description?: string;
  type: PurchaseProductType;
  category: string;
  purchasePrice: number;
  vendorId?: string;
  vendorName?: string;
  vendorSKU?: string;
  leadTime?: number; // in days
  minimumOrderQuantity?: number;
  preferredVendor?: boolean;
  trackInventory: boolean;
  inventoryQuantity?: number;
  lowStockAlert?: boolean;
  lowStockThreshold?: number;
  status: 'active' | 'inactive';
  bundleItems?: PurchaseBundleItem[];
  displayBundleItems?: boolean;
  notes?: string;
  attachments?: File[];
  createdAt: string;
  updatedAt: string;
}

export interface PurchaseProductFormData extends Omit<PurchaseProduct, 'id' | 'createdAt' | 'updatedAt'> {
  workflowStatus?: 'pending_sales_review' | 'active' | 'draft';
  createdByDepartment?: 'purchase' | 'sales';
  purchaseCompleted?: boolean;
  salesCompleted?: boolean;
  workflowNotes?: string;
}

export const PURCHASE_PRODUCT_CATEGORIES = [
  { value: 'raw_materials', label: 'Raw Materials' },
  { value: 'components', label: 'Components' },
  { value: 'finished_goods', label: 'Finished Goods' },
  { value: 'office_supplies', label: 'Office Supplies' },
  { value: 'equipment', label: 'Equipment' },
  { value: 'services', label: 'Services' },
  { value: 'software', label: 'Software' },
  { value: 'maintenance', label: 'Maintenance' },
  { value: 'consulting', label: 'Consulting' },
  { value: 'other', label: 'Other' }
]; 