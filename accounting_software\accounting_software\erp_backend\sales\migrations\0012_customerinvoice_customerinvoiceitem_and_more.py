# Generated by Django 4.2.21 on 2025-07-07 23:12

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('contacts', '0006_remove_pricingrule_customer_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('sales', '0011_update_customer_references'),
    ]

    operations = [
        migrations.CreateModel(
            name='CustomerInvoice',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('invoice_id', models.UUIDField(default=uuid.uuid4, unique=True)),
                ('invoice_number', models.CharField(help_text="Unique identifier (e.g., 'INV-000001')", max_length=50, unique=True)),
                ('invoice_date', models.DateField(help_text='Date of invoice issuance')),
                ('due_date', models.DateField(help_text='Payment due date')),
                ('subtotal', models.DecimalField(decimal_places=2, default=0.0, help_text='Before tax and discounts', max_digits=15)),
                ('discount_amount', models.DecimalField(decimal_places=2, default=0.0, help_text='Total discount amount', max_digits=15)),
                ('shipping_amount', models.DecimalField(decimal_places=2, default=0.0, help_text='Shipping charges', max_digits=15)),
                ('tax_amount', models.DecimalField(decimal_places=2, default=0.0, help_text='Calculated tax amount', max_digits=15)),
                ('total_amount', models.DecimalField(decimal_places=2, default=0.0, help_text='Final receivable amount', max_digits=15)),
                ('amount_paid', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('balance_due', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('posted', 'Posted'), ('sent', 'Sent'), ('partial', 'Partially Paid'), ('paid', 'Paid'), ('overdue', 'Overdue'), ('void', 'Void')], default='draft', max_length=20)),
                ('reference_number', models.CharField(blank=True, help_text='Customer PO number or reference', max_length=100, null=True)),
                ('notes', models.TextField(blank=True, help_text='Internal notes', null=True)),
                ('terms_and_conditions', models.TextField(blank=True, help_text='Terms and conditions for customer', null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='customer_invoices_created', to=settings.AUTH_USER_MODEL)),
                ('customer', models.ForeignKey(blank=True, help_text='Customer from contacts system', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='customer_invoices', to='contacts.contact')),
                ('delivery_note', models.ForeignKey(blank=True, help_text='Linked Delivery Note for goods invoices', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='customer_invoices', to='sales.deliverynote')),
                ('payment_terms', models.ForeignKey(blank=True, help_text='Payment terms for this invoice', null=True, on_delete=django.db.models.deletion.SET_NULL, to='sales.paymentterm')),
                ('sales_order', models.ForeignKey(blank=True, help_text='Linked Sales Order for service invoices', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='customer_invoices', to='sales.salesorder')),
            ],
            options={
                'verbose_name': 'Customer Invoice',
                'verbose_name_plural': 'Customer Invoices',
                'db_table': 'sales_customer_invoices',
                'ordering': ['-invoice_date', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='CustomerInvoiceItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('item_description', models.TextField(help_text='Product/service description')),
                ('quantity', models.DecimalField(decimal_places=2, default=1.0, max_digits=10)),
                ('unit_price', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('discount_percent', models.DecimalField(decimal_places=2, default=0.0, max_digits=5)),
                ('line_total', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('tax_rate', models.DecimalField(decimal_places=2, default=0.0, help_text='Tax rate percentage', max_digits=5)),
                ('tax_amount', models.DecimalField(decimal_places=2, default=0.0, max_digits=15)),
                ('account_code', models.CharField(blank=True, help_text='GL account code for revenue', max_length=20, null=True)),
                ('line_order', models.PositiveIntegerField(default=0)),
                ('invoice', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='line_items', to='sales.customerinvoice')),
                ('product', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='sales.product')),
            ],
            options={
                'verbose_name': 'Customer Invoice Item',
                'verbose_name_plural': 'Customer Invoice Items',
                'db_table': 'sales_customer_invoice_items',
                'ordering': ['line_order'],
            },
        ),
        migrations.RemoveField(
            model_name='invoice',
            name='created_by',
        ),
        migrations.RemoveField(
            model_name='invoice',
            name='customer',
        ),
        migrations.DeleteModel(
            name='InvoiceLineItem',
        ),
        migrations.AlterField(
            model_name='estimate',
            name='converted_to_invoice',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='sales.customerinvoice'),
        ),
        migrations.AlterField(
            model_name='payment',
            name='invoice',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='payments', to='sales.customerinvoice'),
        ),
        migrations.DeleteModel(
            name='Invoice',
        ),
    ]
