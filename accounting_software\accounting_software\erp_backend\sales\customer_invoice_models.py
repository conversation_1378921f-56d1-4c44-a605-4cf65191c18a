"""
Customer Invoice Models - Sales Module
Replica of Vendor Bills but for Accounts Receivable (money customers owe us)
"""

import uuid
from django.db import models
from django.contrib.auth.models import User
from django.utils import timezone
from datetime import timedelta

from contacts.models import Contact
from .models import Product, PaymentTerm, SalesOrder, DeliveryNote


class CustomerInvoice(models.Model):
    """Customer Invoice model for Accounts Receivable - opposite of VendorBill"""

    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('posted', 'Posted'),
        ('sent', 'Sent'),
        ('partial', 'Partially Paid'),
        ('paid', 'Paid'),
        ('overdue', 'Overdue'),
        ('void', 'Void'),
    ]

    # Basic Information
    invoice_id = models.UUIDField(default=uuid.uuid4, unique=True)
    invoice_number = models.CharField(max_length=50, unique=True, help_text="Unique identifier (e.g., 'INV-000001')")
    customer = models.ForeignKey(
        Contact, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True, 
        related_name='customer_invoices',
        help_text='Customer from contacts system'
    )

    # Dates
    invoice_date = models.DateField(help_text="Date of invoice issuance")
    due_date = models.DateField(help_text="Payment due date")

    # Document Links - for different invoice creation scenarios
    sales_order = models.ForeignKey(
        SalesOrder,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='customer_invoices',
        help_text="Linked Sales Order for service invoices"
    )
    delivery_note = models.ForeignKey(
        DeliveryNote,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='customer_invoices',
        help_text="Linked Delivery Note for goods invoices"
    )

    # Financial Information
    subtotal = models.DecimalField(max_digits=15, decimal_places=2, default=0.00, help_text="Before tax and discounts")
    discount_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0.00, help_text="Total discount amount")
    shipping_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0.00, help_text="Shipping charges")
    tax_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0.00, help_text="Calculated tax amount")
    total_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0.00, help_text="Final receivable amount")
    amount_paid = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    balance_due = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)

    # Settings
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='draft')
    payment_terms = models.ForeignKey(
        PaymentTerm,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        help_text="Payment terms for this invoice"
    )

    # Additional Information
    reference_number = models.CharField(max_length=100, blank=True, null=True, help_text="Customer PO number or reference")
    notes = models.TextField(blank=True, null=True, help_text="Internal notes")
    terms_and_conditions = models.TextField(blank=True, null=True, help_text="Terms and conditions for customer")

    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, related_name='customer_invoices_created')

    class Meta:
        db_table = 'sales_customer_invoices'
        ordering = ['-invoice_date', '-created_at']
        verbose_name = 'Customer Invoice'
        verbose_name_plural = 'Customer Invoices'

    def __str__(self):
        customer_name = self.customer.display_name if self.customer else 'No Customer'
        return f"Invoice {self.invoice_number} - {customer_name}"

    def save(self, *args, **kwargs):
        # Generate invoice number if not provided
        if not self.invoice_number:
            last_invoice = CustomerInvoice.objects.filter(
                invoice_number__startswith='INV-'
            ).order_by('-invoice_number').first()
            
            if last_invoice:
                try:
                    last_num = int(last_invoice.invoice_number.split('-')[-1])
                    new_num = last_num + 1
                except (ValueError, IndexError):
                    new_num = 1
            else:
                new_num = 1
            
            self.invoice_number = f"INV-{new_num:06d}"

        # Calculate balance due
        self.balance_due = self.total_amount - self.amount_paid

        # Update status based on payment
        if self.amount_paid >= self.total_amount and self.total_amount > 0:
            self.status = 'paid'
        elif self.amount_paid > 0:
            self.status = 'partial'

        super().save(*args, **kwargs)


class CustomerInvoiceItem(models.Model):
    """Customer Invoice line items for products and services"""
    
    invoice = models.ForeignKey(CustomerInvoice, on_delete=models.CASCADE, related_name='line_items')
    product = models.ForeignKey(Product, on_delete=models.SET_NULL, blank=True, null=True)
    
    # Item details (can override product details)
    item_description = models.TextField(help_text="Product/service description")
    quantity = models.DecimalField(max_digits=10, decimal_places=2, default=1.00)
    unit_price = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    discount_percent = models.DecimalField(max_digits=5, decimal_places=2, default=0.00)
    line_total = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    
    # Tax information
    tax_rate = models.DecimalField(max_digits=5, decimal_places=2, default=0.00, help_text="Tax rate percentage")
    tax_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0.00)
    
    # GL Account for revenue recognition
    account_code = models.CharField(max_length=20, blank=True, null=True, help_text="GL account code for revenue")
    
    # Line item order
    line_order = models.PositiveIntegerField(default=0)
    
    class Meta:
        db_table = 'sales_customer_invoice_items'
        ordering = ['line_order']
        verbose_name = 'Customer Invoice Item'
        verbose_name_plural = 'Customer Invoice Items'
    
    def save(self, *args, **kwargs):
        # Calculate line total
        subtotal = self.quantity * self.unit_price
        discount_amount = subtotal * (self.discount_percent / 100)
        self.line_total = subtotal - discount_amount
        
        # Calculate tax
        self.tax_amount = self.line_total * (self.tax_rate / 100)
        
        super().save(*args, **kwargs)
