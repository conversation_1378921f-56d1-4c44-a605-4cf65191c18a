#!/usr/bin/env python3
"""
Test vendor bill retrieval
"""
import requests

BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api"
TOKEN = "e15bc01f831c5111f413f534ef82288744cb7d41"

HEADERS = {
    'Content-Type': 'application/json',
    'Authorization': f'Token {TOKEN}'
}

# Test retrieval
try:
    # Get the latest bill
    bills_response = requests.get(f"{API_BASE}/purchase/vendor-bills/", headers=HEADERS, timeout=5)
    bills = bills_response.json()['results']
    
    if bills:
        bill_id = bills[0]['id']
        print(f"Testing retrieval of bill ID: {bill_id}")
        
        # Retrieve specific bill
        retrieve_response = requests.get(f"{API_BASE}/purchase/vendor-bills/{bill_id}/", headers=HEADERS, timeout=5)
        
        print(f"Retrieval status: {retrieve_response.status_code}")
        if retrieve_response.status_code == 200:
            bill = retrieve_response.json()
            print(f"✅ SUCCESS - Bill retrieved: {bill['bill_number']}")
            print(f"Total: ${bill['total_amount']}")
            print(f"Line items: {len(bill['line_items'])}")
        else:
            print(f"❌ ERROR: {retrieve_response.text}")
    else:
        print("No bills found")
        
except Exception as e:
    print(f"❌ Exception: {e}")
