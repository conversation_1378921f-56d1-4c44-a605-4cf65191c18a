#!/usr/bin/env python3
import requests
import json

# Configuration
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api"
TOKEN = "e15bc01f831c5111f413f534ef82288744cb7d41"

HEADERS = {
    'Content-Type': 'application/json',
    'Authorization': f'Token {TOKEN}'
}

def test_vendor_bill_creation():
    print("🚀 TESTING VENDOR BILL CREATION")
    print("=" * 50)
    
    # Get vendor
    vendors_response = requests.get(f"{API_BASE}/contacts/vendors/", headers=HEADERS)
    vendor_id = vendors_response.json()['results'][0]['contact']
    print(f"Using vendor ID: {vendor_id}")
    
    # Test data matching frontend structure
    bill_data = {
        "vendor": vendor_id,
        "bill_date": "2025-07-06",
        "due_date": "2025-08-05",
        "status": "draft",
        "payment_terms": "Net 30",
        "reference_number": "FRONTEND-TEST-001",
        "notes": "Test from frontend structure",
        "line_items": [
            {
                "item_description": "Test Product",
                "quantity": 5,
                "unit_price": 100.00,
                "tax_rate": 10.0,
                "account_code": "5010-COGS",
                "line_order": 1
            }
        ]
    }
    
    # Create vendor bill
    response = requests.post(f"{API_BASE}/purchase/vendor-bills/", json=bill_data, headers=HEADERS)
    
    if response.status_code == 201:
        print("✅ SUCCESS! Vendor bill created successfully!")
        data = response.json()
        print(f"Bill ID: {data['id']}")
        print(f"Bill Number: {data['bill_number']}")
        print(f"Total Amount: {data['total_amount']}")
        print(f"Line Items: {len(data['line_items'])}")
        return True
    else:
        print(f"❌ FAILED! Status: {response.status_code}")
        print(f"Error: {response.text}")
        return False

if __name__ == "__main__":
    success = test_vendor_bill_creation()
    if success:
        print("\n🎉 VENDOR BILL BACKEND IS WORKING!")
        print("✅ Frontend can now create vendor bills successfully!")
    else:
        print("\n❌ Still having issues...")
