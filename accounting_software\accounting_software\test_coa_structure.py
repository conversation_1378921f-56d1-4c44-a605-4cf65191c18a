#!/usr/bin/env python3
"""
Test Chart of Accounts API structure to understand the data format
"""
import requests
import json

# Configuration
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api"
TOKEN = "e15bc01f831c5111f413f534ef82288744cb7d41"

HEADERS = {
    'Content-Type': 'application/json',
    'Authorization': f'Token {TOKEN}'
}

def test_coa_structure():
    print("📊 TESTING CHART OF ACCOUNTS API STRUCTURE")
    print("=" * 55)
    
    try:
        # Test the fast COA endpoint
        print("1. Testing Chart of Accounts Fast API...")
        coa_response = requests.get(f"{API_BASE}/gl/chart-of-accounts-fast/", headers=HEADERS, timeout=10)
        
        if coa_response.status_code == 200:
            coa_data = coa_response.json()
            print("✅ Chart of Accounts loaded successfully!")
            
            # Analyze the structure
            print(f"\n📋 COA Data Structure:")
            print(f"  Type: {type(coa_data)}")
            print(f"  Keys: {list(coa_data.keys()) if isinstance(coa_data, dict) else 'Not a dict'}")
            
            if isinstance(coa_data, dict):
                # Check for common keys
                if 'accounts' in coa_data:
                    accounts = coa_data['accounts']
                    print(f"  Accounts Array Length: {len(accounts)}")
                    
                    if accounts:
                        print(f"\n📝 Sample Account Structure:")
                        sample_account = accounts[0]
                        print(f"  Sample Account Keys: {list(sample_account.keys())}")
                        print(f"  Sample Account: {json.dumps(sample_account, indent=2)}")
                        
                        # Look for liability accounts
                        liability_accounts = [
                            acc for acc in accounts 
                            if 'payable' in acc.get('account_name', '').lower() or
                               'liability' in acc.get('account_type_name', '').lower()
                        ]
                        
                        print(f"\n💰 Liability/Payable Accounts Found: {len(liability_accounts)}")
                        for acc in liability_accounts[:3]:  # Show first 3
                            print(f"  - {acc.get('account_number', 'N/A')} - {acc.get('account_name', 'N/A')} ({acc.get('account_type_name', 'N/A')})")
                
                elif 'results' in coa_data:
                    accounts = coa_data['results']
                    print(f"  Results Array Length: {len(accounts)}")
                    
                    if accounts:
                        print(f"\n📝 Sample Account Structure:")
                        sample_account = accounts[0]
                        print(f"  Sample Account: {json.dumps(sample_account, indent=2)}")
                
                # Check metadata
                if 'metadata' in coa_data:
                    metadata = coa_data['metadata']
                    print(f"\n📊 Metadata:")
                    print(f"  Total Accounts: {metadata.get('total_accounts', 'N/A')}")
                    print(f"  Active Accounts: {metadata.get('active_accounts', 'N/A')}")
                    print(f"  Load Time: {metadata.get('load_time_ms', 'N/A')}ms")
            
            return True
        else:
            print(f"❌ Failed to load COA: {coa_response.status_code}")
            print(coa_response.text)
            return False
            
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_coa_structure()
    
    print("\n" + "=" * 55)
    print("📋 COA STRUCTURE TEST SUMMARY")
    print("=" * 55)
    
    if success:
        print("🎉 COA STRUCTURE ANALYSIS COMPLETE!")
        print("✅ API endpoint working correctly")
        print("✅ Data structure identified")
        print("✅ Account format understood")
        print("\n💡 This helps fix the liability account detection!")
    else:
        print("❌ COA structure analysis failed")
        print("🔧 Check the error details above")
