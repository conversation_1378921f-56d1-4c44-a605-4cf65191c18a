from django.shortcuts import render
from rest_framework import viewsets, filters, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, IsAuthenticatedOrReadOnly
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Q, Sum, Count, F, Value, DecimalField
from django.db import models
from django.utils import timezone
from datetime import datetime, timedelta
from django.db.models.functions import Coalesce

from .models import (
    ProductCategory, Product, CustomerInvoice, CustomerInvoiceItem, Payment,
    Estimate, EstimateLineItem, PaymentTerm, SalesOrder, SalesOrderLineItem,
    DeliveryNote, DeliveryNoteItem
)
from contacts.models import Contact
from .serializers import (
    # CustomerSerializer moved to contacts app
    ProductCategorySerializer, ProductSerializer,
    PaymentSerializer,
    EstimateSerializer, EstimateCreateUpdateSerializer, PaymentTermSerializer,
    SalesOrderSerializer, SalesOrderCreateUpdateSerializer, SalesOrderLineItemSerializer,
    DeliveryNoteSerializer, DeliveryNoteCreateUpdateSerializer, DeliveryNoteItemSerializer,
    ProductPricingSerializer, CustomerInvoiceSerializer
)
from gl.models import Account
from inventory.models import Inventory


# DEPRECATED: CustomerViewSet moved to contacts app
# Use /api/contacts/customers/ instead of /api/sales/customers/
# This class is commented out to avoid conflicts with the proper implementation in contacts.views

"""
DEPRECATED CustomerViewSet - moved to contacts app

class CustomerViewSet(viewsets.ModelViewSet):
    ViewSet for managing customers - MOVED TO CONTACTS APP
    queryset = Contact.objects.all()
    serializer_class = CustomerSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['status', 'customer_type', 'taxable']
    search_fields = ['display_name', 'company_name', 'email', 'phone', 'gstin']
    ordering_fields = ['display_name', 'created_at', 'current_balance']
    ordering = ['display_name']

    def get_queryset(self):
        queryset = super().get_queryset()

        # Filter by balance
        min_balance = self.request.query_params.get('min_balance')
        max_balance = self.request.query_params.get('max_balance')

        if min_balance is not None:
            queryset = queryset.filter(current_balance__gte=min_balance)
        if max_balance is not None:
            queryset = queryset.filter(current_balance__lte=max_balance)

        return queryset

    @action(detail=False, methods=['get'])
    def stats(self, request):
        Get customer statistics
        total_customers = self.get_queryset().count()
        active_customers = self.get_queryset().filter(status='active').count()
        inactive_customers = self.get_queryset().filter(status='inactive').count()

        # Calculate total receivables
        total_receivables = self.get_queryset().aggregate(
            total=Sum('current_balance')
        )['total'] or 0

        # Get recent customers (last 30 days)
        thirty_days_ago = timezone.now() - timedelta(days=30)
        new_customers = self.get_queryset().filter(
            created_at__gte=thirty_days_ago
        ).count()

        return Response({
            'total_customers': total_customers,
            'active_customers': active_customers,
            'inactive_customers': inactive_customers,
            'total_receivables': float(total_receivables),
            'new_customers_30_days': new_customers,
        })

    @action(detail=True, methods=['get'])
    def invoices(self, request, pk=None):
        Get customer's invoices
        customer = self.get_object()
        invoices = customer.invoices.all().order_by('-created_at')
        serializer = InvoiceSerializer(invoices, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def payments(self, request, pk=None):
        Get customer's payments
        customer = self.get_object()
        payments = customer.payments.all().order_by('-payment_date')
        serializer = PaymentSerializer(payments, many=True)
        return Response(serializer.data)
"""


class ProductCategoryViewSet(viewsets.ModelViewSet):
    """Enhanced ViewSet for managing product categories"""
    queryset = ProductCategory.objects.all()
    serializer_class = ProductCategorySerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['division_type', 'is_active', 'parent_category', 'level']
    search_fields = ['name', 'code', 'description']
    ordering_fields = ['name', 'code', 'sort_order', 'created_at']
    ordering = ['sort_order', 'name']

    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Filter by division type
        division_type = self.request.query_params.get('division_type')
        if division_type:
            queryset = queryset.filter(division_type=division_type)
        
        # Filter by hierarchy level
        level = self.request.query_params.get('level')
        if level:
            queryset = queryset.filter(level=level)
        
        # Filter top-level categories only
        top_level_only = self.request.query_params.get('top_level_only')
        if top_level_only == 'true':
            queryset = queryset.filter(parent_category__isnull=True)
        
        return queryset

    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Get category statistics"""
        total_categories = self.get_queryset().count()
        active_categories = self.get_queryset().filter(is_active=True).count()
        
        # Count by division type
        division_stats = {}
        for division_type, _ in ProductCategory.DIVISION_TYPE_CHOICES:
            count = self.get_queryset().filter(division_type=division_type, is_active=True).count()
            division_stats[division_type] = count
        
        # Count by level
        level_stats = {}
        for level in [1, 2, 3]:
            count = self.get_queryset().filter(level=level, is_active=True).count()
            level_stats[f'level_{level}'] = count
        
        return Response({
            'total_categories': total_categories,
            'active_categories': active_categories,
            'division_stats': division_stats,
            'level_stats': level_stats,
        })

    @action(detail=False, methods=['get'])
    def hierarchy(self, request):
        """Get categories in hierarchical structure"""
        # Get top-level categories
        top_level = self.get_queryset().filter(parent_category__isnull=True, is_active=True)
        serializer = self.get_serializer(top_level, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def subcategories(self, request, pk=None):
        """Get subcategories of a specific category"""
        category = self.get_object()
        subcategories = category.subcategories.filter(is_active=True).order_by('sort_order', 'name')
        serializer = self.get_serializer(subcategories, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def products(self, request, pk=None):
        """Get products in this category"""
        category = self.get_object()
        products = category.products.filter(status='active').order_by('name')
        # Import ProductSerializer here to avoid circular imports
        from .serializers import ProductSerializer
        serializer = ProductSerializer(products, many=True, context={'request': request})
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def by_division(self, request):
        """Get categories grouped by division type"""
        division_type = request.query_params.get('division_type')
        if not division_type:
            return Response({'error': 'division_type parameter is required'}, status=400)
        
        categories = self.get_queryset().filter(
            division_type=division_type, 
            is_active=True
        ).order_by('sort_order', 'name')
        
        serializer = self.get_serializer(categories, many=True)
        return Response(serializer.data)


class ProductViewSet(viewsets.ModelViewSet):
    """ViewSet for managing products and services"""
    queryset = Product.objects.all()
    serializer_class = ProductSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['status', 'product_type', 'category', 'taxable', 'track_inventory']
    search_fields = ['name', 'sku', 'description']
    ordering_fields = ['name', 'unit_price', 'created_at']
    ordering = ['name']

    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Annotate total quantity from all warehouses
        queryset = queryset.annotate(
            total_quantity_on_hand=Coalesce(
                Sum('inventory_levels__quantity_on_hand'),
                Value(0, output_field=DecimalField())
            )
        )
        
        # Filter by price range
        min_price = self.request.query_params.get('min_price')
        max_price = self.request.query_params.get('max_price')
        
        if min_price is not None:
            queryset = queryset.filter(unit_price__gte=min_price)
        if max_price is not None:
            queryset = queryset.filter(unit_price__lte=max_price)
        
        # Filter by low stock
        low_stock = self.request.query_params.get('low_stock')
        if low_stock == 'true':
            queryset = queryset.filter(
                track_inventory=True,
                total_quantity_on_hand__lte=F('reorder_point')
            )
        
        return queryset

    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Get product statistics"""
        total_products = self.get_queryset().filter(product_type='product').count()
        total_services = self.get_queryset().filter(product_type='service').count()
        
        # Calculate inventory value using annotated total_quantity_on_hand
        inventory_value = self.get_queryset().filter(
            product_type='product',
            track_inventory=True
        ).aggregate(
            total=Sum(F('total_quantity_on_hand') * F('cost_price'))
        )['total'] or 0
        
        # Low stock items using annotated total_quantity_on_hand
        low_stock_count = self.get_queryset().filter(
            track_inventory=True,
            total_quantity_on_hand__lte=F('reorder_point')
        ).count()
        
        return Response({
            'total_products': total_products,
            'total_services': total_services,
            'inventory_value': float(inventory_value),
            'low_stock_count': low_stock_count,
        })

    @action(detail=False, methods=['get'])
    def low_stock(self, request):
        """Get products with low stock"""
        low_stock_products = self.get_queryset().filter(
            track_inventory=True,
            total_quantity_on_hand__lte=F('reorder_point')
        )
        serializer = self.get_serializer(low_stock_products, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def gl_accounts(self, request):
        """Get available GL accounts for product setup"""
        account_type = request.query_params.get('type', 'all')
        
        if account_type == 'revenue':
            accounts = Account.objects.filter(
                account_type__type='REVENUE',
                is_active=True,
                is_header_account=False
            ).order_by('account_number')
        elif account_type == 'expense':
            accounts = Account.objects.filter(
                account_type__type='EXPENSE',
                is_active=True,
                is_header_account=False
            ).order_by('account_number')
        elif account_type == 'asset':
            accounts = Account.objects.filter(
                account_type__type='ASSET',
                is_active=True,
                is_header_account=False
            ).order_by('account_number')
        else:
            # Return all accounts suitable for products
            accounts = Account.objects.filter(
                account_type__type__in=['REVENUE', 'EXPENSE', 'ASSET'],
                is_active=True,
                is_header_account=False
            ).order_by('account_type__type', 'account_number')
        
        # Simple serialization for dropdown
        account_data = [
            {
                'id': acc.id,
                'account_number': acc.account_number,
                'account_name': acc.account_name,
                'account_type': acc.account_type.type,
                'display_name': f"{acc.account_number} - {acc.account_name}"
            }
            for acc in accounts
        ]
        
        return Response(account_data)

    @action(detail=False, methods=['get'])
    def dropdown(self, request):
        """Get all active products for dropdown (optimized for search)"""
        queryset = self.get_queryset().filter(status='active').order_by('name')
        
        # Apply search if provided
        search = request.query_params.get('search', '')
        if search:
            queryset = queryset.filter(
                models.Q(name__icontains=search) |
                models.Q(sku__icontains=search) |
                models.Q(description__icontains=search)
            )
        
        # Limit results for performance
        limit = int(request.query_params.get('limit', 50))
        queryset = queryset[:limit]
        
        # Simple serialization for dropdown
        product_data = [
            {
                'id': product.id,
                'name': product.name,
                'sku': product.sku,
                'unit_price': str(product.unit_price),
                'cost_price': str(product.cost_price),
                'display_name': f"{product.name} ({product.sku})" if product.sku else product.name,
                'track_inventory': product.track_inventory,
                'quantity_on_hand': product.quantity_on_hand,
            }
            for product in queryset
        ]
        
        return Response(product_data)


# OLD INVOICE VIEWSET - COMMENTED OUT (Replaced by CustomerInvoiceViewSet)
# class InvoiceViewSet(viewsets.ModelViewSet):
#     """ViewSet for managing invoices"""
#     queryset = Invoice.objects.all()
#     permission_classes = [IsAuthenticated]
#     filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
#     filterset_fields = ['status', 'customer']
#     search_fields = ['invoice_number', 'customer__display_name', 'po_number']
#     ordering_fields = ['invoice_date', 'due_date', 'total_amount', 'created_at']
#     ordering = ['-invoice_date', '-created_at']
#
#     def get_serializer_class(self):
#         if self.action in ['create', 'update', 'partial_update']:
#             return InvoiceCreateUpdateSerializer
#         return InvoiceSerializer
#
#     def get_queryset(self):
#         queryset = super().get_queryset()
#
#         # Filter by date range
#         start_date = self.request.query_params.get('start_date')
#         end_date = self.request.query_params.get('end_date')
#
#         if start_date:
#             queryset = queryset.filter(invoice_date__gte=start_date)
#         if end_date:
#             queryset = queryset.filter(invoice_date__lte=end_date)
#
#         # Filter by amount range
#         min_amount = self.request.query_params.get('min_amount')
#         max_amount = self.request.query_params.get('max_amount')
#
#         if min_amount is not None:
#             queryset = queryset.filter(total_amount__gte=min_amount)
#         if max_amount is not None:
#             queryset = queryset.filter(total_amount__lte=max_amount)
#
#         # Filter overdue invoices
#         overdue = self.request.query_params.get('overdue')
#         if overdue == 'true':
#             queryset = queryset.filter(
#                 due_date__lt=timezone.now().date(),
#                 status__in=['sent', 'viewed', 'partial']
#             )
#
#         return queryset.select_related('customer').prefetch_related('line_items')
#
#     @action(detail=False, methods=['get'])
#     def stats(self, request):
#         """Get invoice statistics"""
#         queryset = self.get_queryset()
#
#         total_invoices = queryset.count()
#         total_revenue = queryset.aggregate(Sum('total_amount'))['total_amount__sum'] or 0
#         total_paid = queryset.aggregate(Sum('amount_paid'))['amount_paid__sum'] or 0
#         outstanding_amount = queryset.aggregate(Sum('balance_due'))['balance_due__sum'] or 0
#
#         # Status breakdown
#         status_counts = dict(queryset.values_list('status').annotate(Count('status')))
#
#         # Overdue invoices
#         overdue_count = queryset.filter(
#             due_date__lt=timezone.now().date(),
#             status__in=['sent', 'viewed', 'partial']
#         ).count()
#
#         # Count by status for the frontend
#         draft_count = queryset.filter(status='draft').count()
#         paid_count = queryset.filter(status='paid').count()
#
#         return Response({
#             'total_invoices': total_invoices,
#             'total_revenue': float(total_revenue),
#             'outstanding_amount': float(outstanding_amount),
#             'overdue_count': overdue_count,
#             'draft_count': draft_count,
#             'paid_count': paid_count,
#             'status_counts': status_counts,
#         })
#
#     @action(detail=True, methods=['post'])
#     def mark_sent(self, request, pk=None):
#         """Mark invoice as sent"""
#         invoice = self.get_object()
#         invoice.status = 'sent'
#         invoice.email_sent = True
#         invoice.email_sent_date = timezone.now()
#         invoice.save()
#         return Response({'status': 'Invoice marked as sent'})
#
#     @action(detail=True, methods=['post'])
#     def mark_paid(self, request, pk=None):
#         """Mark invoice as paid"""
#         invoice = self.get_object()
#         invoice.status = 'paid'
#         invoice.amount_paid = invoice.total_amount
#         invoice.balance_due = 0
#         invoice.save()
#         return Response({'status': 'Invoice marked as paid'})
#
#     @action(detail=True, methods=['post'])
#     def void(self, request, pk=None):
#         """Void an invoice"""
#         invoice = self.get_object()
#         invoice.status = 'void'
#         invoice.save()
#         return Response({'status': 'Invoice voided'})


class PaymentViewSet(viewsets.ModelViewSet):
    """ViewSet for managing payments"""
    queryset = Payment.objects.all()
    serializer_class = PaymentSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['payment_method', 'customer', 'invoice']
    search_fields = ['reference_number', 'customer__display_name']
    ordering_fields = ['payment_date', 'amount', 'created_at']
    ordering = ['-payment_date']

    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Filter by date range
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')
        
        if start_date:
            queryset = queryset.filter(payment_date__gte=start_date)
        if end_date:
            queryset = queryset.filter(payment_date__lte=end_date)
        
        return queryset.select_related('customer', 'invoice')


class EstimateViewSet(viewsets.ModelViewSet):
    """ViewSet for managing estimates"""
    queryset = Estimate.objects.all()
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['status', 'customer']
    search_fields = ['estimate_number', 'customer__display_name', 'po_number']
    ordering_fields = ['estimate_date', 'expiration_date', 'total_amount', 'created_at']
    ordering = ['-estimate_date', '-created_at']

    def get_serializer_class(self):
        if self.action in ['create', 'update', 'partial_update']:
            return EstimateCreateUpdateSerializer
        return EstimateSerializer

    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Filter by date range
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')
        
        if start_date:
            queryset = queryset.filter(estimate_date__gte=start_date)
        if end_date:
            queryset = queryset.filter(estimate_date__lte=end_date)
        
        return queryset.select_related('customer').prefetch_related('line_items')

    # OLD CONVERT TO INVOICE ACTION - COMMENTED OUT (Will be replaced with CustomerInvoice)
    # @action(detail=True, methods=['post'])
    # def convert_to_invoice(self, request, pk=None):
    #     """Convert estimate to invoice"""
    #     estimate = self.get_object()
    #
    #     if estimate.status == 'invoiced':
    #         return Response(
    #             {'error': 'Estimate already converted to invoice'},
    #             status=status.HTTP_400_BAD_REQUEST
    #         )
    #
    #     # Create invoice from estimate
    #     invoice_data = {
    #         'customer': estimate.customer,
    #         'invoice_date': timezone.now().date(),
    #         'due_date': timezone.now().date() + timedelta(days=30),  # Default 30 days
    #         'subtotal': estimate.subtotal,
    #         'discount_percent': estimate.discount_percent,
    #         'discount_amount': estimate.discount_amount,
    #         'tax_amount': estimate.tax_amount,
    #         'total_amount': estimate.total_amount,
    #         'po_number': estimate.po_number,
    #         'memo': estimate.memo,
    #         'message_to_customer': estimate.message_to_customer,
    #         'created_by': request.user,
    #     }
    #
    #     invoice = Invoice.objects.create(**invoice_data)
    #
    #     # Copy line items
    #     for estimate_line in estimate.line_items.all():
    #         InvoiceLineItem.objects.create(
    #             invoice=invoice,
    #             product=estimate_line.product,
    #             description=estimate_line.description,
    #             quantity=estimate_line.quantity,
    #             unit_price=estimate_line.unit_price,
    #             discount_percent=estimate_line.discount_percent,
    #             line_total=estimate_line.line_total,
    #             taxable=estimate_line.taxable,
    #             tax_rate=estimate_line.tax_rate,
    #             tax_amount=estimate_line.tax_amount,
    #             line_order=estimate_line.line_order,
    #         )
    #
    #     # Update estimate status
    #     estimate.status = 'invoiced'
    #     estimate.converted_to_invoice = invoice
    #     estimate.save()
    #
    #     return Response({
    #         'status': 'Estimate converted to invoice',
    #         'invoice_id': invoice.id,
    #         'invoice_number': invoice.invoice_number
    #     })

    @action(detail=True, methods=['post'])
    def mark_accepted(self, request, pk=None):
        """Mark estimate as accepted"""
        estimate = self.get_object()
        estimate.status = 'accepted'
        estimate.save()
        return Response({'status': 'Estimate marked as accepted'})

    @action(detail=True, methods=['post'])
    def mark_rejected(self, request, pk=None):
        """Mark estimate as rejected"""
        estimate = self.get_object()
        estimate.status = 'rejected'
        estimate.save()
        return Response({'status': 'Estimate marked as rejected'})


class PaymentTermViewSet(viewsets.ModelViewSet):
    """ViewSet for managing payment terms"""
    queryset = PaymentTerm.objects.filter(is_active=True)
    serializer_class = PaymentTermSerializer
    permission_classes = [IsAuthenticatedOrReadOnly]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name', 'code', 'description']
    ordering_fields = ['name', 'days', 'created_at']
    ordering = ['days', 'name']

    @action(detail=False, methods=['get'])
    def default(self, request):
        """Get the default payment term"""
        try:
            default_term = PaymentTerm.objects.get(is_default=True, is_active=True)
            serializer = self.get_serializer(default_term)
            return Response(serializer.data)
        except PaymentTerm.DoesNotExist:
            return Response({'error': 'No default payment term found'}, status=status.HTTP_404_NOT_FOUND)

    @action(detail=True, methods=['post'])
    def set_default(self, request, pk=None):
        """Set this payment term as default"""
        payment_term = self.get_object()
        
        # Remove default from all other terms
        PaymentTerm.objects.filter(is_default=True).update(is_default=False)
        
        # Set this term as default
        payment_term.is_default = True
        payment_term.save()
        
        serializer = self.get_serializer(payment_term)
        return Response(serializer.data)


class SalesOrderViewSet(viewsets.ModelViewSet):
    """ViewSet for managing sales orders"""
    queryset = SalesOrder.objects.all()
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['status', 'customer']
    search_fields = ['so_number', 'customer__display_name', 'customer_po_number']
    ordering_fields = ['so_date', 'expected_delivery_date', 'total_amount', 'created_at']
    ordering = ['-so_date', '-created_at']

    def get_serializer_class(self):
        if self.action in ['create', 'update', 'partial_update']:
            return SalesOrderCreateUpdateSerializer
        return SalesOrderSerializer

    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Filter by date range
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')
        
        if start_date:
            queryset = queryset.filter(so_date__gte=start_date)
        if end_date:
            queryset = queryset.filter(so_date__lte=end_date)
        
        return queryset.select_related('customer').prefetch_related('line_items')

    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Get sales order statistics"""
        queryset = self.get_queryset()
        
        total_orders = queryset.count()
        total_value = queryset.aggregate(total=Sum('total_amount'))['total'] or 0
        
        # Count by status
        status_counts = {}
        for status_choice, _ in SalesOrder.STATUS_CHOICES:
            count = queryset.filter(status=status_choice).count()
            status_counts[status_choice] = count
        
        # Pending delivery value
        pending_delivery_value = queryset.filter(
            status__in=['confirmed', 'partial']
        ).aggregate(total=Sum('total_amount'))['total'] or 0
        
        # Recent orders (last 30 days)
        thirty_days_ago = timezone.now() - timedelta(days=30)
        recent_orders = queryset.filter(so_date__gte=thirty_days_ago).count()
        
        return Response({
            'total_orders': total_orders,
            'total_value': float(total_value),
            'status_counts': status_counts,
            'pending_delivery_value': float(pending_delivery_value),
            'recent_orders_30_days': recent_orders,
        })

    @action(detail=False, methods=['get'])
    def pending_delivery(self, request):
        """Get sales orders pending delivery"""
        pending_orders = self.get_queryset().filter(
            status__in=['confirmed', 'partial']
        ).order_by('expected_delivery_date')
        
        serializer = self.get_serializer(pending_orders, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def confirm(self, request, pk=None):
        """Confirm a sales order"""
        sales_order = self.get_object()
        
        if sales_order.status != 'pending':
            return Response(
                {'error': 'Only pending orders can be confirmed'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        sales_order.status = 'confirmed'
        sales_order.save()
        
        return Response({'status': 'Sales order confirmed'})

    @action(detail=True, methods=['post'])
    def cancel(self, request, pk=None):
        """Cancel a sales order"""
        sales_order = self.get_object()
        
        if sales_order.status in ['delivered', 'invoiced', 'closed']:
            return Response(
                {'error': 'Cannot cancel delivered or invoiced orders'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        sales_order.status = 'cancelled'
        sales_order.save()
        
        return Response({'status': 'Sales order cancelled'})

    @action(detail=True, methods=['get'])
    def delivery_notes(self, request, pk=None):
        """Get delivery notes for this sales order"""
        sales_order = self.get_object()
        delivery_notes = sales_order.delivery_notes.all().order_by('-delivery_date')
        serializer = DeliveryNoteSerializer(delivery_notes, many=True, context={'request': request})
        return Response(serializer.data)


class DeliveryNoteViewSet(viewsets.ModelViewSet):
    """ViewSet for managing delivery notes"""
    queryset = DeliveryNote.objects.all()
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['status', 'customer', 'sales_order', 'warehouse', 'posted']
    search_fields = ['dn_number', 'customer__display_name', 'sales_order__so_number']
    ordering_fields = ['delivery_date', 'created_at']
    ordering = ['-delivery_date', '-created_at']

    def get_serializer_class(self):
        if self.action in ['create', 'update', 'partial_update']:
            return DeliveryNoteCreateUpdateSerializer
        return DeliveryNoteSerializer

    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Filter by date range
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')
        
        if start_date:
            queryset = queryset.filter(delivery_date__gte=start_date)
        if end_date:
            queryset = queryset.filter(delivery_date__lte=end_date)
        
        return queryset.select_related('customer', 'sales_order', 'warehouse').prefetch_related('line_items')

    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Get delivery note statistics"""
        queryset = self.get_queryset()
        
        total_deliveries = queryset.count()
        total_value = queryset.aggregate(total=Sum('total_amount'))['total'] or 0
        
        # Count by status
        status_counts = {}
        for status_choice, _ in DeliveryNote.STATUS_CHOICES:
            count = queryset.filter(status=status_choice).count()
            status_counts[status_choice] = count
        
        # Posted vs unposted
        posted_count = queryset.filter(posted=True).count()
        unposted_count = queryset.filter(posted=False).count()
        
        return Response({
            'total_deliveries': total_deliveries,
            'total_value': float(total_value),
            'status_counts': status_counts,
            'posted_count': posted_count,
            'unposted_count': unposted_count,
        })

    @action(detail=False, methods=['get'])
    def pending_post(self, request):
        """Get delivery notes pending posting to inventory"""
        pending_notes = self.get_queryset().filter(
            status='delivered',
            posted=False
        ).order_by('delivery_date')
        
        serializer = self.get_serializer(pending_notes, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def post_delivery(self, request, pk=None):
        """Post delivery to inventory"""
        delivery_note = self.get_object()
        
        if delivery_note.posted:
            return Response(
                {'error': 'Delivery note already posted'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        if delivery_note.status != 'delivered':
            return Response(
                {'error': 'Only delivered notes can be posted'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            delivery_note.post_delivery(request.user)
            return Response({'status': 'Delivery posted to inventory'})
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=True, methods=['post'])
    def mark_delivered(self, request, pk=None):
        """Mark delivery note as delivered"""
        delivery_note = self.get_object()
        
        if delivery_note.status == 'delivered':
            return Response(
                {'error': 'Delivery note already marked as delivered'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        delivery_note.status = 'delivered'
        delivery_note.save()
        
        return Response({'status': 'Delivery note marked as delivered'})


# Enhanced Product ViewSet with Sales Price Authority
class ProductPricingViewSet(viewsets.ModelViewSet):
    """Special ViewSet for Sales Department to manage product pricing"""
    queryset = Product.objects.filter(status='active')
    serializer_class = ProductPricingSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['category', 'product_type']
    search_fields = ['name', 'sku']
    ordering_fields = ['name', 'unit_price', 'margin_percentage', 'price_last_updated_at']
    ordering = ['name']
    
    http_method_names = ['get', 'patch', 'head', 'options']  # Only allow price updates, not creation/deletion

    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Filter by margin range
        min_margin = self.request.query_params.get('min_margin')
        max_margin = self.request.query_params.get('max_margin')
        
        if min_margin is not None:
            # Calculate margin percentage dynamically
            queryset = queryset.annotate(
                calculated_margin=models.Case(
                    models.When(cost_price__gt=0, then=(F('unit_price') - F('cost_price')) / F('cost_price') * 100),
                    default=0,
                    output_field=models.DecimalField()
                )
            ).filter(calculated_margin__gte=min_margin)
        
        if max_margin is not None:
            queryset = queryset.annotate(
                calculated_margin=models.Case(
                    models.When(cost_price__gt=0, then=(F('unit_price') - F('cost_price')) / F('cost_price') * 100),
                    default=0,
                    output_field=models.DecimalField()
                )
            ).filter(calculated_margin__lte=max_margin)
        
        return queryset

    @action(detail=False, methods=['get'])
    def pricing_stats(self, request):
        """Get pricing statistics for Sales Department"""
        queryset = self.get_queryset()
        
        total_products = queryset.count()
        
        # Products with no cost price (can't calculate margin)
        no_cost_price = queryset.filter(cost_price__isnull=True).count()
        
        # Products with negative margin
        negative_margin = queryset.filter(
            cost_price__gt=0,
            unit_price__lt=F('cost_price')
        ).count()
        
        # Average margin
        avg_margin = queryset.filter(cost_price__gt=0).aggregate(
            avg_margin=models.Avg(
                (F('unit_price') - F('cost_price')) / F('cost_price') * 100
            )
        )['avg_margin'] or 0
        
        # Recent price updates (last 30 days)
        thirty_days_ago = timezone.now() - timedelta(days=30)
        recent_updates = queryset.filter(
            price_last_updated_at__gte=thirty_days_ago
        ).count()
        
        return Response({
            'total_products': total_products,
            'no_cost_price_count': no_cost_price,
            'negative_margin_count': negative_margin,
            'average_margin_percentage': float(avg_margin),
            'recent_price_updates_30_days': recent_updates,
        })

    @action(detail=False, methods=['get'])
    def margin_analysis(self, request):
        """Get detailed margin analysis"""
        queryset = self.get_queryset().filter(cost_price__gt=0)
        
        # Group by margin ranges
        margin_ranges = {
            'negative': queryset.filter(unit_price__lt=F('cost_price')).count(),
            '0_to_10': queryset.filter(
                unit_price__gte=F('cost_price'),
                unit_price__lt=F('cost_price') * 1.1
            ).count(),
            '10_to_25': queryset.filter(
                unit_price__gte=F('cost_price') * 1.1,
                unit_price__lt=F('cost_price') * 1.25
            ).count(),
            '25_to_50': queryset.filter(
                unit_price__gte=F('cost_price') * 1.25,
                unit_price__lt=F('cost_price') * 1.5
            ).count(),
            'above_50': queryset.filter(unit_price__gte=F('cost_price') * 1.5).count(),
        }
        
        return Response(margin_ranges)


# Customer Invoice Views (New Enhanced Models)

class CustomerInvoiceViewSet(viewsets.ModelViewSet):
    """ViewSet for managing customer invoices - comprehensive ERP implementation"""
    queryset = CustomerInvoice.objects.all()
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['status', 'customer', 'invoice_date', 'due_date']
    search_fields = ['invoice_number', 'customer__display_name', 'reference_number', 'notes']
    ordering_fields = ['invoice_date', 'due_date', 'total_amount', 'created_at']
    ordering = ['-invoice_date', '-created_at']

    def get_serializer_class(self):
        return CustomerInvoiceSerializer

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)

    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Get customer invoice statistics"""
        queryset = self.get_queryset()

        total_invoices = queryset.count()
        draft_invoices = queryset.filter(status='draft').count()
        posted_invoices = queryset.filter(status='posted').count()
        paid_invoices = queryset.filter(status='paid').count()
        overdue_invoices = queryset.filter(
            status__in=['posted', 'sent', 'partial'],
            due_date__lt=timezone.now().date()
        ).count()

        # Calculate totals
        total_receivables = queryset.filter(
            status__in=['posted', 'sent', 'partial']
        ).aggregate(
            total=Sum('balance_due')
        )['total'] or 0

        return Response({
            'total_invoices': total_invoices,
            'draft_invoices': draft_invoices,
            'posted_invoices': posted_invoices,
            'paid_invoices': paid_invoices,
            'overdue_invoices': overdue_invoices,
            'total_receivables': float(total_receivables),
        })

    @action(detail=True, methods=['post'])
    def post_invoice(self, request, pk=None):
        """Post a draft invoice"""
        invoice = self.get_object()

        if invoice.status != 'draft':
            return Response(
                {'error': 'Only draft invoices can be posted'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Update status
        invoice.status = 'posted'
        invoice.save()

        return Response({'message': 'Invoice posted successfully'})

    @action(detail=True, methods=['post'])
    def void_invoice(self, request, pk=None):
        """Void an invoice"""
        invoice = self.get_object()

        if invoice.status in ['paid', 'void']:
            return Response(
                {'error': 'Cannot void paid or already voided invoices'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Update status
        invoice.status = 'void'
        invoice.save()

        return Response({'message': 'Invoice voided successfully'})

    @action(detail=False, methods=['post'])
    def create_from_sales_order(self, request):
        """Create customer invoice from sales order"""
        sales_order_id = request.data.get('sales_order_id')

        if not sales_order_id:
            return Response(
                {'error': 'Sales order ID is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            sales_order = SalesOrder.objects.get(id=sales_order_id)
        except SalesOrder.DoesNotExist:
            return Response(
                {'error': 'Sales order not found'},
                status=status.HTTP_404_NOT_FOUND
            )

        # Check if sales order already has an invoice
        existing_invoices = CustomerInvoice.objects.filter(sales_order=sales_order)
        if existing_invoices.exists():
            return Response(
                {'error': 'This sales order already has an invoice created'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Prepare invoice data
        invoice_data = {
            'customer': sales_order.customer.id if sales_order.customer else None,
            'invoice_date': request.data.get('invoice_date', timezone.now().date().isoformat()),
            'due_date': request.data.get('due_date', (timezone.now().date() + timedelta(days=30)).isoformat()),
            'reference_number': f"SO-{sales_order.so_number}",
            'notes': request.data.get('notes', f"Invoice created from Sales Order {sales_order.so_number}"),
            'line_items': []
        }

        # Copy line items from sales order
        for so_item in sales_order.line_items.all():
            line_item_data = {
                'product': so_item.product.id if so_item.product else None,
                'item_description': so_item.product.name if so_item.product else f"Item from SO {sales_order.so_number}",
                'quantity': so_item.quantity,
                'unit_price': so_item.unit_price,
                'tax_rate': so_item.tax_rate,
                'account_code': getattr(so_item.product, 'income_account_code', '4010-SALES') if so_item.product else '4010-SALES'
            }
            invoice_data['line_items'].append(line_item_data)

        if not invoice_data['line_items']:
            return Response(
                {'error': 'No billable items found in this sales order'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Create the customer invoice using the existing serializer
        serializer = CustomerInvoiceSerializer(data=invoice_data)
        if serializer.is_valid():
            customer_invoice = serializer.save()

            # Link the sales order to the customer invoice
            customer_invoice.sales_order = sales_order
            customer_invoice.save()

            return Response({
                'message': 'Customer invoice created successfully from sales order',
                'invoice_id': customer_invoice.id,
                'invoice_number': customer_invoice.invoice_number,
                'so_number': sales_order.so_number
            }, status=status.HTTP_201_CREATED)
        else:
            return Response(
                {'error': 'Failed to create customer invoice', 'details': serializer.errors},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=False, methods=['post'])
    def create_from_delivery_note(self, request):
        """Create customer invoice from delivery note"""
        delivery_note_id = request.data.get('delivery_note_id')

        if not delivery_note_id:
            return Response(
                {'error': 'Delivery note ID is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            delivery_note = DeliveryNote.objects.get(id=delivery_note_id)
        except DeliveryNote.DoesNotExist:
            return Response(
                {'error': 'Delivery note not found'},
                status=status.HTTP_404_NOT_FOUND
            )

        # Check if delivery note already has an invoice
        existing_invoices = CustomerInvoice.objects.filter(delivery_note=delivery_note)
        if existing_invoices.exists():
            return Response(
                {'error': 'This delivery note already has an invoice created'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Prepare invoice data
        invoice_data = {
            'customer': delivery_note.customer.id if delivery_note.customer else None,
            'invoice_date': request.data.get('invoice_date', timezone.now().date().isoformat()),
            'due_date': request.data.get('due_date', (timezone.now().date() + timedelta(days=30)).isoformat()),
            'reference_number': f"DN-{delivery_note.dn_number}",
            'notes': request.data.get('notes', f"Invoice created from Delivery Note {delivery_note.dn_number}"),
            'line_items': []
        }

        # Copy line items from delivery note
        for dn_item in delivery_note.line_items.all():
            line_item_data = {
                'product': dn_item.product.id if dn_item.product else None,
                'item_description': dn_item.product.name if dn_item.product else f"Item from DN {delivery_note.dn_number}",
                'quantity': dn_item.quantity_delivered,
                'unit_price': dn_item.unit_price,
                'tax_rate': 0,  # Delivery notes typically don't have tax info, will be added in invoice
                'account_code': getattr(dn_item.product, 'income_account_code', '4010-SALES') if dn_item.product else '4010-SALES'
            }
            invoice_data['line_items'].append(line_item_data)

        if not invoice_data['line_items']:
            return Response(
                {'error': 'No billable items found in this delivery note'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Create the customer invoice using the existing serializer
        serializer = CustomerInvoiceSerializer(data=invoice_data)
        if serializer.is_valid():
            customer_invoice = serializer.save()

            # Link the delivery note to the customer invoice
            customer_invoice.delivery_note = delivery_note
            customer_invoice.save()

            return Response({
                'message': 'Customer invoice created successfully from delivery note',
                'invoice_id': customer_invoice.id,
                'invoice_number': customer_invoice.invoice_number,
                'dn_number': delivery_note.dn_number
            }, status=status.HTTP_201_CREATED)
        else:
            return Response(
                {'error': 'Failed to create customer invoice', 'details': serializer.errors},
                status=status.HTTP_400_BAD_REQUEST
            )
