from rest_framework import viewsets, filters, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Q, Sum, Count
from .models import ProductCategory, Product
from .serializers import ProductCategorySerializer, ProductSerializer


class ProductCategoryViewSet(viewsets.ModelViewSet):
    """ViewSet for managing product categories"""
    queryset = ProductCategory.objects.all()
    serializer_class = ProductCategorySerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['division_type', 'default_unit_of_measure', 'is_active', 'parent_category']
    search_fields = ['name', 'description']
    ordering_fields = ['name', 'created_at']
    ordering = ['name']

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)

    @action(detail=False, methods=['get'])
    def tree(self, request):
        """Get category tree structure"""
        root_categories = ProductCategory.objects.filter(parent_category=None, is_active=True)
        
        def build_tree(category):
            children = category.subcategories.filter(is_active=True)
            return {
                'id': category.id,
                'name': category.name,
                'division_type': category.division_type,
                'products_count': category.products.filter(status='active').count(),
                'children': [build_tree(child) for child in children]
            }
        
        tree_data = [build_tree(category) for category in root_categories]
        return Response(tree_data)

    @action(detail=True, methods=['get'])
    def products(self, request, pk=None):
        """Get all products in this category and subcategories"""
        category = self.get_object()
        all_categories = [category] + category.get_all_subcategories()
        products = Product.objects.filter(category__in=all_categories, status='active')
        
        serializer = ProductSerializer(products, many=True, context={'request': request})
        return Response(serializer.data)


class ProductViewSet(viewsets.ModelViewSet):
    """ViewSet for managing products and services"""
    queryset = Product.objects.all()
    serializer_class = ProductSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['product_type', 'category', 'status', 'track_inventory', 'taxable']
    search_fields = ['name', 'sku', 'description']
    ordering_fields = ['name', 'unit_price', 'cost_price', 'created_at']
    ordering = ['name']

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)

    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Get product statistics"""
        queryset = self.get_queryset()
        
        total_products = queryset.count()
        active_products = queryset.filter(status='active').count()
        inactive_products = queryset.filter(status='inactive').count()
        
        products_by_type = queryset.values('product_type').annotate(count=Count('id'))
        
        # Calculate total inventory value
        total_inventory_value = 0
        for product in queryset.filter(track_inventory=True, status='active'):
            total_inventory_value += (product.quantity_on_hand * product.cost_price)
        
        return Response({
            'total_products': total_products,
            'active_products': active_products,
            'inactive_products': inactive_products,
            'products_by_type': list(products_by_type),
            'total_inventory_value': float(total_inventory_value),
        })

    @action(detail=False, methods=['get'])
    def low_stock(self, request):
        """Get products with low stock"""
        low_stock_products = self.get_queryset().filter(
            track_inventory=True,
            status='active',
            quantity_on_hand__lte=models.F('reorder_point')
        )
        
        serializer = self.get_serializer(low_stock_products, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def services(self, request):
        """Get all services"""
        services = self.get_queryset().filter(product_type='service', status='active')
        serializer = self.get_serializer(services, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def products_only(self, request):
        """Get only physical products (not services)"""
        products = self.get_queryset().filter(product_type='product', status='active')
        serializer = self.get_serializer(products, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def update_price(self, request, pk=None):
        """Update product price with tracking"""
        product = self.get_object()
        new_price = request.data.get('unit_price')
        
        if new_price is None:
            return Response(
                {'error': 'unit_price is required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            new_price = float(new_price)
            old_price = float(product.unit_price)
            
            product.unit_price = new_price
            product.price_last_updated_by = request.user
            product.save()
            
            return Response({
                'message': 'Price updated successfully',
                'old_price': old_price,
                'new_price': new_price,
                'updated_by': request.user.username
            })
        except ValueError:
            return Response(
                {'error': 'Invalid price format'},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=True, methods=['post'])
    def update_cost(self, request, pk=None):
        """Update product cost with tracking"""
        product = self.get_object()
        new_cost = request.data.get('cost_price')
        
        if new_cost is None:
            return Response(
                {'error': 'cost_price is required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            new_cost = float(new_cost)
            old_cost = float(product.cost_price)
            
            product.cost_price = new_cost
            product.cost_last_updated_by = request.user
            product.save()
            
            return Response({
                'message': 'Cost updated successfully',
                'old_cost': old_cost,
                'new_cost': new_cost,
                'updated_by': request.user.username
            })
        except ValueError:
            return Response(
                {'error': 'Invalid cost format'},
                status=status.HTTP_400_BAD_REQUEST
            )
