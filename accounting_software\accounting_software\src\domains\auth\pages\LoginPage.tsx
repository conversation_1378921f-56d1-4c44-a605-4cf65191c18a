import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Formik } from 'formik';
import * as Yup from 'yup';
import { Box, Paper, Typography, Button, TextField } from '@mui/material';
import { useSnackbar } from 'notistack';
import { useAuth } from '../../../contexts/AuthContext';

const validationSchema = Yup.object({
  usernameOrEmail: Yup.string()
    .required('Username or Email is required'),
  password: Yup.string()
    .required('Password is required')
});

const LoginPage: React.FC = () => {
  const navigate = useNavigate();
  const { enqueueSnackbar } = useSnackbar();
  const { fetchUser } = useAuth();

  // No CSRF token needed for token authentication

  const handleLogin = async (values: { usernameOrEmail: string; password: string }) => {
    try {
      console.log('Attempting login...');
      const response = await fetch('http://localhost:8000/api-token-auth/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username: values.usernameOrEmail,
          password: values.password,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        console.error('Login failed:', errorData);
        const errorMessage = errorData.non_field_errors ? errorData.non_field_errors[0] : (errorData.error || 'Login failed');
        throw new Error(errorMessage);
      }

      const data = await response.json();
      console.log('Login successful, received token');
      
      localStorage.setItem('token', data.token);
      console.log('Token stored in localStorage');
      
      // Verify token is stored correctly
      const storedToken = localStorage.getItem('token');
      console.log('Verified token in localStorage:', storedToken ? 'Token exists' : 'No token found');
      
      // Refresh the auth context with user data
      console.log('Fetching user data...');
      await fetchUser();
      console.log('User data fetched successfully');
      
      enqueueSnackbar('Login successful', { variant: 'success' });
      navigate('/dashboard');
    } catch (error) {
      console.error('Login error:', error);
      enqueueSnackbar(error instanceof Error ? error.message : 'Login failed', { 
        variant: 'error' 
      });
    }
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        bgcolor: 'background.default',
      }}
    >
      <Paper
        elevation={3}
        sx={{
          p: 4,
          width: '100%',
          maxWidth: 400,
        }}
      >
        <Typography variant="h4" align="center" gutterBottom>
          Login
        </Typography>
        
        <Formik
          initialValues={{
            usernameOrEmail: '',
            password: '',
          }}
          validationSchema={validationSchema}
          onSubmit={handleLogin}
        >
          {({
            values,
            errors,
            touched,
            handleChange,
            handleBlur,
            handleSubmit,
            isSubmitting,
          }) => (
            <form onSubmit={handleSubmit}>
              <Box sx={{ mb: 2 }}>
                <TextField
                  fullWidth
                  id="usernameOrEmail"
                  name="usernameOrEmail"
                  label="Username or Email"
                  value={values.usernameOrEmail}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  error={touched.usernameOrEmail && Boolean(errors.usernameOrEmail)}
                  helperText={touched.usernameOrEmail && errors.usernameOrEmail}
                />
              </Box>

              <Box sx={{ mb: 3 }}>
                <TextField
                  fullWidth
                  id="password"
                  name="password"
                  label="Password"
                  type="password"
                  value={values.password}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  error={touched.password && Boolean(errors.password)}
                  helperText={touched.password && errors.password}
                />
              </Box>

              <Button
                fullWidth
                type="submit"
                variant="contained"
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Logging in...' : 'Login'}
              </Button>
            </form>
          )}
        </Formik>
      </Paper>
    </Box>
  );
};

export default LoginPage;