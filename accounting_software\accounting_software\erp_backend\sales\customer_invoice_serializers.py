"""
Customer Invoice Serializers - Sales Module
Replica of Vendor Bill serializers but for Accounts Receivable
"""

from rest_framework import serializers
from contacts.models import Contact
from .models import CustomerInvoice, CustomerInvoiceItem


class CustomerInvoiceCustomerSerializer(serializers.ModelSerializer):
    """Nested serializer for customer details in invoices"""
    
    class Meta:
        model = Contact
        fields = ['id', 'display_name', 'email', 'phone', 'address']
        read_only_fields = ['id']


class CustomerInvoiceItemSerializer(serializers.ModelSerializer):
    """Serializer for customer invoice line items"""
    
    # Read-only fields for display
    product_name = serializers.CharField(source='product.name', read_only=True)
    product_sku = serializers.CharField(source='product.sku', read_only=True)
    
    class Meta:
        model = CustomerInvoiceItem
        fields = [
            'id', 'product', 'product_name', 'product_sku', 'item_description',
            'quantity', 'unit_price', 'discount_percent', 'line_total',
            'tax_rate', 'tax_amount', 'account_code', 'line_order'
        ]
        read_only_fields = ['line_total', 'tax_amount']


class CustomerInvoiceSerializer(serializers.ModelSerializer):
    """Serializer for customer invoices with comprehensive ERP features"""
    customer_details = CustomerInvoiceCustomerSerializer(source='customer', read_only=True)
    line_items = CustomerInvoiceItemSerializer(many=True, required=False)

    # Document link details
    sales_order_details = serializers.SerializerMethodField()
    delivery_note_details = serializers.SerializerMethodField()

    class Meta:
        model = CustomerInvoice
        fields = [
            'id', 'invoice_number', 'customer', 'customer_details', 'invoice_date', 'due_date',
            'sales_order', 'sales_order_details', 'delivery_note', 'delivery_note_details',
            'status', 'payment_terms', 'line_items', 'subtotal', 'discount_amount', 'shipping_amount',
            'tax_amount', 'total_amount', 'amount_paid', 'balance_due', 'reference_number', 'notes',
            'terms_and_conditions', 'created_at', 'updated_at'
        ]
        read_only_fields = ['invoice_number', 'subtotal', 'tax_amount', 'total_amount', 'balance_due']

    def get_sales_order_details(self, obj):
        if obj.sales_order:
            return {
                'so_number': obj.sales_order.so_number,
                'so_date': obj.sales_order.so_date,
                'total_amount': obj.sales_order.total_amount
            }
        return None

    def get_delivery_note_details(self, obj):
        if obj.delivery_note:
            return {
                'dn_number': obj.delivery_note.dn_number,
                'delivery_date': obj.delivery_note.delivery_date,
                'total_quantity': obj.delivery_note.total_quantity
            }
        return None

    def validate_line_items(self, value):
        """Simple validation for line items"""
        if not value:
            return value
        return value

    def create(self, validated_data):
        line_items_data = validated_data.pop('line_items', [])
        validated_data['created_by'] = self.context['request'].user
        
        invoice = CustomerInvoice.objects.create(**validated_data)
        
        for line_item_data in line_items_data:
            CustomerInvoiceItem.objects.create(invoice=invoice, **line_item_data)
        
        # Recalculate totals
        self._calculate_invoice_totals(invoice)
        
        return invoice

    def update(self, instance, validated_data):
        line_items_data = validated_data.pop('line_items', [])
        
        # Update invoice fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        
        # Update line items
        if line_items_data:
            # Clear existing line items
            instance.line_items.all().delete()
            
            # Create new line items
            for line_item_data in line_items_data:
                CustomerInvoiceItem.objects.create(invoice=instance, **line_item_data)
        
        # Recalculate totals
        self._calculate_invoice_totals(instance)
        
        return instance

    def _calculate_invoice_totals(self, invoice):
        """Calculate and update invoice totals"""
        line_items = invoice.line_items.all()
        
        subtotal = sum(item.line_total for item in line_items)
        tax_amount = sum(item.tax_amount for item in line_items)
        
        invoice.subtotal = subtotal
        invoice.tax_amount = tax_amount
        invoice.total_amount = subtotal + tax_amount + invoice.discount_amount + invoice.shipping_amount
        invoice.balance_due = invoice.total_amount - invoice.amount_paid
        
        invoice.save()
