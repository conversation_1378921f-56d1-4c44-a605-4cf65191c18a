#!/usr/bin/env python3
"""
Minimal test for vendor bill creation
"""
import requests
import json
from datetime import datetime, timedelta

# Configuration
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api"
TOKEN = "e15bc01f831c5111f413f534ef82288744cb7d41"

# Headers with authentication
HEADERS = {
    'Content-Type': 'application/json',
    'Authorization': f'Token {TOKEN}'
}

def test_minimal_vendor_bill():
    """Test creating a minimal vendor bill"""
    print("Testing minimal vendor bill creation...")
    
    # First, get a valid vendor ID
    try:
        vendors_response = requests.get(f"{API_BASE}/contacts/vendors/", headers=HEADERS, timeout=5)
        if vendors_response.status_code == 200:
            vendors_data = vendors_response.json()
            if vendors_data.get('results'):
                vendor_id = vendors_data['results'][0]['contact']
                print(f"Using vendor ID: {vendor_id}")
            else:
                print("❌ No vendors found")
                return
        else:
            print(f"❌ Failed to get vendors: {vendors_response.status_code}")
            return
    except Exception as e:
        print(f"❌ Error getting vendors: {e}")
        return
    
    # Test with line items
    bill_data_with_items = {
        "vendor": vendor_id,
        "bill_date": "2025-07-06",
        "due_date": "2025-08-05",
        "status": "draft",
        "payment_terms": "Net 30",
        "reference_number": "TEST-001",
        "notes": "Test bill with line items",
        "line_items": [
            {
                "item_description": "Test Product 1",
                "quantity": 10,
                "unit_price": 100.00,
                "tax_rate": 10.0,
                "account_code": "5010-COGS",
                "line_order": 1
            },
            {
                "item_description": "Test Service 1",
                "quantity": 5,
                "unit_price": 200.00,
                "tax_rate": 10.0,
                "account_code": "5020-Services",
                "line_order": 2
            }
        ]
    }
    
    print("Sending bill data with line items:")
    print(json.dumps(bill_data_with_items, indent=2))

    try:
        url = f"{API_BASE}/purchase/vendor-bills/"
        print(f"Making request to: {url}")
        response = requests.post(url, json=bill_data_with_items, headers=HEADERS, timeout=10)
        
        print(f"\nStatus Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        if response.status_code == 201:
            print("✅ SUCCESS - Minimal vendor bill created!")
            response_data = response.json()
            print("Response data:")
            print(json.dumps(response_data, indent=2))
        else:
            print("❌ FAILED")
            print("Response text:", response.text[:500])
            try:
                error_data = response.json()
                print("Error details:")
                print(json.dumps(error_data, indent=2))
            except:
                print("Could not parse error as JSON")
                
    except requests.exceptions.Timeout:
        print("❌ Request timed out")
    except requests.exceptions.ConnectionError:
        print("❌ Connection error - is the server running?")
    except Exception as e:
        print(f"❌ Exception: {e}")

if __name__ == "__main__":
    print("🚀 MINIMAL VENDOR BILL TEST")
    print("=" * 40)
    test_minimal_vendor_bill()
