import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import {
  Box,
  Paper,
  Typography,
  Grid,
  Button,
  Alert,
  Snackbar,
  Card,
  CardContent,
  Chip,
  CircularProgress,
  Backdrop,
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  CheckCircle as CheckCircleIcon,
} from '@mui/icons-material';
import dayjs from 'dayjs';

// Import services
import { customerInvoiceService, type BillableSalesOrder } from '../../../services/customer-invoice.service';
import { formatCurrency } from '../../../shared/utils/formatters';

interface SnackbarState {
  open: boolean;
  message: string;
  severity: 'success' | 'error' | 'warning' | 'info';
}

const CreateCustomerInvoiceFromSOPage: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  // Get SO ID from URL params
  const soId = searchParams.get('so_id');

  // State
  const [loading, setLoading] = useState(false);
  const [soLoading, setSOLoading] = useState(true);
  const [selectedSO, setSelectedSO] = useState<BillableSalesOrder | null>(null);
  const [availableSOs, setAvailableSOs] = useState<BillableSalesOrder[]>([]);
  const [showSOSelection, setShowSOSelection] = useState(!soId);
  const [snackbar, setSnackbar] = useState<SnackbarState>({
    open: false,
    message: '',
    severity: 'success',
  });

  // Load available SOs for selection
  const loadAvailableSOs = async () => {
    try {
      // Get service SOs that are billable (no existing invoices)
      const serviceSOs = await customerInvoiceService.getBillableSalesOrders();
      setAvailableSOs(serviceSOs);
    } catch (error) {
      console.error('Failed to load billable service SOs:', error);
      setSnackbar({
        open: true,
        message: 'Failed to load billable service Sales Orders',
        severity: 'error',
      });
    }
  };

  // Load specific SO
  const loadSalesOrder = async (soId: string) => {
    try {
      setSOLoading(true);
      // Find the SO in available SOs
      const so = availableSOs.find(so => so.id === parseInt(soId));
      if (so) {
        setSelectedSO(so);
        setShowSOSelection(false);
      } else {
        // Load all SOs first, then find the specific one
        await loadAvailableSOs();
        const foundSO = availableSOs.find(so => so.id === parseInt(soId));
        if (foundSO) {
          setSelectedSO(foundSO);
          setShowSOSelection(false);
        } else {
          throw new Error('Sales Order not found or not billable');
        }
      }
    } catch (error) {
      console.error('Failed to load SO:', error);
      setSnackbar({
        open: true,
        message: 'Failed to load Sales Order',
        severity: 'error',
      });
    } finally {
      setSOLoading(false);
    }
  };

  // Load initial data
  useEffect(() => {
    if (soId) {
      loadSalesOrder(soId);
    } else {
      loadAvailableSOs();
      setSOLoading(false);
    }
  }, [soId]);

  // Handle SO selection
  const handleSOSelection = (so: BillableSalesOrder) => {
    setSelectedSO(so);
    setShowSOSelection(false);
    setSnackbar({
      open: true,
      message: `Sales Order ${so.so_number} selected successfully!`,
      severity: 'success',
    });
  };

  // Create customer invoice from SO
  const handleCreateFromSO = async () => {
    if (!selectedSO) {
      setSnackbar({
        open: true,
        message: 'No Sales Order selected',
        severity: 'error',
      });
      return;
    }

    try {
      setLoading(true);
      const createdInvoice = await customerInvoiceService.createCustomerInvoiceFromSalesOrder(selectedSO.id);

      setSnackbar({
        open: true,
        message: `Customer invoice ${createdInvoice.invoice_number} created successfully!`,
        severity: 'success',
      });

      // Navigate to the created invoice
      setTimeout(() => {
        navigate(`/dashboard/sales/customer-invoices/${createdInvoice.id}/edit`);
      }, 1500);

    } catch (error) {
      console.error('Failed to create customer invoice:', error);
      setSnackbar({
        open: true,
        message: `Failed to create customer invoice: ${error instanceof Error ? error.message : 'Unknown error'}`,
        severity: 'error',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCloseSnackbar = () => {
    setSnackbar(prev => ({ ...prev, open: false }));
  };

  // Loading state
  if (soLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress />
        <Typography sx={{ ml: 2 }}>Loading...</Typography>
      </Box>
    );
  }

  // SO Selection Interface
  if (showSOSelection) {
    return (
      <Box sx={{ minHeight: '100vh', bgcolor: '#f5f5f5' }}>
        <Backdrop open={loading} sx={{ zIndex: 9999 }}>
          <CircularProgress color="primary" />
        </Backdrop>

        {/* Header */}
        <Box sx={{ bgcolor: 'white', borderBottom: 1, borderColor: 'divider', p: 3 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Box>
              <Typography variant="h4" component="h1" sx={{ fontWeight: 600, color: '#1a1a1a' }}>
                Select Service Sales Order
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
                Choose a service Sales Order to create customer invoice from. Only unbilled service SOs are shown.
              </Typography>
            </Box>
            <Button
              variant="outlined"
              startIcon={<ArrowBackIcon />}
              onClick={() => navigate('/dashboard/sales/customer-invoices')}
              sx={{ borderRadius: '8px' }}
            >
              Back to Customer Invoices
            </Button>
          </Box>
        </Box>

        {/* SO Selection Content */}
        <Box sx={{ p: 3 }}>
          <Grid container spacing={3}>
            {availableSOs.length === 0 ? (
              <Grid item xs={12}>
                <Paper sx={{ p: 4, textAlign: 'center' }}>
                  <Typography variant="h6" color="text.secondary" sx={{ mb: 2 }}>
                    No Service Sales Orders Available
                  </Typography>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                    There are no service Sales Orders available for invoice creation. Only service SOs that haven't been invoiced yet will appear here.
                  </Typography>
                  <Button
                    variant="contained"
                    onClick={() => navigate('/dashboard/sales/orders')}
                    sx={{ borderRadius: '8px' }}
                  >
                    View Sales Orders
                  </Button>
                </Paper>
              </Grid>
            ) : (
              availableSOs.map((so) => (
                <Grid item xs={12} md={6} lg={4} key={so.id}>
                  <Card 
                    sx={{ 
                      cursor: 'pointer',
                      transition: 'all 0.2s',
                      '&:hover': {
                        transform: 'translateY(-2px)',
                        boxShadow: 4,
                      }
                    }}
                    onClick={() => handleSOSelection(so)}
                  >
                    <CardContent>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                        <Typography variant="h6" sx={{ fontWeight: 600 }}>
                          {so.so_number}
                        </Typography>
                        <Chip
                          label={so.status}
                          size="small"
                          color={so.status === 'completed' ? 'success' : 'primary'}
                          sx={{ textTransform: 'capitalize' }}
                        />
                      </Box>
                      
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                        <strong>Customer:</strong> {so.customer_name}
                      </Typography>
                      
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                        <strong>Date:</strong> {dayjs(so.so_date).format('DD/MM/YYYY')}
                      </Typography>
                      
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                        <strong>Total:</strong> {formatCurrency(so.total_amount, '$')}
                      </Typography>
                      
                      <Button
                        variant="contained"
                        fullWidth
                        size="small"
                        sx={{ borderRadius: '6px' }}
                      >
                        Create Invoice from this SO
                      </Button>
                    </CardContent>
                  </Card>
                </Grid>
              ))
            )}
          </Grid>
        </Box>

        {/* Snackbar */}
        <Snackbar
          open={snackbar.open}
          autoHideDuration={6000}
          onClose={handleCloseSnackbar}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        >
          <Alert onClose={handleCloseSnackbar} severity={snackbar.severity} sx={{ width: '100%' }}>
            {snackbar.message}
          </Alert>
        </Snackbar>
      </Box>
    );
  }

  // SO not found
  if (!selectedSO) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">
          Sales Order not found. Please check the SO ID and try again.
        </Alert>
        <Button
          startIcon={<ArrowBackIcon />}
          onClick={() => navigate('/dashboard/sales/customer-invoices')}
          sx={{ mt: 2 }}
        >
          Back to Customer Invoices
        </Button>
      </Box>
    );
  }
