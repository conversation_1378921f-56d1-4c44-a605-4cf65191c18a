#!/usr/bin/env python3

import requests
import json

# Configuration
BASE_URL = "http://localhost:8000/api"
USERNAME = "admin"  # Replace with your admin username
PASSWORD = "admin123"  # Replace with your admin password

def get_auth_token():
    """Get authentication token"""
    url = f"{BASE_URL}/../api-token-auth/"
    data = {
        "username": USERNAME,
        "password": PASSWORD
    }
    
    try:
        response = requests.post(url, json=data)
        if response.status_code == 200:
            token = response.json().get('token')
            print(f"✅ Authentication successful. Token: {token[:20]}...")
            return token
        else:
            print(f"❌ Authentication failed: {response.status_code}")
            print(f"Response: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Authentication error: {e}")
        return None

def test_billable_service_pos(token):
    """Test the billable service POs endpoint"""
    url = f"{BASE_URL}/purchase/purchase-orders/billable_service_pos/"
    headers = {
        "Authorization": f"Token {token}",
        "Content-Type": "application/json"
    }
    
    try:
        print(f"\n🔍 Testing endpoint: {url}")
        response = requests.get(url, headers=headers)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Success! Found {len(data)} billable service POs")
            
            for i, po in enumerate(data):
                print(f"\n📋 PO {i+1}:")
                print(f"  - PO Number: {po.get('po_number')}")
                print(f"  - Vendor: {po.get('vendor_name')}")
                print(f"  - Status: {po.get('status')}")
                print(f"  - Total: {po.get('total_amount')}")
                print(f"  - Line Items: {len(po.get('line_items', []))}")
                
                # Show line items
                for j, item in enumerate(po.get('line_items', [])):
                    print(f"    Item {j+1}: {item.get('description')} (Product: {item.get('product')})")
            
            return data
        else:
            print(f"❌ Request failed: {response.status_code}")
            print(f"Response: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Request error: {e}")
        return None

def test_all_pos(token):
    """Test getting all POs for comparison"""
    url = f"{BASE_URL}/purchase/purchase-orders/"
    headers = {
        "Authorization": f"Token {token}",
        "Content-Type": "application/json"
    }
    
    try:
        print(f"\n🔍 Testing all POs endpoint: {url}")
        response = requests.get(url, headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            pos = data.get('results', data) if isinstance(data, dict) else data
            print(f"✅ Found {len(pos)} total POs")
            
            for i, po in enumerate(pos):
                print(f"\n📋 PO {i+1}:")
                print(f"  - PO Number: {po.get('po_number')}")
                print(f"  - Status: {po.get('status')}")
                print(f"  - Line Items: {len(po.get('line_items', []))}")
                
                # Check product types in line items
                for j, item in enumerate(po.get('line_items', [])):
                    product_info = "No product linked"
                    if item.get('product'):
                        product_info = f"Product ID: {item.get('product')}"
                    print(f"    Item {j+1}: {item.get('description')} ({product_info})")
            
            return pos
        else:
            print(f"❌ Request failed: {response.status_code}")
            print(f"Response: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Request error: {e}")
        return None

if __name__ == "__main__":
    print("🚀 Testing Service PO Billing Endpoint")
    print("=" * 50)
    
    # Get authentication token
    token = get_auth_token()
    if not token:
        exit(1)
    
    # Test all POs first
    print("\n" + "=" * 50)
    print("📋 ALL PURCHASE ORDERS")
    print("=" * 50)
    all_pos = test_all_pos(token)
    
    # Test billable service POs
    print("\n" + "=" * 50)
    print("🎯 BILLABLE SERVICE POS")
    print("=" * 50)
    service_pos = test_billable_service_pos(token)
    
    print("\n" + "=" * 50)
    print("📊 SUMMARY")
    print("=" * 50)
    if all_pos and service_pos is not None:
        print(f"Total POs: {len(all_pos)}")
        print(f"Billable Service POs: {len(service_pos)}")
        print(f"Filtered out: {len(all_pos) - len(service_pos)}")
    
    print("\n✅ Test completed!")
